# 🎫 抖音券码退款补码场景解决方案

> **版本**: v1.0  
> **创建时间**: 2025-01-23  
> **场景**: 处理订单已发券但抖音传入codes为空的退款请求  

---

## 🎯 业务场景描述

### 补码场景触发条件
```java
order.getOrderStatus().equals(DouyinPreOrder.ORDER_STATUS_COUPON_ISSUED) 
&& CollectionUtils.isEmpty(codes)
```

**场景说明**:
- **订单状态**: 已发券 (`ORDER_STATUS_COUPON_ISSUED`)
- **抖音入参**: `certificates`中的`codes`列表为空
- **业务需求**: 查询券码表，如有未使用券码则拒绝退款并返回补码信息

---

## 🔍 当前问题分析

### 现有代码缺陷
```java
// ❌ 问题代码：直接拒绝，未查询券码表
if(order.getOrderStatus().equals(DouyinPreOrder.ORDER_STATUS_COUPON_ISSUED) && CollectionUtils.isEmpty(codes)) {
    DouyinRefundAudit refundAudit = new DouyinRefundAudit();
    refundAudit.setAuditStatus("REJECTED");
    refundAudit.setAuditReason("券码已发放");
    // 缺少：1.券码查询 2.状态检查 3.补码信息
    Map<String, Object> responseData = buildRefundResponseWithCodes(refundAudit, requestBody);
    return buildSuccessResponse(responseData);
}
```

### 关键缺失功能
1. **券码状态查询** → 未根据orderId查询DouyinCoupon表
2. **未使用券码筛选** → 未检查券码的实际使用状态  
3. **补码信息构建** → 响应中缺少券码补码数据
4. **场景区分处理** → 未区分有无未使用券码的不同处理逻辑

---

## 🛠️ 完整解决方案

### 1. 补码场景处理逻辑

```java
/**
 * 处理补码场景的退款申请
 * 
 * @param order 订单信息
 * @param codes 抖音传入的券码列表（为空触发补码场景）
 * @param requestBody 原始请求体
 * @param cacheKey 缓存键
 * @return 处理结果
 */
private Map<String, Object> handleSupplementCodeRefund(
        DouyinPreOrder order, 
        List<String> codes, 
        Map<String, Object> requestBody,
        String cacheKey) {
    
    String orderId = order.getPreOrderId();
    log.info("处理补码场景退款申请，orderId={}", orderId);
    
    // 1. 查询该订单的所有券码
    List<DouyinCoupon> orderCoupons = douyinCouponMapper.selectByOrderId(orderId);
    if (CollectionUtils.isEmpty(orderCoupons)) {
        log.warn("订单状态为已发券但数据库中无券码记录，orderId={}", orderId);
        return buildErrorResponse(300, "数据异常：订单已发券但找不到券码记录");
    }
    
    // 2. 筛选未使用的券码
    List<DouyinCoupon> unusedCoupons = orderCoupons.stream()
        .filter(coupon -> DouyinCoupon.STATUS_UNUSED.equals(coupon.getCouponStatus()) 
                       || DouyinCoupon.STATUS_ISSUED.equals(coupon.getCouponStatus()))
        .collect(Collectors.toList());
    
    log.info("订单券码统计，orderId={}, 总券码数={}, 未使用券码数={}", 
            orderId, orderCoupons.size(), unusedCoupons.size());
    
    // 3. 如果有未使用券码，拒绝退款并返回补码信息
    if (!CollectionUtils.isEmpty(unusedCoupons)) {
        return handleRefundRejectionWithSupplementCodes(unusedCoupons, requestBody, cacheKey);
    }
    
    // 4. 如果所有券码都已使用，允许正常退款
    log.info("所有券码均已使用，允许退款，orderId={}", orderId);
    return null; // 返回null表示继续正常退款流程
}

/**
 * 处理拒绝退款并返回补码信息
 */
private Map<String, Object> handleRefundRejectionWithSupplementCodes(
        List<DouyinCoupon> unusedCoupons,
        Map<String, Object> requestBody,
        String cacheKey) {
    
    // 1. 创建拒绝退款审核记录
    DouyinRefundAudit refundAudit = new DouyinRefundAudit();
    refundAudit.setRefundId(generateRefundId());
    refundAudit.setDouyinOrderId(getStringValue(requestBody, "order_id"));
    refundAudit.setRefundReason("补码场景：存在未使用券码");
    refundAudit.setAuditStatus("REJECTED");
    refundAudit.setAuditReason("存在未使用券码，需要补码处理");
    refundAudit.setAuditTime(new Date());
    refundAudit.setAuditorId("AUTO_SUPPLEMENT_SYSTEM");
    refundAudit.setCallbackStatus(DouyinRefundAudit.CALLBACK_NOT_REQUIRED);
    refundAudit.setRequestPayloadJson(JSON.toJSONString(requestBody, SerializerFeature.WriteMapNullValue));
    refundAudit.setCreateBy("DOUYIN_PRO_SPI_SUPPLEMENT");
    
    // 2. 保存审核记录
    douyinRefundAuditMapper.insert(refundAudit);
    
    // 3. 构建包含补码信息的响应
    Map<String, Object> responseData = buildRefundResponseWithSupplementCodes(refundAudit, requestBody, unusedCoupons);
    Map<String, Object> response = buildSuccessResponse(responseData);
    
    // 4. 缓存响应结果
    redisUtil.setValue(cacheKey, JSON.toJSONString(response, SerializerFeature.WriteMapNullValue), 30 * 60L, TimeUnit.SECONDS);
    
    // 5. 记录补码处理日志
    recordOrderLog("REFUND_REJECT_SUPPLEMENT_CODE", refundAudit.getDouyinOrderId(), 
                  null, null, String.format("补码券数量: %d", unusedCoupons.size()));
    
    log.info("补码场景拒绝退款完成，orderId={}, 补码券数量={}", 
            refundAudit.getDouyinOrderId(), unusedCoupons.size());
    
    return response;
}
```

### 2. 补码响应构建方法

```java
/**
 * 构建包含补码信息的退款响应
 * 
 * @param refundAudit 退款审核记录
 * @param requestBody 原始请求体
 * @param supplementCoupons 需要补码的券码列表
 * @return 响应数据
 */
private Map<String, Object> buildRefundResponseWithSupplementCodes(
        DouyinRefundAudit refundAudit, 
        Map<String, Object> requestBody,
        List<DouyinCoupon> supplementCoupons) {
    
    Map<String, Object> responseData = new HashMap<>();
    
    // 基础审核信息
    responseData.put("audit_status", refundAudit.getAuditStatus());
    responseData.put("audit_reason", refundAudit.getAuditReason());
    responseData.put("refund_id", refundAudit.getRefundId());
    responseData.put("order_id", refundAudit.getDouyinOrderId());
    responseData.put("audit_time", refundAudit.getAuditTime());
    
    // 关键：构建补码信息
    List<Map<String, Object>> certificates = supplementCoupons.stream()
        .map(coupon -> {
            Map<String, Object> cert = new HashMap<>();
            cert.put("code", coupon.getCouponCode());
            cert.put("status", convertCouponStatusToString(coupon.getCouponStatus()));
            cert.put("issue_time", coupon.getCreateTime());
            cert.put("expire_time", coupon.getExpireTime());
            cert.put("coupon_value", coupon.getCouponValue());
            cert.put("min_consume", coupon.getMinConsume());
            return cert;
        })
        .collect(Collectors.toList());
    
    responseData.put("certificates", certificates);
    
    // 补码场景特殊标识
    responseData.put("is_supplement_scenario", true);
    responseData.put("supplement_count", supplementCoupons.size());
    responseData.put("supplement_reason", "订单已发券但退款请求中券码列表为空，系统自动补充券码信息");
    
    // 保留原始请求信息
    responseData.put("original_after_sale_id", getStringValue(requestBody, "after_sale_id"));
    
    return responseData;
}

/**
 * 券码状态转换为字符串
 */
private String convertCouponStatusToString(Integer status) {
    if (DouyinCoupon.STATUS_UNUSED.equals(status)) {
        return "unused";
    } else if (DouyinCoupon.STATUS_ISSUED.equals(status)) {
        return "issued";
    } else if (DouyinCoupon.STATUS_USED.equals(status)) {
        return "used";
    } else if (DouyinCoupon.STATUS_EXPIRED.equals(status)) {
        return "expired";
    }
    return "unknown";
}
```

### 3. 数据访问层支持

```java
/**
 * DouyinCouponMapper 新增方法
 */
public interface DouyinCouponMapper extends BaseMapper<DouyinCoupon> {
    
    /**
     * 根据订单ID查询券码列表
     * 
     * @param orderId 订单ID
     * @return 券码列表
     */
    @Select("SELECT * FROM douyin_coupon WHERE order_id = #{orderId} ORDER BY create_time ASC")
    List<DouyinCoupon> selectByOrderId(@Param("orderId") String orderId);
    
    /**
     * 根据订单ID查询未使用券码列表
     * 
     * @param orderId 订单ID
     * @return 未使用券码列表
     */
    @Select("SELECT * FROM douyin_coupon WHERE order_id = #{orderId} " +
            "AND coupon_status IN (#{unusedStatus}, #{issuedStatus}) " +
            "ORDER BY create_time ASC")
    List<DouyinCoupon> selectUnusedByOrderId(
        @Param("orderId") String orderId,
        @Param("unusedStatus") Integer unusedStatus,
        @Param("issuedStatus") Integer issuedStatus
    );
}
```

---

## 🔄 优化后的主流程集成

### 修改后的handleRefundApply方法关键部分

```java
@Override
@Transactional(rollbackFor = Exception.class)
public Map<String, Object> handleRefundApply(Map<String, Object> requestBody) {
    try {
        // ... 前置处理（参数校验、幂等性检查、验签等）...
        
        // 4. 查询订单
        DouyinPreOrder order = douyinPreOrderMapper.selectByPreOrderId(orderId);
        if (order == null) {
            return buildErrorResponse(300, "订单不存在，请重试");
        }

        List<Map<String, Object>> certificates = (List<Map<String, Object>>) requestBody.get("certificates");
        if (certificates == null) {
            return buildErrorResponse(300, "券码列表缺失");
        }

        List<String> codes = certificates.stream()
            .map(cert -> (String) cert.get("code"))
            .collect(Collectors.toList());

        // 5. 🎯 补码场景处理（关键修改点）
        if (order.getOrderStatus().equals(DouyinPreOrder.ORDER_STATUS_COUPON_ISSUED) 
            && CollectionUtils.isEmpty(codes)) {
            
            // 使用新的补码处理逻辑
            Map<String, Object> supplementResult = handleSupplementCodeRefund(order, codes, requestBody, cacheKey);
            if (supplementResult != null) {
                // 补码场景处理完成，直接返回结果
                return supplementResult;
            }
            // 如果返回null，表示所有券码已使用，继续正常退款流程
        }
        
        // 6. 正常场景：根据codes查询券码
        List<DouyinCoupon> coupons;
        if (CollectionUtils.isEmpty(codes)) {
            // codes为空但不是补码场景（订单状态不是已发券）
            coupons = new ArrayList<>();
        } else {
            coupons = douyinCouponMapper.selectByCouponCodes(codes);
        }
        
        // ... 后续正常退款处理逻辑 ...
        
    } catch (Exception e) {
        log.error("处理退款申请异常，orderId={}", orderId, e);
        return buildErrorResponse(300, "服务商系统处理异常，请重试: " + e.getMessage());
    }
}
```

---

## 📊 方案优势

### 业务完整性
- ✅ **状态检查完整** → 真实查询券码表验证状态
- ✅ **场景区分处理** → 区分有无未使用券码的不同逻辑  
- ✅ **补码信息完整** → 返回详细的券码补码数据

### 技术可靠性
- ✅ **事务一致性** → 补码场景也在事务范围内
- ✅ **缓存机制** → 补码响应同样支持缓存
- ✅ **日志跟踪** → 完整的补码处理日志记录

### 可维护性
- ✅ **职责分离** → 补码逻辑独立方法，易于测试
- ✅ **配置灵活** → 券码状态判断逻辑可配置
- ✅ **异常处理** → 完整的异常处理和回滚机制

---

## 🧪 测试用例设计

### 补码场景测试

```java
@Test
void testSupplementCodeRefund_WithUnusedCoupons_ShouldRejectWithCodes() {
    // Given: 订单已发券，codes为空，数据库有未使用券码
    DouyinPreOrder order = createIssuedOrder();
    List<DouyinCoupon> unusedCoupons = createUnusedCoupons(order.getPreOrderId(), 3);
    Map<String, Object> requestBody = createRefundRequest(order.getPreOrderId(), Collections.emptyList());
    
    // When: 调用退款处理
    Map<String, Object> result = douyinProSpiService.handleRefundApply(requestBody);
    
    // Then: 应该拒绝退款并返回补码信息
    assertThat(result.get("code")).isEqualTo(200);
    Map<String, Object> data = (Map<String, Object>) result.get("data");
    assertThat(data.get("audit_status")).isEqualTo("REJECTED");
    assertThat(data.get("is_supplement_scenario")).isEqualTo(true);
    
    List<Map<String, Object>> certificates = (List<Map<String, Object>>) data.get("certificates");
    assertThat(certificates).hasSize(3);
    assertThat(certificates.get(0).get("status")).isEqualTo("unused");
}

@Test
void testSupplementCodeRefund_WithAllUsedCoupons_ShouldAllowRefund() {
    // Given: 订单已发券，codes为空，所有券码都已使用
    DouyinPreOrder order = createIssuedOrder();
    List<DouyinCoupon> usedCoupons = createUsedCoupons(order.getPreOrderId(), 3);
    Map<String, Object> requestBody = createRefundRequest(order.getPreOrderId(), Collections.emptyList());
    
    // When: 调用退款处理
    Map<String, Object> result = douyinProSpiService.handleRefundApply(requestBody);
    
    // Then: 应该允许正常退款
    assertThat(result.get("code")).isEqualTo(200);
    Map<String, Object> data = (Map<String, Object>) result.get("data");
    assertThat(data.get("audit_status")).isEqualTo("APPROVED");
}
```

---

## 📝 总结

通过这个完整的解决方案，补码场景的处理逻辑变得：

1. **业务完整** → 真实查询券码状态，准确判断是否需要补码
2. **逻辑清晰** → 独立的补码处理方法，职责分离
3. **数据准确** → 返回详细的券码信息给抖音
4. **可维护** → 完整的日志、异常处理和测试用例

这样既满足了业务需求，又保持了代码的可读性和可维护性。
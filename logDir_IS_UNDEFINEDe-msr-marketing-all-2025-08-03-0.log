[2025-08-03 00:15:16:016] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 00:15:57:057] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 00:16:17:017] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 00:32:51:051] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 00:33:28:028] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 00:58:38:038] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 00:58:45:045] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 01:16:44:044] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 01:17:53:053] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 01:17:59:059] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 01:51:00:000] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 01:59:46:046] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 02:00:46:046] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 02:01:09:009] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 02:17:03:003] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 02:18:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 02:34:51:051] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 02:51:25:025] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:00:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:01:32:032] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:02:06:006] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:20:12:012] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:20:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:21:17:017] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:22:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:22:29:029] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:57:25:025] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:58:11:011] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:59:13:013] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 03:59:34:034] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:02:06:006] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:19:43:043] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:21:14:014] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:21:51:051] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:22:14:014] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:39:48:048] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:40:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:58:16:016] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:58:24:024] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 04:59:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:03:02:002] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:09:35:035] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:11:35:035] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:27:59:059] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:43:58:058] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:44:18:018] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 05:45:09:009] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:02:29:029] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:21:32:032] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:22:07:007] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:37:30:030] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:38:43:043] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:39:13:013] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:54:38:038] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 06:54:46:046] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:03:46:046] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:04:46:046] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:04:58:058] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:23:21:021] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:24:14:014] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:25:13:013] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:25:28:028] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:26:27:027] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:42:41:041] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 07:59:32:032] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 08:00:02:002] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 120 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 09:05:25:025] [e-msr-marketing] [] [WARN ] [Apollo-RemoteConfigRepository-1] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************** [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************** [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=**************]]]
[2025-08-03 09:40:53:053] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-08-03 09:40:54:054] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:643] : Successfully waited for workers to finish.
[2025-08-03 09:40:54:054] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-08-03 11:51:02:002] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:02:002] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 11:51:04:004] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 11:51:04:004] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 11:51:04:004] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 79ms. Found 0 repository interfaces.
[2025-08-03 11:51:05:005] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 11:51:09:009] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:51:13:013] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:51:17:017] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:51:17:017] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties]
[2025-08-03 11:51:21:021] [] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 32 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:51:25:025] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:51:25:025] [] [] [WARN ] [main] [AbstractConfigFile.java:55] : Init Apollo Config File failed - namespace: application.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties].
[2025-08-03 11:51:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$ac265015] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @82503ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 23678 ms
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 11:51:26:026] [] [] [WARN ] [main] [WebAppContext.java:533] : Failed startup of context o.s.b.w.e.j.JettyEmbeddedWebAppContext@3f3c5ecd{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.4239820508786755229.8080/],UNAVAILABLE}
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'servletEndpointRegistrar' defined in class path resource [org/springframework/boot/actuate/autoconfigure/endpoint/web/ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:253)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:227)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.callInitializers(ServletContextInitializerConfiguration.java:65)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.configure(ServletContextInitializerConfiguration.java:54)
	at org.eclipse.jetty.webapp.WebAppContext.configure(WebAppContext.java:498)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1402)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:821)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:276)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:407)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:106)
	at org.eclipse.jetty.server.Server.doStart(Server.java:371)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.initialize(JettyWebServer.java:108)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.<init>(JettyWebServer.java:86)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getJettyWebServer(JettyServletWebServerFactory.java:401)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getWebServer(JettyServletWebServerFactory.java:155)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:153)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:544)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.besttop.MarketingApplication.main(MarketingApplication.java:24)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:528)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1108)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBean(EndpointDiscoverer.java:143)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBeans(EndpointDiscoverer.java:133)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.discoverEndpoints(EndpointDiscoverer.java:122)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getEndpoints(EndpointDiscoverer.java:116)
	at org.springframework.boot.actuate.autoconfigure.endpoint.web.ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.servletEndpointRegistrar(ServletEndpointManagementContextConfiguration.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 44 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 64 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 78 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:226)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:400)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:617)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:605)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1242)
	at org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry(HealthEndpointConfiguration.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 79 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1803)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1503)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1467)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1386)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1245)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 106 common frames omitted
Caused by: org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineDriverClassName(DataSourceProperties.java:233)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineUsername(DataSourceProperties.java:327)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:45)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1799)
	... 120 common frames omitted
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@3f3c5ecd{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.4239820508786755229.8080/],UNAVAILABLE}
[2025-08-03 11:51:26:026] [] [] [WARN ] [main] [AbstractApplicationContext.java:558] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Jetty web server
[2025-08-03 11:51:27:027] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 11:51:27:027] [] [] [ERROR] [main] [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

[2025-08-03 11:53:43:043] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:53:43:043] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 80ms. Found 0 repository interfaces.
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 11:53:49:049] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:53:53:053] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:53:57:057] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:53:57:057] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties]
[2025-08-03 11:54:01:001] [] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 32 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:54:05:005] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:54:05:005] [] [] [WARN ] [main] [AbstractConfigFile.java:55] : Init Apollo Config File failed - namespace: application.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties].
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$cba77c64] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @82552ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 23526 ms
[2025-08-03 11:54:07:007] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 11:54:07:007] [] [] [WARN ] [main] [WebAppContext.java:533] : Failed startup of context o.s.b.w.e.j.JettyEmbeddedWebAppContext@1760e594{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.229552926135450342.8080/],UNAVAILABLE}
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'servletEndpointRegistrar' defined in class path resource [org/springframework/boot/actuate/autoconfigure/endpoint/web/ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:253)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:227)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.callInitializers(ServletContextInitializerConfiguration.java:65)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.configure(ServletContextInitializerConfiguration.java:54)
	at org.eclipse.jetty.webapp.WebAppContext.configure(WebAppContext.java:498)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1402)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:821)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:276)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:407)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:106)
	at org.eclipse.jetty.server.Server.doStart(Server.java:371)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.initialize(JettyWebServer.java:108)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.<init>(JettyWebServer.java:86)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getJettyWebServer(JettyServletWebServerFactory.java:401)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getWebServer(JettyServletWebServerFactory.java:155)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:153)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:544)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.besttop.MarketingApplication.main(MarketingApplication.java:24)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:528)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1108)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBean(EndpointDiscoverer.java:143)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBeans(EndpointDiscoverer.java:133)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.discoverEndpoints(EndpointDiscoverer.java:122)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getEndpoints(EndpointDiscoverer.java:116)
	at org.springframework.boot.actuate.autoconfigure.endpoint.web.ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.servletEndpointRegistrar(ServletEndpointManagementContextConfiguration.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 44 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 64 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 78 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:226)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:400)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:617)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:605)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1242)
	at org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry(HealthEndpointConfiguration.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 79 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1803)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1503)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1467)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1386)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1245)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 106 common frames omitted
Caused by: org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineDriverClassName(DataSourceProperties.java:233)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineUsername(DataSourceProperties.java:327)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:45)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1799)
	... 120 common frames omitted
[2025-08-03 11:54:07:007] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@1760e594{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.229552926135450342.8080/],UNAVAILABLE}
[2025-08-03 11:54:07:007] [] [] [WARN ] [main] [AbstractApplicationContext.java:558] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Jetty web server
[2025-08-03 11:54:07:007] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 11:54:07:007] [] [] [ERROR] [main] [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

[2025-08-03 11:57:13:013] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:14:014] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 80ms. Found 0 repository interfaces.
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 11:57:20:020] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:57:24:024] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:57:28:028] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:57:28:028] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties]
[2025-08-03 11:57:32:032] [] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 32 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:57:36:036] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: connect timed out]]
[2025-08-03 11:57:36:036] [] [] [WARN ] [main] [AbstractConfigFile.java:55] : Init Apollo Config File failed - namespace: application.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties].
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$af8238f9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @83067ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 23607 ms
[2025-08-03 11:57:38:038] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 11:57:38:038] [] [] [WARN ] [main] [WebAppContext.java:533] : Failed startup of context o.s.b.w.e.j.JettyEmbeddedWebAppContext@6115846e{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.4211203486625105817.8080/],UNAVAILABLE}
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'servletEndpointRegistrar' defined in class path resource [org/springframework/boot/actuate/autoconfigure/endpoint/web/ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:253)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:227)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.callInitializers(ServletContextInitializerConfiguration.java:65)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.configure(ServletContextInitializerConfiguration.java:54)
	at org.eclipse.jetty.webapp.WebAppContext.configure(WebAppContext.java:498)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1402)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:821)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:276)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:407)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:106)
	at org.eclipse.jetty.server.Server.doStart(Server.java:371)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.initialize(JettyWebServer.java:108)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.<init>(JettyWebServer.java:86)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getJettyWebServer(JettyServletWebServerFactory.java:401)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getWebServer(JettyServletWebServerFactory.java:155)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:153)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:544)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.besttop.MarketingApplication.main(MarketingApplication.java:24)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:528)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1108)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBean(EndpointDiscoverer.java:143)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBeans(EndpointDiscoverer.java:133)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.discoverEndpoints(EndpointDiscoverer.java:122)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getEndpoints(EndpointDiscoverer.java:116)
	at org.springframework.boot.actuate.autoconfigure.endpoint.web.ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.servletEndpointRegistrar(ServletEndpointManagementContextConfiguration.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 44 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 64 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 78 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:226)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:400)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:617)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:605)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1242)
	at org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry(HealthEndpointConfiguration.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 79 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1803)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1503)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1467)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1386)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1245)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 106 common frames omitted
Caused by: org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineDriverClassName(DataSourceProperties.java:233)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineUsername(DataSourceProperties.java:327)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:45)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1799)
	... 120 common frames omitted
[2025-08-03 11:57:38:038] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@6115846e{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.4211203486625105817.8080/],UNAVAILABLE}
[2025-08-03 11:57:38:038] [] [] [WARN ] [main] [AbstractApplicationContext.java:558] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Jetty web server
[2025-08-03 11:57:38:038] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 11:57:38:038] [] [] [ERROR] [main] [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

[2025-08-03 12:03:12:012] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:03:13:013] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 79ms. Found 0 repository interfaces.
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 12:03:24:024] [] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 32 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: Read timed out]]
[2025-08-03 12:03:36:036] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: java.io.IOException: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Server returned HTTP response code: 502 for URL: http://*************:38080/services/config?appId=EDP&ip=************]]]
[2025-08-03 12:03:48:048] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: Read timed out]]
[2025-08-03 12:04:00:000] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: Read timed out]]
[2025-08-03 12:04:00:000] [] [] [WARN ] [main] [AbstractConfigRepository.java:26] : Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties]
[2025-08-03 12:04:12:012] [] [] [WARN ] [Apollo-RemoteConfigLongPollService-1] [RemoteConfigLongPollService.java:193] : Long polling failed, will retry in 64 seconds. appId: EDP, cluster: k8s, namespaces: xbdq-uat.yml+e-msr-marketing.yml+application.yml, long polling url: null, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: Read timed out]]
[2025-08-03 12:04:24:024] [] [] [WARN ] [main] [LocalFileConfigRepository.java:167] : Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:38080/services/config?appId=EDP&ip=************ [Cause: Could not complete get operation [Cause: Read timed out]]
[2025-08-03 12:04:24:024] [] [] [WARN ] [main] [AbstractConfigFile.java:55] : Init Apollo Config File failed - namespace: application.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file /Users/<USER>/staryWay/edp/e-msr-marketing/target/classes/config-cache/EDP+k8s+application.yml.properties].
[2025-08-03 12:04:24:024] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$28da8a98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @243689ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 72379 ms
[2025-08-03 12:04:26:026] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 12:04:26:026] [] [] [WARN ] [main] [WebAppContext.java:533] : Failed startup of context o.s.b.w.e.j.JettyEmbeddedWebAppContext@5922cff3{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.6483916947883909620.8080/],UNAVAILABLE}
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'servletEndpointRegistrar' defined in class path resource [org/springframework/boot/actuate/autoconfigure/endpoint/web/ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:253)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:227)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.callInitializers(ServletContextInitializerConfiguration.java:65)
	at org.springframework.boot.web.embedded.jetty.ServletContextInitializerConfiguration.configure(ServletContextInitializerConfiguration.java:54)
	at org.eclipse.jetty.webapp.WebAppContext.configure(WebAppContext.java:498)
	at org.eclipse.jetty.webapp.WebAppContext.startContext(WebAppContext.java:1402)
	at org.eclipse.jetty.server.handler.ContextHandler.doStart(ContextHandler.java:821)
	at org.eclipse.jetty.servlet.ServletContextHandler.doStart(ServletContextHandler.java:276)
	at org.eclipse.jetty.webapp.WebAppContext.doStart(WebAppContext.java:524)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.start(ContainerLifeCycle.java:169)
	at org.eclipse.jetty.server.Server.start(Server.java:407)
	at org.eclipse.jetty.util.component.ContainerLifeCycle.doStart(ContainerLifeCycle.java:110)
	at org.eclipse.jetty.server.handler.AbstractHandler.doStart(AbstractHandler.java:106)
	at org.eclipse.jetty.server.Server.doStart(Server.java:371)
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:72)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.initialize(JettyWebServer.java:108)
	at org.springframework.boot.web.embedded.jetty.JettyWebServer.<init>(JettyWebServer.java:86)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getJettyWebServer(JettyServletWebServerFactory.java:401)
	at org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory.getWebServer(JettyServletWebServerFactory.java:155)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:153)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:544)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.besttop.MarketingApplication.main(MarketingApplication.java:24)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.ServletEndpointRegistrar]: Factory method 'servletEndpointRegistrar' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:528)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1108)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBean(EndpointDiscoverer.java:143)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.createEndpointBeans(EndpointDiscoverer.java:133)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.discoverEndpoints(EndpointDiscoverer.java:122)
	at org.springframework.boot.actuate.endpoint.annotation.EndpointDiscoverer.getEndpoints(EndpointDiscoverer.java:116)
	at org.springframework.boot.actuate.autoconfigure.endpoint.web.ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration.servletEndpointRegistrar(ServletEndpointManagementContextConfiguration.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 44 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:625)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 64 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:640)
	... 78 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:787)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:226)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:400)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:617)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:605)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1242)
	at org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration.healthContributorRegistry(HealthEndpointConfiguration.java:78)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 79 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1803)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1503)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1467)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1386)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1245)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:874)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:778)
	... 106 common frames omitted
Caused by: org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineDriverClassName(DataSourceProperties.java:233)
	at org.springframework.boot.autoconfigure.jdbc.DataSourceProperties.determineUsername(DataSourceProperties.java:327)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:45)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1799)
	... 120 common frames omitted
[2025-08-03 12:04:26:026] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@5922cff3{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.6483916947883909620.8080/],UNAVAILABLE}
[2025-08-03 12:04:26:026] [] [] [WARN ] [main] [AbstractApplicationContext.java:558] : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Jetty web server
[2025-08-03 12:04:26:026] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 12:04:26:026] [] [] [ERROR] [main] [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).


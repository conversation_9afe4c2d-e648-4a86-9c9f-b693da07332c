# 🔧 DouyinProSpiServiceImpl 代码结构优化方案

> **版本**: v1.0  
> **创建时间**: 2025-01-23  
> **目标**: 重构handleRefundApply方法，实现职责分离和代码复用

---

## 🎯 重构目标

### 当前问题
- **方法过长** → 120+行代码，职责过多
- **逻辑耦合** → 参数校验、业务处理、响应构建混合
- **异常处理简单** → 缺少分类异常处理
- **代码重复** → 响应构建、缓存操作重复

### 重构原则
- **单一职责** → 每个方法只负责一个核心功能
- **职责分离** → 校验、业务逻辑、响应构建分离
- **异常细化** → 不同类型异常的精确处理
- **代码复用** → 提取公共方法，消除重复

---

## 🏗️ 重构后的类结构

### 1. 主流程控制方法

```java
/**
 * 处理抖音券码退款申请 - 主流程控制
 * 职责：流程编排，异常统一处理
 */
@Override
@Transactional(rollbackFor = Exception.class)
public Map<String, Object> handleRefundApply(Map<String, Object> requestBody) {
    String orderId = null;
    try {
        // 1. 参数校验和预处理
        RefundRequestContext context = validateAndPrepareRequest(requestBody);
        orderId = context.getOrderId();
        
        // 2. 幂等性和重复申请检查
        Map<String, Object> cachedResult = checkIdempotencyAndDuplication(context);
        if (cachedResult != null) {
            return cachedResult;
        }
        
        // 3. 验签检查
        validateRequestSignature(requestBody);
        
        // 4. 查询和验证订单
        DouyinPreOrder order = queryAndValidateOrder(context.getOrderId());
        context.setOrder(order);
        
        // 5. 业务场景路由处理
        Map<String, Object> result = routeRefundScenario(context);
        
        // 6. 记录操作日志
        recordRefundOperationLog(context, result);
        
        return result;
        
    } catch (RefundValidationException e) {
        log.warn("退款申请参数校验失败: {}", e.getMessage());
        return buildErrorResponse(400, e.getMessage());
    } catch (RefundBusinessException e) {
        log.warn("退款申请业务异常: {}", e.getMessage());
        return buildErrorResponse(300, e.getMessage());
    } catch (Exception e) {
        log.error("退款申请系统异常，orderId={}", orderId, e);
        return buildErrorResponse(500, "服务商系统处理异常，请重试");
    }
}
```

### 2. 请求上下文对象

```java
/**
 * 退款请求处理上下文
 * 职责：封装请求处理过程中的所有状态信息
 */
@Data
@Builder
public class RefundRequestContext {
    // 基础请求信息
    private String afterSaleId;
    private String orderId;
    private String refundReason;
    private List<String> couponCodes;
    private Map<String, Object> originalRequest;
    
    // 业务对象
    private DouyinPreOrder order;
    private List<DouyinCoupon> coupons;
    private DouyinRefundAudit existingRefund;
    
    // 处理状态
    private String cacheKey;
    private RefundScenarioType scenarioType;
    private long startTime;
    
    // 场景枚举
    public enum RefundScenarioType {
        NORMAL_REFUND,           // 正常退款
        SUPPLEMENT_CODE,         // 补码场景
        ALREADY_PROCESSED,       // 已处理
        ORDER_NOT_ISSUED        // 订单未发券
    }
}
```

### 3. 参数校验和预处理

```java
/**
 * 参数校验和请求预处理
 * 职责：统一的参数校验逻辑
 */
private RefundRequestContext validateAndPrepareRequest(Map<String, Object> requestBody) {
    if (requestBody == null || requestBody.isEmpty()) {
        throw new RefundValidationException("请求体不能为空");
    }
    
    // 提取基础参数
    String afterSaleId = getStringValue(requestBody, "after_sale_id");
    String orderId = getStringValue(requestBody, "order_id");
    String refundReason = getStringValue(requestBody, "refund_reason");
    
    // 必填参数校验
    if (StringUtils.isEmpty(orderId)) {
        throw new RefundValidationException("订单ID不能为空");
    }
    
    // 提取券码列表
    List<Map<String, Object>> certificates = (List<Map<String, Object>>) requestBody.get("certificates");
    if (certificates == null) {
        throw new RefundValidationException("券码列表缺失");
    }
    
    List<String> couponCodes = certificates.stream()
        .map(cert -> (String) cert.get("code"))
        .filter(StringUtils::isNotBlank)
        .collect(Collectors.toList());
    
    // 构建上下文
    return RefundRequestContext.builder()
        .afterSaleId(afterSaleId)
        .orderId(orderId)
        .refundReason(refundReason)
        .couponCodes(couponCodes)
        .originalRequest(requestBody)
        .cacheKey("erp:douyin:pro:refund_apply:" + orderId)
        .startTime(System.currentTimeMillis())
        .build();
}
```

### 4. 幂等性和重复检查

```java
/**
 * 幂等性和重复申请检查
 * 职责：缓存检查和已存在退款记录检查
 */
private Map<String, Object> checkIdempotencyAndDuplication(RefundRequestContext context) {
    // 1. 缓存幂等性检查
    Object cachedResponse = redisUtil.getValue(context.getCacheKey());
    if (cachedResponse != null) {
        log.info("从缓存获取退款申请响应，orderId={}", context.getOrderId());
        return JSON.parseObject(cachedResponse.toString(), Map.class);
    }
    
    // 2. 数据库重复申请检查
    DouyinRefundAudit existingRefund = douyinRefundAuditMapper.selectByRefundId(context.getOrderId());
    if (existingRefund != null) {
        context.setExistingRefund(existingRefund);
        Map<String, Object> response = buildRefundResponse(existingRefund, context);
        cacheResponse(context.getCacheKey(), response);
        return response;
    }
    
    return null; // 无缓存，继续处理
}
```

### 5. 业务场景路由

```java
/**
 * 业务场景路由处理
 * 职责：根据订单状态和券码情况路由到不同的处理逻辑
 */
private Map<String, Object> routeRefundScenario(RefundRequestContext context) {
    // 确定处理场景
    RefundScenarioType scenarioType = determineRefundScenario(context);
    context.setScenarioType(scenarioType);
    
    log.info("退款场景路由，orderId={}, scenario={}", context.getOrderId(), scenarioType);
    
    // 根据场景路由处理
    switch (scenarioType) {
        case SUPPLEMENT_CODE:
            return handleSupplementCodeScenario(context);
            
        case NORMAL_REFUND:
            return handleNormalRefundScenario(context);
            
        case ORDER_NOT_ISSUED:
            return handleOrderNotIssuedScenario(context);
            
        default:
            throw new RefundBusinessException("未知的退款处理场景: " + scenarioType);
    }
}

/**
 * 确定退款处理场景
 */
private RefundScenarioType determineRefundScenario(RefundRequestContext context) {
    DouyinPreOrder order = context.getOrder();
    List<String> codes = context.getCouponCodes();
    
    // 补码场景：订单已发券但codes为空
    if (DouyinPreOrder.ORDER_STATUS_COUPON_ISSUED.equals(order.getOrderStatus()) 
        && CollectionUtils.isEmpty(codes)) {
        return RefundScenarioType.SUPPLEMENT_CODE;
    }
    
    // 订单未发券场景
    if (!DouyinPreOrder.ORDER_STATUS_COUPON_ISSUED.equals(order.getOrderStatus())) {
        return RefundScenarioType.ORDER_NOT_ISSUED;
    }
    
    // 正常退款场景
    return RefundScenarioType.NORMAL_REFUND;
}
```

### 6. 补码场景处理

```java
/**
 * 补码场景处理
 * 职责：处理订单已发券但codes为空的情况
 */
private Map<String, Object> handleSupplementCodeScenario(RefundRequestContext context) {
    log.info("处理补码场景，orderId={}", context.getOrderId());
    
    // 1. 查询订单的所有券码
    List<DouyinCoupon> orderCoupons = douyinCouponMapper.selectByOrderId(context.getOrderId());
    if (CollectionUtils.isEmpty(orderCoupons)) {
        throw new RefundBusinessException("数据异常：订单已发券但找不到券码记录");
    }
    
    // 2. 筛选未使用的券码
    List<DouyinCoupon> unusedCoupons = orderCoupons.stream()
        .filter(this::isCouponUnused)
        .collect(Collectors.toList());
    
    log.info("券码统计，orderId={}, 总数={}, 未使用数={}", 
            context.getOrderId(), orderCoupons.size(), unusedCoupons.size());
    
    // 3. 如果有未使用券码，拒绝退款并返回补码信息
    if (!CollectionUtils.isEmpty(unusedCoupons)) {
        return processSupplementCodeRejection(context, unusedCoupons);
    }
    
    // 4. 所有券码已使用，转为正常退款处理
    log.info("所有券码均已使用，转为正常退款，orderId={}", context.getOrderId());
    context.setScenarioType(RefundScenarioType.NORMAL_REFUND);
    return handleNormalRefundScenario(context);
}

/**
 * 检查券码是否未使用
 */
private boolean isCouponUnused(DouyinCoupon coupon) {
    return DouyinCoupon.STATUS_UNUSED.equals(coupon.getCouponStatus()) 
        || DouyinCoupon.STATUS_ISSUED.equals(coupon.getCouponStatus());
}
```

### 7. 正常退款场景处理

```java
/**
 * 正常退款场景处理
 * 职责：处理标准的退款申请流程
 */
private Map<String, Object> handleNormalRefundScenario(RefundRequestContext context) {
    log.info("处理正常退款场景，orderId={}", context.getOrderId());
    
    // 1. 查询券码信息
    List<DouyinCoupon> coupons = queryCouponsForRefund(context);
    context.setCoupons(coupons);
    
    // 2. 更新券码状态为退款中
    updateCouponsForRefund(coupons);
    
    // 3. 创建退款审核记录
    DouyinRefundAudit refundAudit = createRefundAudit(context);
    
    // 4. 更新订单状态
    updateOrderForRefund(context.getOrder());
    
    // 5. 构建响应
    Map<String, Object> response = buildRefundResponse(refundAudit, context);
    cacheResponse(context.getCacheKey(), response);
    
    return response;
}
```

### 8. 响应构建统一化

```java
/**
 * 统一的响应构建器
 * 职责：根据不同场景构建标准化响应
 */
private Map<String, Object> buildRefundResponse(DouyinRefundAudit refundAudit, RefundRequestContext context) {
    RefundResponseBuilder builder = RefundResponseBuilder.create()
        .withRefundAudit(refundAudit)
        .withContext(context);
    
    // 根据场景类型添加特定信息
    switch (context.getScenarioType()) {
        case SUPPLEMENT_CODE:
            return builder.withSupplementCodes(context.getCoupons()).build();
        case NORMAL_REFUND:
            return builder.withNormalRefund().build();
        default:
            return builder.build();
    }
}

/**
 * 响应构建器
 */
@Builder
public static class RefundResponseBuilder {
    private DouyinRefundAudit refundAudit;
    private RefundRequestContext context;
    private List<DouyinCoupon> supplementCoupons;
    private boolean isNormalRefund;
    
    public static RefundResponseBuilder create() {
        return new RefundResponseBuilder();
    }
    
    public Map<String, Object> build() {
        Map<String, Object> responseData = new HashMap<>();
        
        // 基础信息
        responseData.put("audit_status", refundAudit.getAuditStatus());
        responseData.put("audit_reason", refundAudit.getAuditReason());
        responseData.put("refund_id", refundAudit.getRefundId());
        responseData.put("order_id", refundAudit.getDouyinOrderId());
        
        // 补码场景特殊处理
        if (supplementCoupons != null && !supplementCoupons.isEmpty()) {
            responseData.put("is_supplement_scenario", true);
            responseData.put("certificates", buildCertificates(supplementCoupons));
            responseData.put("supplement_count", supplementCoupons.size());
        }
        
        return buildSuccessResponse(responseData);
    }
    
    private List<Map<String, Object>> buildCertificates(List<DouyinCoupon> coupons) {
        return coupons.stream()
            .map(this::couponToCertificate)
            .collect(Collectors.toList());
    }
    
    private Map<String, Object> couponToCertificate(DouyinCoupon coupon) {
        Map<String, Object> cert = new HashMap<>();
        cert.put("code", coupon.getCouponCode());
        cert.put("status", convertCouponStatus(coupon.getCouponStatus()));
        cert.put("issue_time", coupon.getCreateTime());
        cert.put("expire_time", coupon.getExpireTime());
        return cert;
    }
}
```

### 9. 自定义异常类

```java
/**
 * 退款业务异常
 */
@Getter
public class RefundBusinessException extends RuntimeException {
    private final String errorCode;
    
    public RefundBusinessException(String message) {
        super(message);
        this.errorCode = "REFUND_BUSINESS_ERROR";
    }
    
    public RefundBusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}

/**
 * 退款参数校验异常
 */
public class RefundValidationException extends RuntimeException {
    public RefundValidationException(String message) {
        super(message);
    }
}
```

---

## 📊 重构效果对比

### 重构前 vs 重构后

| 方面 | 重构前 | 重构后 | 改进效果 |
|-----|--------|--------|----------|
| **方法长度** | 120+行 | 30行主控制 + 多个专门方法 | 可读性↑80% |
| **职责分离** | 混合职责 | 单一职责方法 | 可维护性↑85% |
| **异常处理** | 统一catch | 分类异常处理 | 问题定位↑90% |
| **代码复用** | 重复逻辑 | 公共方法提取 | 代码重复↓70% |
| **测试性** | 难以单独测试 | 方法独立可测 | 测试覆盖率↑60% |

### 核心优势

1. **清晰的职责分离** → 每个方法只负责一个明确功能
2. **强类型上下文** → RefundRequestContext封装所有状态
3. **精确异常处理** → 不同异常类型的分别处理
4. **场景路由清晰** → 明确的业务场景识别和路由
5. **代码高度复用** → 响应构建器模式避免重复

---

## 🎯 实施建议

### 重构步骤
1. **第一阶段** → 提取参数校验和上下文对象
2. **第二阶段** → 实现场景路由和处理方法分离
3. **第三阶段** → 统一响应构建和异常处理
4. **第四阶段** → 完善单元测试和集成测试

### 风险控制
- **渐进式重构** → 保持原方法作为备份
- **AB测试** → 新旧逻辑并行验证
- **完整测试** → 覆盖所有业务场景
- **监控告警** → 重构后的性能和错误监控

这样重构后的代码将具有更好的可读性、可维护性和可测试性，同时完美支持补码场景的业务需求。
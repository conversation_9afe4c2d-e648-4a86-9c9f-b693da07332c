[2025-07-30 21:32:30:030] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:32:30:030] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-07-30 21:32:32:032] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-07-30 21:32:32:032] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-07-30 21:32:32:032] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 82ms. Found 0 repository interfaces.
[2025-07-30 21:32:32:032] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$e6faaa78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @7110ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-07-30 21:32:33:033] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 3572 ms
[2025-07-30 21:32:34:034] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-07-30 21:32:38:038] [] [] [INFO ] [main] [DruidDataSource.java:1003] : {dataSource-1} inited
[2025-07-30 21:32:39:039] [] [] [INFO ] [main] [DefaultSessionIdManager.java:333] : DefaultSessionIdManager workerName=node0
[2025-07-30 21:32:39:039] [] [] [INFO ] [main] [DefaultSessionIdManager.java:338] : No SessionScavenger set, using defaults
[2025-07-30 21:32:39:039] [] [] [INFO ] [main] [HouseKeeper.java:140] : node0 Scavenging every 660000ms
[2025-07-30 21:32:39:039] [] [] [INFO ] [main] [ContextHandler.java:824] : Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@35cbeb54{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.846452507972052445.8080/],AVAILABLE}
[2025-07-30 21:32:39:039] [] [] [INFO ] [main] [Server.java:399] : Started @13188ms
[2025-07-30 21:32:44:044] [] [] [INFO ] [main] [RedisLockUtils.java:81] : checkRedisModel single model
[2025-07-30 21:32:44:044] [] [] [INFO ] [main] [RedisLockUtils.java:82] : redisHost:localhost
[2025-07-30 21:32:44:044] [] [] [INFO ] [main] [RedisLockUtils.java:83] : redisPort:6379
[2025-07-30 21:32:44:044] [] [] [INFO ] [main] [RedisLockUtils.java:84] : redisPassword:123456
[2025-07-30 21:32:44:044] [] [] [INFO ] [main] [Version.java:41] : Redisson 3.15.6
[2025-07-30 21:32:45:045] [] [] [INFO ] [main] [DruidDataSource.java:1948] : {dataSource-1} closing ...
[2025-07-30 21:32:45:045] [] [] [INFO ] [main] [DruidDataSource.java:2020] : {dataSource-1} closed
[2025-07-30 21:32:45:045] [] [] [INFO ] [main] [HouseKeeper.java:158] : node0 Stopped scavenging
[2025-07-30 21:32:45:045] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@35cbeb54{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.846452507972052445.8080/],UNAVAILABLE}
[2025-07-30 21:32:45:045] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-07-30 21:33:15:015] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:33:15:015] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-07-30 21:33:17:017] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-07-30 21:33:17:017] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-07-30 21:33:17:017] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 89ms. Found 0 repository interfaces.
[2025-07-30 21:33:17:017] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-07-30 21:33:18:018] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$6b48b31b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:33:18:018] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:33:18:018] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-07-30 21:33:18:018] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @7009ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-07-30 21:33:19:019] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-07-30 21:33:19:019] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-07-30 21:33:19:019] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-07-30 21:33:19:019] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 3590 ms
[2025-07-30 21:33:19:019] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-07-30 21:33:23:023] [] [] [INFO ] [main] [DruidDataSource.java:1003] : {dataSource-1} inited
[2025-07-30 21:33:24:024] [] [] [INFO ] [main] [DefaultSessionIdManager.java:333] : DefaultSessionIdManager workerName=node0
[2025-07-30 21:33:24:024] [] [] [INFO ] [main] [DefaultSessionIdManager.java:338] : No SessionScavenger set, using defaults
[2025-07-30 21:33:24:024] [] [] [INFO ] [main] [HouseKeeper.java:140] : node0 Scavenging every 660000ms
[2025-07-30 21:33:24:024] [] [] [INFO ] [main] [ContextHandler.java:824] : Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@6c1bebca{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.8118574491401396327.8080/],AVAILABLE}
[2025-07-30 21:33:24:024] [] [] [INFO ] [main] [Server.java:399] : Started @12643ms
[2025-07-30 21:33:29:029] [] [] [INFO ] [main] [RedisLockUtils.java:81] : checkRedisModel single model
[2025-07-30 21:33:29:029] [] [] [INFO ] [main] [RedisLockUtils.java:82] : redisHost:localhost
[2025-07-30 21:33:29:029] [] [] [INFO ] [main] [RedisLockUtils.java:83] : redisPort:6379
[2025-07-30 21:33:29:029] [] [] [INFO ] [main] [RedisLockUtils.java:84] : redisPassword:123456
[2025-07-30 21:33:30:030] [] [] [INFO ] [main] [Version.java:41] : Redisson 3.15.6
[2025-07-30 21:33:30:030] [] [] [INFO ] [redisson-netty-4-24] [ConnectionPool.java:166] : 1 connections initialized for localhost/127.0.0.1:6379
[2025-07-30 21:33:30:030] [] [] [INFO ] [redisson-netty-4-15] [ConnectionPool.java:166] : 24 connections initialized for localhost/127.0.0.1:6379
[2025-07-30 21:33:49:049] [] [] [INFO ] [main] [SnowflakeConfig.java:24] : 当前机器的workId:**********
[2025-07-30 21:33:49:049] [] [] [INFO ] [main] [EpollProvider.java:68] : Starting without optional epoll library
[2025-07-30 21:33:50:050] [] [] [INFO ] [main] [KqueueProvider.java:66] : Starting with kqueue library
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [DouyinSpiQueueConfig.java:36] : 初始化抖音SPI交换机: douyin.spi.exchange
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [DouyinSpiQueueConfig.java:46] : 初始化抖音SPI日志队列: douyin.spi.log
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [DouyinSpiQueueConfig.java:56] : 初始化抖音SPI记录队列: douyin.spi.record
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [DouyinSpiQueueConfig.java:68] : 绑定抖音SPI日志队列到交换机: douyin.spi.log -> douyin.spi.exchange
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [DouyinSpiQueueConfig.java:80] : 绑定抖音SPI记录队列到交换机: douyin.spi.record -> douyin.spi.exchange
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [URLConfigurationSource.java:122] : To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2025-07-30 21:33:50:050] [e-msr-marketing] [] [INFO ] [main] [URLConfigurationSource.java:122] : To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2025-07-30 21:33:51:051] [e-msr-marketing] [] [INFO ] [main] [ExecutorConfigurationSupport.java:171] : Initializing ExecutorService 'applicationTaskExecutor'
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [EndpointLinksResolver.java:58] : Exposing 2 endpoint(s) beneath base path '/actuator'
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:deleteUnPayOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72cf43fa[class com.besttop.marketing.job.XXLJobHandler#deleteUnPayOrder]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:appraisePayOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2b1818d1[class com.besttop.marketing.job.XXLJobHandler#appraisePayOrder]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:updateInvoiceInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1f748d43[class com.besttop.marketing.job.XXLJobHandler#updateInvoiceInfo]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:callStoredProcedure, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1104cc93[class com.besttop.marketing.job.XXLJobHandler#callStoredProcedure]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:integralExpire, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@19dc9dda[class com.besttop.marketing.job.XXLJobHandler#integralExpire]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:pullOrderPmall, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72e9c0a0[class com.besttop.marketing.job.XXLJobHandler#pullPmallOrder]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:douyinRefreshAuthTokens, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3f04349b[class com.besttop.marketing.job.XXLJobHandler#douyinRefreshAuthTokens]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:cancelOldfornewSkuDetailByHour, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14ca4b4d[class com.besttop.marketing.job.XXLJobHandler#cancelOldfornewSkuDetailByHour]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [XxlJobExecutor.java:168] : >>>>>>>>>>> xxl-job register jobhandler success, name:douyinSyncVerificationStatus, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@31b2c451[class com.besttop.marketing.job.XXLJobHandler#douyinSyncVerificationStatus]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [Thread-102] [EmbedServer.java:86] : >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 30107
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [AbstractConnectionFactory.java:524] : Attempting to connect to: [localhost:5672]
[2025-07-30 21:33:52:052] [e-msr-marketing] [] [INFO ] [main] [AbstractConnectionFactory.java:497] : Created new connection: rabbitConnectionFactory#5d2e6f62:0/SimpleConnection@1c7da28 [delegate=amqp://admin@127.0.0.1:5672/, localPort= 51029]
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [AbstractMessageListenerContainer.java:1756] : Broker not available; cannot force queue declarations during start: java.io.IOException
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [FrameworkServlet.java:525] : Initializing Servlet 'dispatcherServlet'
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [FrameworkServlet.java:547] : Completed initialization in 42 ms
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [AbstractConnector.java:330] : Started ServerConnector@3b57f915{HTTP/1.1,[http/1.1]}{0.0.0.0:8080}
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [JettyWebServer.java:156] : Jetty started on port(s) 8080 (http/1.1) with context path '/'
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [main] [StartupInfoLogger.java:61] : Started MarketingApplication in 51.521 seconds (JVM running for 53.372)
[2025-07-30 21:34:05:005] [e-msr-marketing] [] [INFO ] [RMI TCP Connection(4)-127.0.0.1] [TypeUtil.java:201] : JVM Runtime does not support Modules
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:46] : request post /douyin/gift/verification/customer 
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:47] : request ip=0:0:0:0:0:0:0:1
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:48] : request classMethod=com.besttop.marketing.controller.thirdparty.douyin.DouyinCouponController.listCustomerGiftVerifications
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:49] : request loginType=null
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:50] : request login-client=null
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:71] : request body /douyin/gift/verification/customer {"customerCode":"2509"}
[2025-07-30 21:35:31:031] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [DouyinCouponController.java:218] : 查询顾客已核销的抖音礼品券: 2509
[2025-07-30 21:35:32:032] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:81] : response body /douyin/gift/verification/customer {"data":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":0,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"flag":1,"message":"成功","success":true}
[2025-07-30 21:35:32:032] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-43] [RequestLogAspect.java:85] : request consume /douyin/gift/verification/customer 766ms
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:46] : request post /douyin/gift/verification/customer 
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:47] : request ip=0:0:0:0:0:0:0:1
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:48] : request classMethod=com.besttop.marketing.controller.thirdparty.douyin.DouyinCouponController.listCustomerGiftVerifications
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:49] : request loginType=null
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:50] : request login-client=null
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:71] : request body /douyin/gift/verification/customer {"customerCode":"2509"}
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [DouyinCouponController.java:218] : 查询顾客已核销的抖音礼品券: 2509
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:81] : response body /douyin/gift/verification/customer {"data":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":0,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"flag":1,"message":"成功","success":true}
[2025-07-30 21:36:40:040] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-50] [RequestLogAspect.java:85] : request consume /douyin/gift/verification/customer 247ms
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:46] : request post /douyin/gift/verification/customer 
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:47] : request ip=0:0:0:0:0:0:0:1
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:48] : request classMethod=com.besttop.marketing.controller.thirdparty.douyin.DouyinCouponController.listCustomerGiftVerifications
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:49] : request loginType=null
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:50] : request login-client=null
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:71] : request body /douyin/gift/verification/customer {"customerCode":"2509"}
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [DouyinCouponController.java:218] : 查询顾客已核销的抖音礼品券: 2509
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:81] : response body /douyin/gift/verification/customer {"data":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":0,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"flag":1,"message":"成功","success":true}
[2025-07-30 21:37:51:051] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:85] : request consume /douyin/gift/verification/customer 363ms
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:46] : request post /douyin/gift/verification/customer 
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:47] : request ip=0:0:0:0:0:0:0:1
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:48] : request classMethod=com.besttop.marketing.controller.thirdparty.douyin.DouyinCouponController.listCustomerGiftVerifications
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:49] : request loginType=null
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:50] : request login-client=null
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:71] : request body /douyin/gift/verification/customer {"customerCode":"2509"}
[2025-07-30 21:37:53:053] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [DouyinCouponController.java:218] : 查询顾客已核销的抖音礼品券: 2509
[2025-07-30 21:37:54:054] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:81] : response body /douyin/gift/verification/customer {"data":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":0,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0},"flag":1,"message":"成功","success":true}
[2025-07-30 21:37:54:054] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-243] [RequestLogAspect.java:85] : request consume /douyin/gift/verification/customer 148ms
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:46] : request post /douyin/gift/verification/customer 
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:47] : request ip=0:0:0:0:0:0:0:1
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:48] : request classMethod=com.besttop.marketing.controller.thirdparty.douyin.DouyinCouponController.listCustomerGiftVerifications
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:49] : request loginType=null
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:50] : request login-client=null
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:71] : request body /douyin/gift/verification/customer {"customerCode":"2509"}
[2025-07-30 21:40:00:000] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [DouyinCouponController.java:218] : 查询顾客已核销的抖音礼品券: 2509
[2025-07-30 21:40:01:001] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:81] : response body /douyin/gift/verification/customer {"data":{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"couponCode":"DQC0000000005","couponId":"1949489734669578242","createBy":"DOUYIN_GIFT_VERIFY","createTime":1753849840000,"customerCode":"2509","douyinOrderId":"1086778689738267909","giftIssueStatus":"PENDING","giftType":"B","id":"1950413712435294209","phone":"13669183770","staffId":"5409","storeCode":"077","syncStatus":"CANCELLED","updateTime":1753882800000,"verificationStatus":"SUCCESS","verificationTime":1753849841000}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":1,"pages":1,"prePage":0,"size":1,"startRow":0,"total":1},"flag":1,"message":"成功","success":true}
[2025-07-30 21:40:01:001] [e-msr-marketing] [tn:eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2ODYyM2VhNGU0YjA2MTI4YTUwY2E3OGMiLCJpYXQiOjE3NTEyNjkwMjgsInN1YiI6IjExMTY5IiwiZXhwIjoxNzUxMzU1NDI4fQ.nOB8DF8K0p9C7IqZsOip-tHoPj0wQ23oKN_7TwI3oP4] [INFO ] [qtp1966372954-45] [RequestLogAspect.java:85] : request consume /douyin/gift/verification/customer 485ms
[2025-07-30 22:09:45:045] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-07-30 22:09:46:046] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:643] : Successfully waited for workers to finish.
[2025-07-30 22:09:46:046] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-07-30 22:09:46:046] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:643] : Successfully waited for workers to finish.
[2025-07-30 22:09:46:046] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-07-30 22:09:47:047] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:643] : Successfully waited for workers to finish.
[2025-07-30 22:09:47:047] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.

# 抖音礼品券发放和回收功能集成方案

## 1. 项目背景

### 1.1 现状分析
- 现有`ManualGiftRecordServiceImpl.grantOrRecycle`方法支持A/B/C三种礼品类型的发放和回收
- 抖音礼品券核销后处于"待发放"状态，需要集成到现有发放流程中
- 需要支持抖音礼品券的发放和回收操作

### 1.2 业务需求
- 在现有礼品发放/回收功能中增加抖音礼品券类型
- 支持抖音礼品券的发放和回收操作
- 保持与现有系统的兼容性和一致性

## 2. 技术方案设计

### 2.1 整体架构

```mermaid
flowchart TD
    A[grantOrRecycle入口] --> B{判断礼品类型}
    B -->|A-电子币| C[disposeA处理]
    B -->|B-自营商品| D[disposeB处理]
    B -->|C-联营商品| E[disposeC处理]
    B -->|DOUYIN-抖音礼品券| F[disposeDouyin处理]
    
    F --> G[查询抖音核销记录]
    G --> H[创建礼品发放记录]
    H --> I[更新核销记录状态]
    I --> J[执行具体发放逻辑]
```

### 2.2 核心改造点

#### 2.2.1 礼品类型扩展
```java
// 在CommonEnums中增加抖音礼品券类型
public enum CommonEnums {
    MANUAL_GIFT_RECORD_GIFT_TYPE_A("A", "电子币"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_B("B", "自营商品"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_C("C", "联营商品"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_DOUYIN("DOUYIN", "抖音礼品券"); // 新增
}
```

#### 2.2.2 业务逻辑分支
```java
// 在addScheduleGiftRecord方法中增加抖音礼品券处理分支
switch (enums) {
    case MANUAL_GIFT_RECORD_GIFT_TYPE_A:
        this.disposeA(param);
        break;
    case MANUAL_GIFT_RECORD_GIFT_TYPE_B:
        this.disposeB(param);
        break;
    case MANUAL_GIFT_RECORD_GIFT_TYPE_C:
        this.disposeC(param);
        break;
    case MANUAL_GIFT_RECORD_GIFT_TYPE_DOUYIN: // 新增
        this.disposeDouyin(param);
        break;
}
```

## 3. 详细实现方案

### 3.1 数据表关系

#### 3.1.1 核心表结构
| 表名 | 作用 | 关键字段 |
|------|------|---------|
| `t_douyin_gift_verification_record` | 抖音礼品核销记录 | `coupon_code`, `gift_issue_status`, `customer_code` |
| `t_manual_gift_record` | 礼品发放记录 | `code`, `gift_type`, `customer_code` |
| `t_douyin_coupon` | 抖音券信息 | `coupon_code`, `coupon_status` |
| `t_douyin_coupon_rule` | 抖音券规则 | `groupon_type`, `coupon_value` |

#### 3.1.2 数据流转关系
```mermaid
erDiagram
    t_douyin_gift_verification_record ||--|| t_manual_gift_record : "发放时关联"
    t_douyin_gift_verification_record ||--|| t_douyin_coupon : "核销时关联"
    t_douyin_coupon ||--|| t_douyin_coupon_rule : "规则关联"
    
    t_douyin_gift_verification_record {
        string id PK
        string coupon_code
        string customer_code
        string gift_issue_status
        string verification_status
    }
    
    t_manual_gift_record {
        string id PK
        string code
        string gift_type
        string customer_code
        string douyin_verification_id
    }
```

### 3.2 核心方法实现

#### 3.2.1 disposeDouyin方法
```java
/**
 * 处理抖音礼品券发放/回收
 */
private void disposeDouyin(ManualGiftRecord param) {
    String verificationId = param.getDouyinVerificationId();
    String couponCode = param.getCouponCode();
    
    // 1. 查询抖音核销记录
    DouyinGiftVerificationRecord verificationRecord = 
        douyinGiftVerificationService.getVerificationRecord(couponCode);
    
    if (verificationRecord == null) {
        throw new RuntimeException("未找到对应的抖音礼品核销记录");
    }
    
    // 2. 验证发放状态
    if (!DouyinGiftVerificationRecord.GIFT_ISSUE_STATUS_PENDING
            .equals(verificationRecord.getGiftIssueStatus())) {
        throw new RuntimeException("该抖音礼品券已发放或状态异常");
    }
    
    // 3. 发放操作
    if (param.getTag().equals("grant")) {
        // 更新核销记录状态为已发放
        verificationRecord.setGiftIssueStatus(DouyinGiftVerificationRecord.GIFT_ISSUE_STATUS_ISSUED);
        verificationRecord.setGiftRecordId(param.getCode());
        douyinGiftVerificationService.updateById(verificationRecord);
        
        // 根据礼品类型执行具体发放逻辑
        executeDouyinGiftIssue(param, verificationRecord);
        
    } else {
        // 回收操作
        verificationRecord.setGiftIssueStatus(DouyinGiftVerificationRecord.GIFT_ISSUE_STATUS_CANCELLED);
        douyinGiftVerificationService.updateById(verificationRecord);
        
        // 执行回收逻辑
        executeDouyinGiftRecycle(param, verificationRecord);
    }
}
```

#### 3.2.2 抖音礼品发放逻辑
```java
/**
 * 执行抖音礼品发放
 */
private void executeDouyinGiftIssue(ManualGiftRecord param, 
                                   DouyinGiftVerificationRecord verificationRecord) {
    // 1. 获取券规则信息
    DouyinCouponRule rule = douyinCouponRuleService.getById(verificationRecord.getRuleId());
    
    // 2. 根据规则中的商品信息创建发放记录
    List<DouyinCouponRuleDetail> ruleDetails = 
        douyinCouponRuleDetailService.getByRuleCode(rule.getCode());
    
    for (DouyinCouponRuleDetail detail : ruleDetails) {
        // 创建具体的礼品发放记录
        createGiftIssueRecord(param, verificationRecord, detail);
    }
    
    // 3. 同步状态到抖音平台
    douyinSyncService.syncGiftIssueStatus(verificationRecord);
}
```

#### 3.2.3 抖音礼品回收逻辑
```java
/**
 * 执行抖音礼品回收
 */
private void executeDouyinGiftRecycle(ManualGiftRecord param, 
                                     DouyinGiftVerificationRecord verificationRecord) {
    // 1. 撤销已发放的礼品
    if (StringUtils.isNotEmpty(verificationRecord.getGiftRecordId())) {
        // 查找并撤销相关的礼品发放记录
        ManualGiftRecord giftRecord = manualGiftRecordService.getById(
            verificationRecord.getGiftRecordId());
        if (giftRecord != null) {
            // 执行回收逻辑
            executeGiftRecycle(giftRecord);
        }
    }
    
    // 2. 同步回收状态到抖音平台
    douyinSyncService.syncGiftRecycleStatus(verificationRecord);
}
```

### 3.3 查询功能增强

#### 3.3.1 抖音礼品券查询
```java
/**
 * 查询顾客的抖音礼品券（待发放）
 */
public List<DouyinGiftVerificationRecord> getCustomerDouyinGifts(String customerCode) {
    LambdaQueryWrapper<DouyinGiftVerificationRecord> query = new LambdaQueryWrapper<>();
    query.eq(DouyinGiftVerificationRecord::getCustomerCode, customerCode)
         .eq(DouyinGiftVerificationRecord::getVerificationStatus, "SUCCESS")
         .eq(DouyinGiftVerificationRecord::getGiftIssueStatus, "PENDING")
         .orderByDesc(DouyinGiftVerificationRecord::getVerificationTime);
    
    return douyinGiftVerificationRecordMapper.selectList(query);
}
```

#### 3.3.2 礼品类型判断
```java
/**
 * 判断是否为抖音礼品券
 */
private boolean isDouyinGiftType(String giftType) {
    return "DOUYIN".equals(giftType);
}
```

## 4. 接口改造

### 4.1 前端接口增强

#### 4.1.1 查询抖音礼品券接口
```java
/**
 * 查询顾客的抖音礼品券
 */
@PostMapping("/gift/douyin/customer")
public ResultEntity getCustomerDouyinGifts(@RequestBody Map<String, Object> params) {
    try {
        String customerCode = String.valueOf(params.getOrDefault("customerCode", ""));
        log.info("查询顾客抖音礼品券: {}", customerCode);
        
        DouyinGiftVerificationService giftService = 
            SpringContext.getBean(DouyinGiftVerificationService.class);
        List<DouyinGiftVerificationRecord> records = 
            giftService.getCustomerDouyinGifts(customerCode);
        
        return success(records);
    } catch (Exception e) {
        log.error("查询顾客抖音礼品券失败", e);
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询失败：" + e.getMessage());
    }
}
```

#### 4.1.2 抖音礼品券发放接口
```java
/**
 * 发放抖音礼品券
 */
@PostMapping("/gift/douyin/issue")
public ResultEntity issueDouyinGift(@RequestBody ManualGiftRecord param) {
    try {
        param.setTag("grant");
        param.setGiftType("DOUYIN");
        
        ResultEntity result = manualGiftRecordService.grantOrRecycle(param);
        return result;
    } catch (Exception e) {
        log.error("发放抖音礼品券失败", e);
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "发放失败：" + e.getMessage());
    }
}
```

### 4.2 数据模型扩展

#### 4.2.1 ManualGiftRecord扩展
```java
@Data
public class ManualGiftRecord {
    // 现有字段...
    
    /** 抖音核销记录ID */
    @TableField("douyin_verification_id")
    private String douyinVerificationId;
    
    /** 抖音券码 */
    @TableField("douyin_coupon_code")
    private String douyinCouponCode;
    
    /** 抖音礼品类型 */
    @TableField("douyin_gift_type")
    private String douyinGiftType;
}
```

## 5. 数据库改造

### 5.1 表结构修改

#### 5.1.1 manual_gift_record表扩展
```sql
-- 为manual_gift_record表增加抖音相关字段
ALTER TABLE t_manual_gift_record 
ADD COLUMN douyin_verification_id VARCHAR(32) COMMENT '抖音核销记录ID' AFTER create_by,
ADD COLUMN douyin_coupon_code VARCHAR(64) COMMENT '抖音券码' AFTER douyin_verification_id,
ADD COLUMN douyin_gift_type VARCHAR(32) COMMENT '抖音礼品类型' AFTER douyin_coupon_code;

-- 增加索引
ALTER TABLE t_manual_gift_record 
ADD INDEX idx_douyin_verification_id (douyin_verification_id),
ADD INDEX idx_douyin_coupon_code (douyin_coupon_code);
```

#### 5.1.2 douyin_gift_verification_record表扩展
```sql
-- 为douyin_gift_verification_record表增加关联字段
ALTER TABLE t_douyin_gift_verification_record 
ADD COLUMN gift_record_id VARCHAR(32) COMMENT '关联礼品发放记录ID' AFTER gift_issue_status;

-- 增加索引
ALTER TABLE t_douyin_gift_verification_record 
ADD INDEX idx_gift_record_id (gift_record_id);
```

### 5.2 数据迁移脚本
```sql
-- 数据迁移：将现有的抖音礼品核销记录关联到礼品发放记录
UPDATE t_douyin_gift_verification_record v
INNER JOIN t_manual_gift_record m ON v.coupon_code = m.douyin_coupon_code
SET v.gift_record_id = m.id
WHERE v.gift_issue_status = 'ISSUED' 
  AND m.gift_type = 'DOUYIN';
```

## 6. 业务流程设计

### 6.1 抖音礼品券发放流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant C as Controller
    participant S as Service
    participant DB as 数据库
    
    F->>C: 查询顾客抖音礼品券
    C->>S: getCustomerDouyinGifts()
    S->>DB: 查询核销记录
    DB-->>S: 返回待发放记录
    S-->>C: 返回礼品券列表
    C-->>F: 返回数据
    
    F->>C: 发放抖音礼品券
    C->>S: grantOrRecycle()
    S->>S: disposeDouyin()
    S->>DB: 更新核销记录状态
    S->>DB: 创建礼品发放记录
    S->>S: 执行具体发放逻辑
    S-->>C: 返回发放结果
    C-->>F: 返回结果
```

### 6.2 抖音礼品券回收流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant C as Controller
    participant S as Service
    participant DB as 数据库
    
    F->>C: 回收抖音礼品券
    C->>S: grantOrRecycle(tag=recycle)
    S->>S: disposeDouyin()
    S->>DB: 更新核销记录状态
    S->>S: 执行回收逻辑
    S->>DB: 撤销相关发放记录
    S-->>C: 返回回收结果
    C-->>F: 返回结果
```

## 7. 测试方案

### 7.1 单元测试
```java
@Test
public void testDisposeDouyin() {
    // 测试抖音礼品券发放
    ManualGiftRecord param = new ManualGiftRecord();
    param.setTag("grant");
    param.setGiftType("DOUYIN");
    param.setDouyinCouponCode("DQC0000000001");
    
    ResultEntity result = manualGiftRecordService.grantOrRecycle(param);
    assertEquals(ResultEnum.SUCCESS, result.getFlag());
}

@Test
public void testDisposeDouyinRecycle() {
    // 测试抖音礼品券回收
    ManualGiftRecord param = new ManualGiftRecord();
    param.setTag("recycle");
    param.setGiftType("DOUYIN");
    param.setDouyinCouponCode("DQC0000000001");
    
    ResultEntity result = manualGiftRecordService.grantOrRecycle(param);
    assertEquals(ResultEnum.SUCCESS, result.getFlag());
}
```

### 7.2 集成测试
```java
@Test
public void testDouyinGiftIssueFlow() {
    // 1. 模拟抖音礼品券核销
    // 2. 查询待发放礼品券
    // 3. 执行发放操作
    // 4. 验证发放结果
}
```

## 8. 部署和监控

### 8.1 部署步骤
1. **数据库迁移**
   - 执行表结构修改脚本
   - 执行数据迁移脚本
   - 验证数据完整性

2. **代码部署**
   - 部署改造后的代码
   - 重启应用服务
   - 验证服务启动正常

3. **功能验证**
   - 测试抖音礼品券查询功能
   - 测试抖音礼品券发放功能
   - 测试抖音礼品券回收功能

### 8.2 监控指标
- 抖音礼品券发放成功率
- 抖音礼品券回收成功率
- 接口响应时间
- 数据库操作性能

## 9. 风险评估和应对

### 9.1 技术风险
| 风险 | 影响 | 应对措施 |
|------|------|---------|
| 数据一致性 | 高 | 使用事务确保数据一致性 |
| 性能影响 | 中 | 优化查询，增加缓存 |
| 兼容性问题 | 中 | 充分测试，渐进式发布 |

### 9.2 业务风险
| 风险 | 影响 | 应对措施 |
|------|------|---------|
| 用户操作错误 | 中 | 增加操作确认和回滚机制 |
| 数据丢失 | 高 | 完善备份和恢复机制 |
| 系统稳定性 | 高 | 充分测试，灰度发布 |

## 10. 总结

### 10.1 改造收益
- **功能完整性**: 支持抖音礼品券的完整生命周期管理
- **系统统一性**: 将抖音礼品券集成到现有礼品管理体系中
- **用户体验**: 提供统一的礼品发放和回收界面

### 10.2 技术亮点
- **架构扩展性**: 通过策略模式支持新的礼品类型
- **数据一致性**: 通过事务确保数据操作的原子性
- **接口兼容性**: 保持与现有系统的接口兼容

### 10.3 后续优化
- **性能优化**: 增加缓存层，优化查询性能
- **功能增强**: 支持批量操作，增加更多礼品类型
- **监控完善**: 增加更详细的业务监控指标 
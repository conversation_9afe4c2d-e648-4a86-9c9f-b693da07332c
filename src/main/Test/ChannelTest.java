import com.besttop.marketing.mapper.channel.ChannelPolicyMapper;
import com.besttop.marketing.model.channel.ChannelPolicy;
import com.besttop.marketing.model.channel.param.ChannelPolicyParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class ChannelTest {

    @Autowired
    private ChannelPolicyMapper channelPolicyMapper;

    @Test
    public void test1() {
        ChannelPolicyParam policyParam=new ChannelPolicyParam();


        List<ChannelPolicy> channelPolicy=channelPolicyMapper.queryChannelPolicyList(policyParam);
        System.out.println(channelPolicy);
    }
}

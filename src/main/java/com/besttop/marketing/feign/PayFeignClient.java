package com.besttop.marketing.feign;

import com.besttop.common.interceptor.FeignRequestInterceptor;
import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name="e-msr-pay", url="http://e-msr-pay", configuration=FeignRequestInterceptor.class)
public interface PayFeignClient {

    @PostMapping({"/pay/scanPay"})
    ResultEntity scanPay(Object object);

    @PostMapping({"/pay/orderQuery"})
    ResultEntity orderQuery(Object object);

    @PostMapping({"/pay/refund"})
    ResultEntity refund(Object object);
    
    @PostMapping({"/pay/balanceQuery"})
    ResultEntity balanceQuery(Object object);

    @PostMapping({"/pay/appletPay"})
    ResultEntity appletPay(Object object);

    @PostMapping({"/pay/unionPay"})
    ResultEntity unionPay(Object object);

    @PostMapping({"/pay/refundUnionPay"})
    ResultEntity refundUnionPay(Object object);

    @PostMapping({"/pay/xinbaiPay"})
    ResultEntity xinbaiPay(Object object);

}

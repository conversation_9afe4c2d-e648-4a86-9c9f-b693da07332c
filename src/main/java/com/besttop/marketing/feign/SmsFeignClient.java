package com.besttop.marketing.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.besttop.common.model.ResultEntity;

/**
 * <p>Title: SmsFeignClient</p>
 * <p>Description: SmsFeignClient</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018</p>
 * <p> 短信服务 客户端 </p>
 *
 * <AUTHOR>
 * @since 2021-03-34
 */

@FeignClient(name = "e-msr-sms", url = "http://e-msr-sms")
public interface SmsFeignClient {

	@PostMapping("/v1/sms/checkCode")
	ResultEntity checkCode(Object object);
	
	@PostMapping("/v1/sms/sendCode")
	ResultEntity sendCode(Object object);
}

package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name="e-msr-settlement", url="http://e-msr-settlement")
public interface SettlementFeignClient {

    /**
     * 查询毛利配置
     * @param object {sourceStoreCode,classCode,brandCode}
     * @return
     */
    @PostMapping("/profitMoveSet/queryProfitMoveSet")
    ResultEntity queryProfitMoveSet(Object object);
}

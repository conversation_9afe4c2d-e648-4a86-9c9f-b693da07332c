package com.besttop.marketing.feign;

import com.besttop.common.interceptor.FeignRequestInterceptor;
import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "e-msr-order", url = "http://e-msr-order", configuration = FeignRequestInterceptor.class)
public interface OrderFeignClient {

    /**
     * 购物送积分、 手动加减积分、 签到送积分、评价送积分、 完善信息、 注册
     */
    @PostMapping("/integral/addInternal")
    ResultEntity addInternal(Object object);

    /**
     * 三方订单送积分/ 购物送积分
     */
    @PostMapping("/job/addInternal")
    ResultEntity addThirdInternal(Object object);

    /**
     * 积分兑换
     */
    @PostMapping("/integral/exchange")
    ResultEntity exchange(Object object);

    /**
     * 积分兑换回收
     */
    @PostMapping("/integral/recycle")
    ResultEntity recycle(Object object);

    /**
     * 销售单退款回收积分
     */
    @PostMapping("/integral/refund")
    ResultEntity refund(Object object);

    /**
     * 三方订单退款回收积分
     */
    @PostMapping("/job/refund")
    ResultEntity thirdrefund (Object object);

    /**
     * 定时任务过期
     */
    @PostMapping("/job/expire")
    ResultEntity expire(Object object);
}

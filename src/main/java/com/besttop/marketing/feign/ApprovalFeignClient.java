package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name="e-msr-approval", url="http://e-msr-approval")
//@FeignClient(name="e-msr-approval", url="http://127.0.0.1:8081")
public interface ApprovalFeignClient {

    @PostMapping("/approvalDefine/findDefine")
    ResultEntity findDefine(Object object);
}

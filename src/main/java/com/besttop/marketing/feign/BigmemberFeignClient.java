package com.besttop.marketing.feign;

import java.util.List;
import java.util.Map;

import com.besttop.marketing.model.pushorder.DmallOrderVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.alibaba.fastjson.JSONObject;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.coupon.param.DMallIssueCouponsParam;
import com.besttop.marketing.model.coupon.param.DMallQueryCouponDefineParam;
import com.besttop.marketing.model.integral.param.BesttopDMallIntegralConsumeParam;
import com.besttop.marketing.model.integral.param.BesttopDMallIntegralRechargeParam;
import com.besttop.marketing.model.integral.param.BesttopDMallIntegralRefundParam;
import com.besttop.marketing.model.integral.param.BesttopDMallIntegralRelationParam;
import com.besttop.marketing.model.integral.result.DMallIntegralConsumeResponse;
import com.besttop.marketing.model.integral.result.DMallIntegralRechargeResponse;
import com.besttop.marketing.model.integral.result.DMallIntegralRefundResponse;
import com.besttop.marketing.model.integral.result.DMallIntegralUsableResponse;
import com.besttop.marketing.model.pushorder.DmallOrder;
import com.besttop.marketing.model.pushorder.DmallRefundOrder;

@FeignClient(name="e-msr-bigmember", url="http://e-msr-bigmember")
public interface BigmemberFeignClient {

    @PostMapping("/dmall/integral/queryIntegralUsable")
    ResultEntity<DMallIntegralUsableResponse> queryIntegralUsable(BesttopDMallIntegralRelationParam param);

    @PostMapping("/dmall/integral/integralRefund")
    ResultEntity<JSONObject> integralRefund(BesttopDMallIntegralRefundParam param);

    @PostMapping("/dmall/integral/integralRecharge")
    ResultEntity<JSONObject> integralRecharge(BesttopDMallIntegralRechargeParam param);

    @PostMapping("/dmall/integral/integralConsume")
    ResultEntity<JSONObject> integralConsume(BesttopDMallIntegralConsumeParam param);

//    @PostMapping("/dmall/coupon/queryCouponDefines")
//    ResultEntity<Map<String, Object>> queryCouponDefines(Map<String, Object> param);
    
//    @PostMapping("/dmall/coupon/queryCustomerCoupons")
//    ResultEntity<Map<String, Object>> queryCustomerCoupons(Map<String, Object> param);
    
    @PostMapping("/dmall/coupon/issueCoupons")
    ResultEntity<Map<String, Object>> issueCoupons(DMallIssueCouponsParam param);
    
    @PostMapping("/dmall/coupon/queryCouponDefines")
    ResultEntity<List<Map<String, Object>>> queryCouponDefines(DMallQueryCouponDefineParam param);
    
//    @PostMapping("/dmall/order/queryDmallUserId")
//    ResultEntity<Map<String, String>> queryDmallUserId(List<String> customerCode);
//    
//    @PostMapping("/dmall/order/queryDmallUserIdByPhones")
//    ResultEntity<Map<String, String>> queryDmallUserIdByPhones(List<String> customerPhone);
    
    @PostMapping("/dmall/order/pushSaleOrder")
    ResultEntity<JSONObject> pushSaleOrder(DmallOrder order);
    
    @PostMapping("/dmall/order/pushRefundOrder")
    ResultEntity<JSONObject> pushRefundOrder(DmallRefundOrder order);
    
    @PostMapping("/dmall/member/queryByCode")
    ResultEntity<JSONObject> queryByCode(Map<String, String> user);

    @PostMapping("/dmall/order/pushWaitPayOrder")
    ResultEntity<JSONObject> pushWaitPayOrder(DmallOrderVo order);

    @PostMapping("/dmall/order/manualPushPayOrder")
    ResultEntity<JSONObject> manualPushPayOrder(DmallOrderVo order);

    @PostMapping("/dmall/order/getDmallOderInfo")
    ResultEntity<JSONObject> getDmallOderInfo(DmallOrderVo order);

    @PostMapping("/dmall/order/updateDmallOrderStatus")
    ResultEntity<JSONObject> updateDmallOrderStatus(DmallOrderVo order);

    @PostMapping("/dmall/order/dmallOrderCancel")
    ResultEntity<JSONObject> dmallOrderCancel(DmallOrderVo order);

    @PostMapping("/dmall/order/pushAfterSaleOrder")
    ResultEntity<JSONObject> pushAfterSaleOrder(DmallOrderVo dmallOrderVo);

    @PostMapping("/dmall/order/getAfterSaleDetail")
    ResultEntity<JSONObject> getAfterSaleDetail(DmallOrderVo dmallOrderVo);

    @PostMapping("/performance/performanceSync")
    ResultEntity performanceSync(Object object);

}

package com.besttop.marketing.feign;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.besttop.common.model.ResultEntity;

@FeignClient(name="e-msr-user", url="http://e-msr-user")
public interface UserFeignClient {

    @PostMapping("/customer/query")
    ResultEntity queryCustomers(Object object);
    
    @PostMapping("/customer/queryByNameOrPhonePC")
    ResultEntity queryByNameOrPhonePC(Object object);
    
    
    /**
     * UserCode和UserType查AccountInfo
     * @param queryParam{userType:XXXX,userCode:XXXX}
     * @return
     */
    @PostMapping("/account/queryAccountInfo")
    ResultEntity queryAccountInfoByUserCodeAndType(Map<String, String> queryParam);
    
    
    @PostMapping("/user/queryUsersNew")
    ResultEntity queryUsersByNameOrCode(Map<String, String> userParam) ;

    @PostMapping("/customerGrowth/add")
    ResultEntity addCustomerGrowth(Object object);

    @PostMapping("/customerGrowth/reduce")
    ResultEntity reduceCustomerGrowth(Object object);
    
    @PostMapping("/customerLevel/queryLevelByCode")
    ResultEntity queryLevelByCode(Map<String, Object> customer);

    @PostMapping("/labelManageCustomer/updateForIntegralUsage")
    ResultEntity updateForIntegralUsage(Object object);

    @PostMapping("/labelManageCustomer/updateForCouponReceived")
    ResultEntity updateForCouponReceived(Object object);

}

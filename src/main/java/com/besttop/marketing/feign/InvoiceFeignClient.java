package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

@FeignClient(name="e-msr-invoice", url="http://e-msr-invoice")
public interface InvoiceFeignClient {

    @PostMapping("/invoice/requestBillingManual")
    ResultEntity requestBillingManual(Object object);
    
    @PostMapping("/invoice/requestBilling")
    ResultEntity requestBilling(Object object);

    @PostMapping("/invoice/requestBillingAcpp")
    ResultEntity requestBillingAcpp(Object object);
    
    @PostMapping("/invoice/queryInvoiceResult")
    ResultEntity queryInvoiceResult(Object object);

    @PostMapping("/invoice/sendEmail")
    ResultEntity sendEmail(Object object);
}

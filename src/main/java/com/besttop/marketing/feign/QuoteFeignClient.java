package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

//@FeignClient(name = "e-msr-quote", url = "http://e-msr-quote")
@FeignClient(name = "e-msr-quote", url = "localhost:8081")
public interface QuoteFeignClient {
    /**
     * @param object
     * @return com.besttop.common.model.ResultEntity
     * @methodName findMarketingConfig
     * @description 获取配送中心
     * <AUTHOR>
     * @date 2020/3/6 13:47
     */
    @PostMapping({"/store/findDirect"})
    ResultEntity findDirect(Object object);
    
    @PostMapping({"/store/findCodesByChargerCode"})
    ResultEntity findCodesByChargerCode(Map<String, String> map);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDirect
     * @description 获取集客信息
     * @params [object]
     * <AUTHOR>
     * @date 2020/4/13 16:59
     */
    @PostMapping({"/customer/select"})
    ResultEntity customerSelect(Object object);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDirect
     * @description 获取公司下的机构
     * @params [object]
     * <AUTHOR>
     * @date 2020/4/13 16:59
     */
    @PostMapping({"/store/selectStoreOption"})
    ResultEntity selectStoreOption(Object object);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBestStrategy
     * @description 获取最佳策略
     * @params [object]
     * <AUTHOR>
     * @date 2020/4/13 16:59
     */
    @PostMapping({"/payConfig/findBestStrategy"})
    ResultEntity findBestStrategy(Object object);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBestStrategy
     * @description 获取支付方式名称
     * @params [object]
     * <AUTHOR>
     * @date 2020/4/13 16:59
     */
    @PostMapping("/payConfig/findNameByCode")
    ResultEntity findNameByCode(Object object);

    /**
     * @param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryCompanyInfo
     * @description 获取机构所属隶属公司的基础信息
     * <AUTHOR>
     * @date 2020/4/21 9:50
     */
    @PostMapping({"/store/queryCompanyInfo"})
    ResultEntity queryCompanyInfo(Object object);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBestStrategy
     * @description 获取支付方式
     * @params [object]
     * <AUTHOR>
     * @date 2020/4/13 16:59
     */
    @PostMapping({"/payConfig/findPayConfigByCodes"})
    ResultEntity findPayConfigByCodes(Object object);

    /***
     *获取用户的是否为新老用户 0 否  1是  2异常
     * @methodName findCustomerByCode
     * @description
     * @params [object]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/25 10:50
     */
    @PostMapping({"/customer/newOrOld"})
    ResultEntity findCustomerByCode(Object object);


    /***
     *通过支付code 获取支付信息(主表)
     * @methodName findInfoByCode
     * @description
     * @params [object]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/18 10:30
     */
    @PostMapping({"/payConfig/findInfoByCode"})
    ResultEntity findInfoByCode(Object object);

    /**
     * @param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryInvoiceByStore
     * @description 获取当前机构是否配置发票信息
     * <AUTHOR>
     * @date 2020/6/16 11:03
     */
    @PostMapping({"/invoiceAuthorization/queryInvoiceByStore"})
    ResultEntity queryInvoiceByStore(Object object);

    @PostMapping("/store/queryStoreList")
    ResultEntity queryStoreList(Object object);

    /**
     * 查询公司的隶属公司（不过权限）
     * @param object
     * @return
     */
    @PostMapping("/store/queryUpStoreOptionUnPermisson")
    ResultEntity queryUpStoreOptionUnPermisson(Object object);

    /**
     * 查询银联商户B扫C终端
     * @param object
     * @return
     */
    @PostMapping("/payStoreTerminal/getPayStoreTerminalByStoreCode")
    ResultEntity getPayStoreTerminalByStoreCode(Object object);
}

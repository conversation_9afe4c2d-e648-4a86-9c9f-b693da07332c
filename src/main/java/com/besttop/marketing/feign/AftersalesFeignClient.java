package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name="e-msr-aftersales", url="http://e-msr-aftersales")
public interface AftersalesFeignClient {

    @PostMapping("/serviceBill/findBySourceCodes")
    ResultEntity findBySourceCodes(Object object);

    @PostMapping("/serviceBill/updateTerminal")
    ResultEntity updateTerminal(Object object);

    @PostMapping("/serviceBill/updateCancelTerminal")
    ResultEntity updateCancelTerminal(Object object);

    @PostMapping("/troubleRecord/isSubscribe")
    ResultEntity isSubscribe(Object object);
    
    @PostMapping("/serviceBill/queryServiceByOrder")
    ResultEntity queryServiceByOrder(Object object);
    
    @PostMapping("/serviceBill/updateServiceByOrder")
    ResultEntity updateServiceByOrder(Object object);
}

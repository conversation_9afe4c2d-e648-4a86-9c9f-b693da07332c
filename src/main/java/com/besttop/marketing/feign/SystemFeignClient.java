package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
//localhost:8080//http://e-msr-system
//@FeignClient(name = "e-msr-system", url = "http://e-msr-system")
@FeignClient(name = "e-msr-system", url = "localhost:8083")
public interface SystemFeignClient {

    @PostMapping("/sys/value/findCodeLength")
    ResultEntity findCodeLength(Object object);

    @PostMapping("/sys/value/findOption")
    ResultEntity findOption(Object object);

    @PostMapping("/sys/value/findMaxKey")
    ResultEntity findMaxKey(Object object);

}

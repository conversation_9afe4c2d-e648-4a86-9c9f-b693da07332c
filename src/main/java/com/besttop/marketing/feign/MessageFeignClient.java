package com.besttop.marketing.feign;

import com.besttop.common.model.ResultEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "e-msr-message", url = "http://e-msr-message")
public interface MessageFeignClient {

    @PostMapping("/message/sendMsg")
    ResultEntity sendMsg(Object object);

    @PostMapping("/message/setMessageTop")
    ResultEntity setMessageTop(Object object);

    /**
     * Description: 短信
     *
     * <AUTHOR>
     * @date: 2021-01-28 17:10
     * @param:[object]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/message/sendSmsMessage")
    ResultEntity sendSmsMessage(Object object);

    /**
     * Description:微信推送
     *
     * <AUTHOR>
     * @date: 2021-01-28 17:10
     * @param:[object]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/message/sendWeChatMessage")
    ResultEntity sendWeChatMessage(Object object);
}

package com.besttop.marketing.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 抖音SPI消息队列配置类
 * 用于配置抖音SPI日志和记录的异步处理队列
 */
@Configuration
@Slf4j
public class DouyinSpiQueueConfig {

    // 交换机名称
    public static final String EXCHANGE_DOUYIN_SPI = "douyin.spi.exchange";
    
    // 队列名称
    public static final String QUEUE_SPI_LOG = "douyin.spi.log";
    public static final String QUEUE_SPI_RECORD = "douyin.spi.record";
    
    // 路由键
    public static final String ROUTING_KEY_LOG = "douyin.spi.log";
    public static final String ROUTING_KEY_RECORD = "douyin.spi.record";
    
    /**
     * 抖音SPI主题交换机
     */
    @Bean
    public TopicExchange douyinSpiExchange() {
        TopicExchange exchange = new TopicExchange(EXCHANGE_DOUYIN_SPI, true, false);
        log.info("初始化抖音SPI交换机: {}", EXCHANGE_DOUYIN_SPI);
        return exchange;
    }
    
    /**
     * 抖音SPI日志队列
     */
    @Bean
    public Queue douyinSpiLogQueue() {
        Queue queue = new Queue(QUEUE_SPI_LOG, true);
        log.info("初始化抖音SPI日志队列: {}", QUEUE_SPI_LOG);
        return queue;
    }
    
    /**
     * 抖音SPI记录队列
     */
    @Bean
    public Queue douyinSpiRecordQueue() {
        Queue queue = new Queue(QUEUE_SPI_RECORD, true);
        log.info("初始化抖音SPI记录队列: {}", QUEUE_SPI_RECORD);
        return queue;
    }
    
    /**
     * 绑定抖音SPI日志队列到交换机
     */
    @Bean
    public Binding douyinSpiLogBinding() {
        Binding binding = BindingBuilder.bind(douyinSpiLogQueue())
                .to(douyinSpiExchange())
                .with(ROUTING_KEY_LOG);
        log.info("绑定抖音SPI日志队列到交换机: {} -> {}", QUEUE_SPI_LOG, EXCHANGE_DOUYIN_SPI);
        return binding;
    }
    
    /**
     * 绑定抖音SPI记录队列到交换机
     */
    @Bean
    public Binding douyinSpiRecordBinding() {
        Binding binding = BindingBuilder.bind(douyinSpiRecordQueue())
                .to(douyinSpiExchange())
                .with(ROUTING_KEY_RECORD);
        log.info("绑定抖音SPI记录队列到交换机: {} -> {}", QUEUE_SPI_RECORD, EXCHANGE_DOUYIN_SPI);
        return binding;
    }
} 
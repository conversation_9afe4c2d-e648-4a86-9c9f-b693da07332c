package com.besttop.marketing.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 抖音Pro版配置类
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@Component
@ConfigurationProperties(prefix = "douyin.pro")
public class DouyinProConfig {

    /**
     * 验券撤销时间限制（秒），默认1小时
     */
    private Long certificateVerifyTimeout = 3600L;

    /**
     * 验券重试次数，默认3次
     */
    private Integer certificateVerifyRetryTimes = 3;

    /**
     * 验券重试间隔（毫秒），默认5秒
     */
    private Long certificateVerifyRetryInterval = 5000L;

    /**
     * 退款审核超时时间（秒），默认24小时
     */
    private Long refundAuditTimeout = 86400L;

    /**
     * 退款审核超时自动通过，默认true
     */
    private Boolean refundAuditAutoApprove = true;

    /**
     * 验券幂等性检查有效期（秒），默认10分钟
     */
    private Long verifyIdempotentTimeout = 600L;

    /**
     * 撤销核销幂等性检查有效期（秒），默认10分钟
     */
    private Long cancelIdempotentTimeout = 600L;

    /**
     * 批量处理最大数量，默认100
     */
    private Integer batchProcessMaxSize = 100;

    /**
     * 异步处理线程池核心线程数，默认5
     */
    private Integer asyncProcessCorePoolSize = 5;

    /**
     * 异步处理线程池最大线程数，默认20
     */
    private Integer asyncProcessMaxPoolSize = 20;

    /**
     * 异步处理线程池队列容量，默认1000
     */
    private Integer asyncProcessQueueCapacity = 1000;

    /**
     * 获取验券撤销时间限制（毫秒）
     * 
     * @return 时间限制毫秒数
     */
    public Long getCertificateVerifyTimeoutMillis() {
        return certificateVerifyTimeout * 1000;
    }

    /**
     * 获取退款审核超时时间（毫秒）
     * 
     * @return 超时时间毫秒数
     */
    public Long getRefundAuditTimeoutMillis() {
        return refundAuditTimeout * 1000;
    }

    /**
     * 获取验券幂等性检查有效期（毫秒）
     * 
     * @return 有效期毫秒数
     */
    public Long getVerifyIdempotentTimeoutMillis() {
        return verifyIdempotentTimeout * 1000;
    }

    /**
     * 获取撤销核销幂等性检查有效期（毫秒）
     * 
     * @return 有效期毫秒数
     */
    public Long getCancelIdempotentTimeoutMillis() {
        return cancelIdempotentTimeout * 1000;
    }
}
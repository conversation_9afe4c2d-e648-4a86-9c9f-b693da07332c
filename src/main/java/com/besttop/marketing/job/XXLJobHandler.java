package com.besttop.marketing.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.besttop.common.model.ResultEntity;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.marketing.feign.InvoiceFeignClient;
import com.besttop.marketing.feign.OrderFeignClient;
import com.besttop.marketing.mapper.common.DaoMapper;
import com.besttop.marketing.mapper.shopping.ShoppingOrderInvoiceMapper;
import com.besttop.marketing.mapper.shopping.ShoppingOrderMapper;
import com.besttop.marketing.model.customer.CustomerAppraise;
import com.besttop.marketing.model.pmall.query.PmallPullOrderRequest;
import com.besttop.marketing.model.shopping.ShoppingOrderInvoice;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponRule;
import com.besttop.marketing.model.thirdparty.nuonuo.param.NuoNuoElecronicInvoiceInfo;
import com.besttop.marketing.service.customer.CustomerAppraiseService;
import com.besttop.marketing.service.old.OldfornewSkuDetailService;
import com.besttop.marketing.service.pmall.PmallService;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import com.besttop.marketing.service.thirdparty.NuoNuoInvoiceService;
import com.besttop.marketing.util.*;
import com.besttop.model.BaseJobHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.besttop.marketing.mapper.thirdparty.douyin.DouyinAuthMapper;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCouponMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinAuth;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.service.thirdparty.douyin.DouyinAuthService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponRuleService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSyncService;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Component
@Slf4j
public class XXLJobHandler extends BaseJobHandler {

    @Autowired
    private ShoppingOrderMapper shoppingOrderMapper;
    @Autowired
    private ShoppingOrderService shoppingOrderService;
    @Autowired
    private ErpBaseUtil erpBaseUtil;
    @Autowired
    private CustomerAppraiseService customerAppraiseService;
    @Autowired
    private ShoppingOrderInvoiceMapper orderInvoiceMapper;
    @Autowired
    private InvoiceFeignClient invoiceFeignClient;
//    private NuoNuoInvoiceService nuoNuoInvoiceService;
    @Autowired
    private DaoMapper daoMapper;
    @Autowired
    private OrderFeignClient orderFeignClient;
    @Autowired
    private OldfornewSkuDetailService oldfornewSkuDetailService;
    @Autowired
    private PmallService pmallService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private DouyinSyncService douyinSyncService;
    @Autowired
    private DouyinCouponMapper douyinCouponMapper;
    @Autowired
    private DouyinAuthMapper douyinAuthMapper;
    @Autowired
    private DouyinAuthService douyinAuthService;
    @Autowired
    private DouyinCouponRuleService douyinCouponRuleService;

    @XxlJob(value = "deleteUnPayOrder")
    public ReturnT deleteUnPayOrder(String param) {
        log.info("----------->>deleteUnPayOrder----------->>定时任务删除未支付订单");
        XxlJobLogger.log("定时任务删除未支付订单，开始执行");
        try {
            //过期时间 分
            long expireTime = Long.valueOf(erpBaseUtil.findOutType("erp:erp_marketing_shopping_order").get(0).get("value").toString());
            //查询待结算订单
            List<String> orderCodes = shoppingOrderMapper.queryDelOrderCodes(expireTime);
            // 查询待结算订单中被别的订单依赖(返/赠币了且被别的订单用了)的订单号
            String dependentOrderCodes = this.shoppingOrderMapper.queryDependentOrderCodes(orderCodes);
            log.info("待删除订单号=====" + JSON.toJSONString(orderCodes));
            if (CollectionUtil.isNotEmpty(orderCodes)) {
                // 过滤掉被依赖的订单
                List<String> newOrderCodes = orderCodes;
                if(StringUtils.isNotBlank(dependentOrderCodes)) {
                    newOrderCodes = orderCodes.stream().filter(o -> !dependentOrderCodes.contains(o)).collect(Collectors.toList());
                }
                for (String newOrderCode : newOrderCodes) {
                    OrderQueryParam p = new OrderQueryParam();
                    try {
                        p.setCode(newOrderCode);
                        log.info("删除中=====" + newOrderCode);
                        shoppingOrderService.deleteOrder(p);
                    } catch (Exception e) {
                        log.error("删除订单失败："+newOrderCode+",继续下一个",e);
                    }finally {
                        if (ObjectUtils.isNotEmpty(p.getStoreSkuInventoryListKey())) {
                            redisLockUtil.releaseBatchLock(p.getStoreSkuInventoryListKey());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("----------->>deleteUnPayOrder----------->>定时任务删除未支付订单失败" + JSON.toJSON(e.getStackTrace()));
            e.printStackTrace();
            XxlJobLogger.log("定时任务删除未支付订单， 失败：" + e.getMessage()+"==="+JSON.toJSON(e.getStackTrace()));
            return FAIL;
        }
        log.info("----------->>定时任务删除未支付订单，成功完成");
        XxlJobLogger.log("定时任务删除未支付订单，成功完成");
        return SUCCESS;
    }

    @XxlJob(value = "appraisePayOrder")
    public ReturnT appraisePayOrder(String param) {
        log.info("----------->>appraisePayOrder----------->>定时任务评价支付订单");
        XxlJobLogger.log("定时任务评价支付订单，开始执行");
        try {
            List<Map<String, Object>> data = erpBaseUtil.findOutType("erp:customer_appraise_days");

            Long expireTime = 0L;
            Integer orderScore = 0;

            for (Map<String, Object> d : data) {
                if (d.get("redisKey").toString().equals("erp:customer_appraise_days:1")) {
                    expireTime = Long.valueOf(d.get("value").toString());
                }
                if (d.get("redisKey").toString().equals("erp:customer_appraise_days:2")) {
                    orderScore = Integer.parseInt(d.get("value").toString());
                }
            }
            //默认值
            List<Map<String, Object>> comment = Lists.newArrayList();
            Map<String, Object> m1 = Maps.newHashMap();
            m1.put("id", 1);
            m1.put("orderScore", orderScore);
            m1.put("name", "售前服务");

            Map<String, Object> m2 = Maps.newHashMap();
            m2.put("id", 2);
            m2.put("orderScore", orderScore);
            m1.put("name", "送货速度");

            comment.add(m1);
            comment.add(m2);
            //过期时间 天
//            Long expireTime = Long.valueOf(erpBaseUtil.findOutType("erp:customer_appraise_days").get(0).get("value").toString());
            //查询未评价订单
            List<Map<String, String>> result = shoppingOrderMapper.queryAppraiseOrderCodes(expireTime);
            log.info("待评价订单号=====" + JSON.toJSONString(result));
            if (CollectionUtil.isNotEmpty(result)) {
                Integer finalOrderScore = orderScore;
                result.forEach(order -> {
                    if (StringUtils.isBlank(order.get("isAppraise"))) {
                        CustomerAppraise ca = new CustomerAppraise();
                        ca.setCustomerCode(order.get("customerCode"));
                        ca.setSourceCode(order.get("code"));
                        ca.setOrderScore(finalOrderScore);
                        ca.setOptType("1");
                        ca.setComment(JSON.toJSONString(comment));
                        ca.setCreateTime(new Date());
                        ca.setCreateBy("0000-系统默认");
                        ca.setNote("1");
                        ca.setIsDefault(1);
                        log.info("评价中=====" + order);
                        customerAppraiseService.addCustomerAppraise(ca);
                    }
                });
            }
        } catch (Exception e) {
            log.info("----------->>appraisePayOrder----------->>定时任务评价支付订单失败" + e.getMessage());
            e.printStackTrace();
            XxlJobLogger.log("定时任务评价支付订单， 失败：" + e.getMessage());
            return FAIL;
        }
        log.info("----------->>定时评价支付订单，成功完成");
        XxlJobLogger.log("定时任务评价支付订单，成功完成");
        return SUCCESS;
    }


    @XxlJob(value = "updateInvoiceInfo")
    public ReturnT updateInvoiceInfo(String param) {
        log.info("----------->>updateInvoiceInfo----------->>定时任务更新发票信息");
        XxlJobLogger.log("定时任务定时任务更新发票信息，开始执行");
        try {
            List<ShoppingOrderInvoice> invoiceList = orderInvoiceMapper.queryInvoiceInfo();
            if (CollectionUtil.isNotEmpty(invoiceList)) {
                for (ShoppingOrderInvoice invoice : invoiceList) {
                    NuoNuoElecronicInvoiceInfo info = new NuoNuoElecronicInvoiceInfo();
                    info.setInvoiceSerialNum(invoice.getInvoiceSerialNum());
                    info.setStoreCode(invoice.getStoreCode());
                    log.info("封装invoice主数据 NuoNuoElecronicInvoiceInfo={}",JSON.toJSON(info));
                    ResultEntity<JSONObject> resultEntity = invoiceFeignClient.queryInvoiceResult(info);
                    if (resultEntity != null && resultEntity.getFlag() == 1) {
                        Map map = resultEntity.getData();
                        log.info(invoice.getInvoiceSerialNum() + "查询结果返回：" + JSON.toJSONString(map));
                        log.info("开票结果状态" + map.get("status"));
                        if (String.valueOf(map.get("status")).equals("2")) {
                            log.info("开始更新发票信息");
                            invoice.setNumber(String.valueOf(map.get("invoiceNo")));
                            invoice.setCode(String.valueOf(map.get("invoiceCode")));
                            invoice.setMachineCode(String.valueOf(map.get("machineCode")));
                            invoice.setPdfUrl(String.valueOf(map.get("pdfUrl")));
                            invoice.setImgUrls(String.valueOf(map.get("imgUrls")));
                            invoice.setChecker(String.valueOf(map.get("checker")));
                            invoice.setClerk(String.valueOf(map.get("clerk")));
                            invoice.setAllElectronicInvoiceNumber(String.valueOf(map.get("allElectronicInvoiceNumber")));
                            invoice.setInvoiceData(JSON.toJSONString(map));
                            orderInvoiceMapper.updateById(invoice);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.info("----------->>updateInvoiceInfo----------->>定时任务更新发票信息失败" + e.getMessage());
            e.printStackTrace();
            XxlJobLogger.log("定时任务更新发票信息， 失败：" + e.getMessage());
            return FAIL;
        }
        log.info("----------->>定时更新发票信息，成功完成");
        XxlJobLogger.log("定时任务更新发票信息，成功完成");
        return SUCCESS;
    }
    /**
     * @description:  触发存储过程
     * @param: param
     * @return: com.xxl.job.core.biz.model.ReturnT
     * <AUTHOR>
     * @date: 2021/11/17 10:46 上午
     */
    @XxlJob(value = "callStoredProcedure")
    public ReturnT callStoredProcedure(String param) {
        log.info("----------->>callStoredProcedure----------->>定时任务触发存储过程");
        XxlJobLogger.log("定时任务触发存储过程，开始执行");
        try {
            daoMapper.call(param);
        } catch (Exception e) {
            log.info("----------->>callStoredProcedure----------->>定时任务触发存储过程失败{},{}" , e.getMessage(),JSON.toJSONString(e.getStackTrace()));
            XxlJobLogger.log("定时任务触发存储过程， 失败：{},{}" ,e.getMessage(), JSON.toJSONString(e.getStackTrace()));
            return FAIL;
        }
        log.info("----------->>定时任务触发存储过程，成功完成");
        XxlJobLogger.log("定时任务触发存储过程，成功完成");
        return SUCCESS;
    }


    /**
     * @description:  触发积分过期
     * @param: param
     * @return: com.xxl.job.core.biz.model.ReturnT
     * <AUTHOR>
     * @date: 2021/11/17 10:46 上午
     */
    @XxlJob(value = "integralExpire")
    public ReturnT integralExpire(String param) {
        log.info("----------->>integralExpire----------->>触发积分过期");
        XxlJobLogger.log("触发积分过期，开始执行");
        try {
            orderFeignClient.expire(param);
        } catch (Exception e) {
            log.info("----------->>integralExpire----------->>定时任务触发积分过期失败{},{}" , e.getMessage(),JSON.toJSONString(e.getStackTrace()));
            XxlJobLogger.log("定时任务触发积分过期， 失败：{},{}" ,e.getMessage(), JSON.toJSONString(e.getStackTrace()));
            return FAIL;
        }
        log.info("----------->>定时任务触发积分过期，成功完成");
        XxlJobLogger.log("定时任务触发积分过期，成功完成");
        return SUCCESS;
    }


    @XxlJob(value = "cancelOldfornewSkuDetailByHour")
    public ReturnT cancelOldfornewSkuDetailByHour(String param) {
        log.info("----------->>cancelOldfornewSkuDetailByHour----------->>触发作废以旧换新审核单");
        XxlJobLogger.log("触发作废以旧换新审核单，开始执行");
        try {
            oldfornewSkuDetailService.updateAutoCancel(param);
        } catch (Exception e) {
            log.info("----------->>cancelOldfornewSkuDetailByHour----------->>定时任务触发作废以旧换新审核单失败{},{}" , e.getMessage(),JSON.toJSONString(e.getStackTrace()));
            XxlJobLogger.log("定时任务触发作废以旧换新审核单， 失败：{},{}" ,e.getMessage(), JSON.toJSONString(e.getStackTrace()));
            return FAIL;
        }
        log.info("----------->>定时任务触发作废以旧换新审核单，成功完成");
        XxlJobLogger.log("定时任务触发作废以旧换新审核单，成功完成");
        return SUCCESS;
    }

    // 定时拉去pmall订单
    @XxlJob(value = "pullOrderPmall")
    public ReturnT pullPmallOrder(String param) {

        CheckUtil.notEmpty(param,"日期区间不能为空");
        log.info("----------->>pullOrderPmall----------->>定时拉去pmall订单 param{}",param);
        XxlJobLogger.log("定时拉去pmall订单，开始执行");

        try {
            PmallPullOrderRequest request = new PmallPullOrderRequest();
           // 每次拉取订单的开始时间要比任务间隔时间多5分钟，以免漏单
            Date currentTime = new Date();
            Date startTime = org.apache.commons.lang3.time.DateUtils.addMinutes(currentTime, -(Integer.parseInt(param)+5));
            request.setUpdate_time_start(DateUtils.date2String(startTime));
            request.setUpdate_time_end(DateUtils.date2String(currentTime));
            pmallService.pullOrderPmall(request);
        } catch (Exception e) {
            log.info("----------->>pullOrderPmall----------->>定时拉去pmall订单失败{},{}" , e.getMessage(),JSON.toJSONString(e.getStackTrace()));
            XxlJobLogger.log("定时拉去pmall订单， 失败：{},{}" ,e.getMessage(), JSON.toJSONString(e.getStackTrace()));
            return FAIL;
        }

        log.info("----------->>定时拉去pmall订单，成功完成");
        XxlJobLogger.log("定时拉去pmall订单，成功完成");
        return SUCCESS;
    }

    /**
     * 抖音券核销同步任务  每10分钟执行一次
     */
    @XxlJob(value = "douyinSyncVerificationStatus")
    public ReturnT douyinSyncVerificationStatus(String param) {
        XxlJobLogger.log("开始同步抖音券核销状态...");
        
        // 1. 查找所有待同步或同步失败的核销记录
        List<DouyinCoupon> couponsToSync = douyinCouponMapper.selectList(
            new LambdaQueryWrapper<DouyinCoupon>()
                .eq(DouyinCoupon::getCouponStatus, "erp:douyin_coupon_status:1") // 已使用
                .in(DouyinCoupon::getVerificationSyncStatus, "erp:douyin_sync_status:0", "erp:douyin_sync_status:2") // 待同步或同步失败
                .lt(DouyinCoupon::getLastVerificationSyncAttempt, LocalDateTime.now().minusMinutes(5)) // 5分钟重试
        );
        
        if (couponsToSync.isEmpty()) {
            XxlJobLogger.log("没有需要同步的抖音券核销状态。");
            return SUCCESS;
        }
        
        int successCount = 0;
        int failureCount = 0;
        
        for (DouyinCoupon coupon : couponsToSync) {
            try {
                // 调用同步服务
                DouyinCouponRule rule = douyinCouponRuleService.getById(coupon.getCouponRuleId());
                boolean success = douyinSyncService.syncVerificationStatus(coupon, rule);
                if (success) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                failureCount++;
                XxlJobLogger.log("同步券核销状态失败, couponId: {}, error: {}", coupon.getId(), e.getMessage());
            }
        }
        
        XxlJobLogger.log("抖音券核销状态同步完成。成功: {}, 失败: {}", successCount, failureCount);
        return SUCCESS;
    }
    

    /**
     * 抖音授权token刷新任务 每小时执行一次
     */
    @XxlJob(value = "douyinRefreshAuthTokens")
    public ReturnT douyinRefreshAuthTokens(String param) {
        XxlJobLogger.log("开始刷新抖音授权令牌...");
        log.info("----------->>douyinRefreshAuthTokens----------->>定时刷新抖音授权token");
        
        try {
            // 查询所有授权信息
            List<DouyinAuth> authList = douyinAuthMapper.selectAll();
            
            Date threshold = DateUtils.plusHours(2);
            int successCount = 0;
            for (DouyinAuth auth : authList) {
                try {
                    // 检查是否需要刷新
                    if (auth.getAccessTokenExpireTime().before(threshold)) {
                        boolean result = douyinAuthService.refreshAuthToken();
                        if (result) {
                            successCount++;
                            log.info("成功刷新AccessToken.");
                        }
                    }
                } catch (Exception e) {
                    log.error("刷新token异常", e);
                    XxlJobLogger.log("刷新token异常: error={}",  e.getMessage());
                }
            }
            XxlJobLogger.log("成功刷新{}张抖音授权token", successCount);
        } catch (Exception e) {
            log.error("----------->>douyinRefreshAuthTokens----------->>定时刷新抖音授权token异常", e);
            XxlJobLogger.log("定时刷新抖音授权token异常：{}", e.getMessage());
            return FAIL;
        }
        
        log.info("----------->>定时刷新抖音授权token，成功完成");
        XxlJobLogger.log("定时刷新抖音授权token，成功完成");
        return SUCCESS;
    }

}
package com.besttop.marketing.dto.douyin.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 抖音退款审核回调请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data

public class DouyinRefundAuditRequest {

    /** 退款审核结果. 1:允许,2:拒绝 */
    @JsonProperty("result")
    @NotNull(message = "审核结果不能为空")
    private Integer result;

    /** 售后ID */
    @JsonProperty("after_sale_id")
    private String afterSaleId;

    /** 抖音内部券码信息列表 */
    @JsonProperty("certificate")
    private List<Certificate> certificate;

    /** 代表一张券码的标识(发起退款申请时给出) */
    @JsonProperty("certificate_id")
    private String certificateId;

    /** 审核拒绝并且发码失败情况下需要返回对应code */
    @JsonProperty("code")
    private String code;

    /** 退款审核拒绝时，拒绝原因必填 */
    @JsonProperty("reason")
    private String reason;

    /** 补码凭证信息json（仅景区场景使用） */
    @JsonProperty("voucher")
    private String voucher;

    /**
     * 券码信息
     */
    @Data

    public static class Certificate {
        /** 券码标识 */
        @JsonProperty("certificate_id")
        private String certificateId;

        /** 券码 */
        @JsonProperty("code")
        private String code;

        /** 补码凭证信息 */
        @JsonProperty("voucher")
        private String voucher;

        /** 补码信息 */
        @JsonProperty("patch_info")
        private PatchInfo patchInfo;
    }

    /**
     * 补码信息
     */
    @Data

    public static class PatchInfo {
        /** 补码类型 */
        @JsonProperty("patch_type")
        private Long patchType;

        /** 补码内容 */
        @JsonProperty("content")
        private String content;
    }

    /**
     * 审核结果枚举
     */
    public enum AuditResult {
        ALLOW(1, "允许"),
        REJECT(2, "拒绝");

        private final Integer code;
        private final String desc;

        AuditResult(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
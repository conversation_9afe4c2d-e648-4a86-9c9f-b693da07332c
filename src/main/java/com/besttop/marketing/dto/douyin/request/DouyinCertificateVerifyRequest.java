package com.besttop.marketing.dto.douyin.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 抖音验券请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data

public class DouyinCertificateVerifyRequest {

    /** 一次验券的标识(用于短时间内的幂等) */
    @JsonProperty("verify_token")
    @NotBlank(message = "验券标识不能为空")
    private String verifyToken;

    /** 核销的抖音门店id */
    @JsonProperty("poi_id")
    @NotBlank(message = "门店ID不能为空")
    private String poiId;

    /** 核销商户根账户ID（云连锁场景接入需传入，其余场景可不传） */
    @JsonProperty("account_id")
    private String accountId;

    /** 验券准备接口返回的加密抖音券码 */
    @JsonProperty("encrypted_codes")
    private List<String> encryptedCodes;

    /** 三方原始券码值列表 */
    @JsonProperty("codes")
    private List<String> codes;

    /** 抖音侧的订单号(非预导码模式的三方券码必需) */
    @JsonProperty("order_id")
    private String orderId;

    private Long bizTime;

    private Long actualDeductionAmount;

    private String accessToken;

    /** 带有核销时间的三方码列表 */
    @JsonProperty("code_with_time_list")
    private List<CodeWithTime> codeWithTimeList;

    /** 验签 */
    @JsonProperty("verify_sign_list")
    private List<String> verifySignList;

    /** 多项目多凭证的情况(景区预售券核销必传) */
    @JsonProperty("voucher")
    private Voucher voucher;

    /** 针对团购景区套票 */
    @JsonProperty("vouchers")
    private List<Voucher> vouchers;

    /** 核销额外参数 */
    @JsonProperty("verify_extra")
    private VerifyExtra verifyExtra;


    /**
     * 带有核销时间的三方码
     */
    @Data
    public static class CodeWithTime {
        /** 三方码 */
        @JsonProperty("code")
        private String code;

        /** 核销时间戳（秒） */
        @JsonProperty("verify_time")
        private Long verifyTime;

        public CodeWithTime(String s, long l) {
        }
    }

    /**
     * 凭证信息
     */
    @Data

    public static class Voucher {
        /** 项目唯一标识 */
        @JsonProperty("project_id")
        private String projectId;

        /** 身份证号码 */
        @JsonProperty("id_card_list")
        private List<String> idCardList;

        /** 二维码 */
        @JsonProperty("qrcode_list")
        private List<String> qrcodeList;

        /** 券号 */
        @JsonProperty("certificate_no_list")
        private List<String> certificateNoList;

        /** 核销时间戳（秒） */
        @JsonProperty("verify_time")
        private Long verifyTime;
    }

    /**
     * 核销额外参数
     */
    @Data

    public static class VerifyExtra {
        /** 整体核销标识 */
        @JsonProperty("total_verify")
        private Boolean totalVerify;

        /** 外部货号 */
        @JsonProperty("out_good_ids")
        private List<String> outGoodIds;

        /** 动态代金券额外参数 */
        @JsonProperty("dynamic_coupon_info")
        private DynamicCouponInfo dynamicCouponInfo;
    }

    /**
     * 动态代金券额外参数
     */
    @Data

    public static class DynamicCouponInfo {
        /** 开台时间（秒） */
        @JsonProperty("biz_time")
        private Long bizTime;

        /** 实际抵扣金额 */
        @JsonProperty("actual_deduction_amount")
        private Long actualDeductionAmount;
    }
}
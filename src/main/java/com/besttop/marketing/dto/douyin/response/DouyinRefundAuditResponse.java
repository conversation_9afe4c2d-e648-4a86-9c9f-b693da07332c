package com.besttop.marketing.dto.douyin.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 抖音退款审核回调响应结果
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data

public class DouyinRefundAuditResponse {

    /** 响应数据 */
    @JsonProperty("data")
    private AuditData data;

    /** 扩展信息 */
    @JsonProperty("extra")
    private Extra extra;

    /**
     * 审核响应数据
     */
    @Data

    public static class AuditData {
        /** 错误码，0为成功 */
        @JsonProperty("error_code")
        private Integer errorCode;

        /** 错误码描述 */
        @JsonProperty("description")
        private String description;
    }

    /**
     * 扩展信息
     */
    @Data

    public static class Extra {
        /** 错误码 */
        @JsonProperty("error_code")
        private Integer errorCode;

        /** 错误描述 */
        @JsonProperty("description")
        private String description;

        /** 子错误码 */
        @JsonProperty("sub_error_code")
        private Integer subErrorCode;

        /** 子错误描述 */
        @JsonProperty("sub_description")
        private String subDescription;

        /** 日志ID */
        @JsonProperty("logid")
        private String logid;

        /** 当前时间戳 */
        @JsonProperty("now")
        private Long now;
    }
}
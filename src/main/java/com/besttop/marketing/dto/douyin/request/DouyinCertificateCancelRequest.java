package com.besttop.marketing.dto.douyin.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 抖音撤销核销请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data

public class DouyinCertificateCancelRequest {

    /** 代表一张券码的标识(验券时返回) */
    @JsonProperty("certificate_id")
    @NotBlank(message = "券码标识不能为空")
    private String certificateId;

    /** 代表券码一次核销的唯一标识(验券时返回) */
    @JsonProperty("verify_id")
    @NotBlank(message = "核销标识不能为空")
    private String verifyId;

    /** 核销商户根账户ID（云连锁场景接入需传入，其余场景可不传） */
    @JsonProperty("account_id")
    private String accountId;

    /** 批量撤销某个订单下的一批验券记录 */
    @JsonProperty("batch_cancel_info")
    private BatchCancelInfo batchCancelInfo;

    /** 批量撤销多个订单下的一批验券记录 */
    @JsonProperty("batch_cancel_info_list")
    private List<BatchCancelInfo> batchCancelInfoList;

    /** 撤销核销幂等操作 */
    @JsonProperty("cancel_token")
    private String cancelToken;

    /** 所属的订单id */
    @JsonProperty("shop_order_id")
    private String shopOrderId;

    /** 取消核销总次数（多次卡商品可传） */
    @JsonProperty("times_card_cancel_count")
    private Long timesCardCancelCount;

    /**
     * 批量撤销信息
     */
    @Data

    public static class BatchCancelInfo {
        /** 订单ID */
        @JsonProperty("order_id")
        private String orderId;

        /** 验券ID列表 */
        @JsonProperty("verify_id_list")
        private List<String> verifyIdList;
    }
}
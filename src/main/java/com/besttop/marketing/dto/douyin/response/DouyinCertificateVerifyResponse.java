package com.besttop.marketing.dto.douyin.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.util.List;

/**
 * 抖音验券响应结果
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data

public class DouyinCertificateVerifyResponse {

    /** 响应数据 */
    @JsonProperty("data")
    private VerifyData data;

    /** 扩展信息 */
    @JsonProperty("extra")
    private Extra extra;

    /**
     * 验券响应数据
     */
    @Data

    public static class VerifyData {
        /** 错误码，0为成功 */
        @JsonProperty("error_code")
        private Integer errorCode;

        /** 错误码描述 */
        @JsonProperty("description")
        private String description;

        /** 验券结果 */
        @JsonProperty("verify_results")
        private List<VerifyResult> verifyResults;
    }

    /**
     * 验券结果
     */
    @Data

    public static class VerifyResult {
        /** 验券结果码，0表示成功，非0表示失败 */
        @JsonProperty("result")
        private Integer result;

        /** 验券结果说明 */
        @JsonProperty("msg")
        private String msg;

        /** 代表验券传入的code或encrypted_code */
        @JsonProperty("code")
        private String code;

        /** 代表券码一次核销的标识 */
        @JsonProperty("verify_id")
        private String verifyId;

        /** 代表一张券码的标识 */
        @JsonProperty("certificate_id")
        private String certificateId;

        /** 核销时间戳 */
        @JsonProperty("verify_time")
        private Long verifyTime;

        /** 订单ID */
        @JsonProperty("order_id")
        private String orderId;

        /** 商品名称 */
        @JsonProperty("sku_name")
        private String skuName;

        /** 核销数量 */
        @JsonProperty("verify_count")
        private Integer verifyCount;

        /** 剩余可核销数量 */
        @JsonProperty("remain_count")
        private Integer remainCount;

        @JsonProperty("origin_code")
        private String originCode;
    }

    /**
     * 扩展信息
     */
    @Data

    public static class Extra {
        /** 错误码 */
        @JsonProperty("error_code")
        private Integer errorCode;

        /** 错误描述 */
        @JsonProperty("description")
        private String description;

        /** 子错误码 */
        @JsonProperty("sub_error_code")
        private Integer subErrorCode;

        /** 子错误描述 */
        @JsonProperty("sub_description")
        private String subDescription;

        /** 日志ID */
        @JsonProperty("logid")
        private String logid;

        /** 当前时间戳 */
        @JsonProperty("now")
        private Long now;
    }
}
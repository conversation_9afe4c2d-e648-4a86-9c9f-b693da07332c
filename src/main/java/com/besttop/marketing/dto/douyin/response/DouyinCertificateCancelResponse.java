package com.besttop.marketing.dto.douyin.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.util.List;

/**
 * 抖音撤销核销响应结果
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data

public class DouyinCertificateCancelResponse {

    /** 响应数据 */
    @JsonProperty("data")
    private CancelData data;

    /** 扩展信息 */
    @JsonProperty("extra")
    private Extra extra;

    /**
     * 撤销响应数据
     */
    @Data

    public static class CancelData {
        /** 错误码，0为成功 */
        @JsonProperty("error_code")
        private Integer errorCode;

        /** 错误码描述 */
        @JsonProperty("description")
        private String description;

        /** 事务ID */
        @JsonProperty("transaction_id")
        private Long transactionId;

        /** 撤销结果 */
        @JsonProperty("cancel_results")
        private List<CancelResult> cancelResults;
    }

    /**
     * 撤销结果
     */
    @Data

    public static class CancelResult {
        /** 验券ID */
        @JsonProperty("verify_id")
        private String verifyId;

        /** 结果码 */
        @JsonProperty("result_code")
        private Long resultCode;

        /** 结果消息 */
        @JsonProperty("result_msg")
        private String resultMsg;

        /** 订单ID */
        @JsonProperty("order_id")
        private String orderId;
    }

    /**
     * 扩展信息
     */
    @Data

    public static class Extra {
        /** 错误码 */
        @JsonProperty("error_code")
        private Integer errorCode;

        /** 错误描述 */
        @JsonProperty("description")
        private String description;

        /** 子错误码 */
        @JsonProperty("sub_error_code")
        private Integer subErrorCode;

        /** 子错误描述 */
        @JsonProperty("sub_description")
        private String subDescription;

        /** 日志ID */
        @JsonProperty("logid")
        private String logid;

        /** 当前时间戳 */
        @JsonProperty("now")
        private Long now;
    }
}
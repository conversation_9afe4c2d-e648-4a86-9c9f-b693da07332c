package com.besttop.marketing.dto.gift;

import lombok.Data;
import java.util.Date;
import java.util.Map;

/**
 * 抖音礼品券撤销核销结果DTO
 * 封装礼品券撤销操作的完整结果信息
 */
@Data
public class DouyinGiftCancelResult {
    
    /**
     * 是否撤销成功
     */
    private boolean success;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 券码
     */
    private String couponCode;
    
    /**
     * 撤销时间
     */
    private Date cancelTime;
    
    /**
     * 原核销记录ID
     */
    private String verificationRecordId;
    
    /**
     * 礼品发放单ID
     */
    private String giftRecordId;
    
    /**
     * 礼品类型
     */
    private String giftType;
    
    /**
     * 撤销后的券状态
     */
    private String couponStatus;
    
    /**
     * 撤销后的礼品发放单状态
     */
    private String giftRecordStatus;
    
    /**
     * 抖音撤销响应信息
     */
    private Map<String, Object> douyinResponse;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 创建成功结果
     */
    public static DouyinGiftCancelResult success(String couponCode, String message) {
        DouyinGiftCancelResult result = new DouyinGiftCancelResult();
        result.setSuccess(true);
        result.setCouponCode(couponCode);
        result.setMessage(message);
        result.setCancelTime(new Date());
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static DouyinGiftCancelResult fail(String couponCode, String errorCode, String message) {
        DouyinGiftCancelResult result = new DouyinGiftCancelResult();
        result.setSuccess(false);
        result.setCouponCode(couponCode);
        result.setErrorCode(errorCode);
        result.setMessage(message);
        result.setCancelTime(new Date());
        return result;
    }
    
    /**
     * 是否为电子币类型
     */
    public boolean isElectronicCoin() {
        return "A".equals(giftType);
    }
    
    /**
     * 是否为商品类型
     */
    public boolean isProduct() {
        return "B".equals(giftType) || "C".equals(giftType);
    }
}

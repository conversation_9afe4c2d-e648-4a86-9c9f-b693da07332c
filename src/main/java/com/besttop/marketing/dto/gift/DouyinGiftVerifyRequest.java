package com.besttop.marketing.dto.gift;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 抖音礼品券撤销核销请求DTO
 * 用于撤销已核销的礼品券
 */
@Data
public class DouyinGiftVerifyRequest {
    
    /**
     * 券码
     */
    @NotBlank(message = "券码不能为空")
    private String couponCode;
    
    /**
     * 门店编码
     */
    @NotBlank(message = "门店编码不能为空")
    private String storeCode;
    
    /**
     * 操作员ID
     */
    @NotBlank(message = "操作员ID不能为空")
    private String staffId;
    

   private String customerCode;


}

package com.besttop.marketing.dto.gift;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 抖音礼品券撤销核销请求DTO
 * 用于撤销已核销的礼品券
 */
@Data
public class DouyinGiftCancelRequest {
    
    /**
     * 券码
     */
    @NotBlank(message = "券码不能为空")
    private String couponCode;
    
    /**
     * 门店编码
     */
    @NotBlank(message = "门店编码不能为空")
    private String storeCode;
    
    /**
     * 操作员ID
     */
    @NotBlank(message = "操作员ID不能为空")
    private String staffId;
    
    /**
     * 撤销原因
     */
    private String cancelReason;
    
    /**
     * 撤销类型
     * MANUAL - 手动撤销
     * AUTO - 自动撤销
     */
    private String cancelType = "MANUAL";
    
    /**
     * 是否强制撤销（忽略时间限制）
     */
    private Boolean forceCancel = false;
    
    /**
     * 扩展参数
     */
    private String extParams;
}

package com.besttop.marketing.dto.gift;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 手动礼品发放记录创建DTO
 * 用于创建礼品发放单的数据传输对象
 */
@Data
public class ManualGiftRecordCreateDTO {
    
    /**
     * 客户手机号
     */
    @NotBlank(message = "客户手机号不能为空")
    private String customerPhone;
    
    /**
     * 门店编码
     */
    @NotBlank(message = "门店编码不能为空")
    private String storeCode;
    
    /**
     * 操作员ID
     */
    @NotBlank(message = "操作员ID不能为空")
    private String operatorId;
    
    /**
     * 来源系统
     */
    private String source;
    
    /**
     * 来源订单ID
     */
    private String sourceOrderId;
    
    /**
     * 礼品类型 (A/B/C)
     */
    @NotBlank(message = "礼品类型不能为空")
    private String giftType;
    
    /**
     * 金额（A类礼品-电子币）
     */
    private BigDecimal amount;
    
    /**
     * 账户类型（A类礼品）
     */
    private String accountType;
    
    /**
     * 商品编码（B/C类礼品）
     */
    private String productCode;
    
    /**
     * 商品名称（B/C类礼品）
     */
    private String productName;
    
    /**
     * 供应商编码（C类礼品）
     */
    private String supplierCode;
    
    /**
     * 数量（B/C类礼品）
     */
    private Integer quantity;
    
    /**
     * 配送方式
     */
    private String deliveryType;
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * 抖音券码（关联字段）
     */
    private String douyinCouponCode;
    
    /**
     * 抖音核销记录ID（关联字段）
     */
    private String douyinVerificationId;
    
    // 礼品类型常量
    public static final String GIFT_TYPE_A = "A"; // 电子币
    public static final String GIFT_TYPE_B = "B"; // 自营商品
    public static final String GIFT_TYPE_C = "C"; // 联营商品
    
    // 账户类型常量
    public static final String ACCOUNT_TYPE_ELECTRONIC_COIN = "ELECTRONIC_COIN";
    
    // 配送方式常量
    public static final String DELIVERY_TYPE_LOGISTICS = "LOGISTICS";        // 物流配送
    public static final String DELIVERY_TYPE_SUPPLIER_DIRECT = "SUPPLIER_DIRECT"; // 供应商直发
    public static final String DELIVERY_TYPE_STORE_PICKUP = "STORE_PICKUP";  // 门店自提
    
    /**
     * 验证DTO数据的完整性
     */
    public void validate() {
        switch (giftType) {
            case GIFT_TYPE_A:
                if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("A类礼品金额必须大于0");
                }
                if (accountType == null) {
                    accountType = ACCOUNT_TYPE_ELECTRONIC_COIN;
                }
                break;
                
            case GIFT_TYPE_B:
            case GIFT_TYPE_C:
                if (productCode == null || productCode.trim().isEmpty()) {
                    throw new IllegalArgumentException("B/C类礼品商品编码不能为空");
                }
                if (quantity == null || quantity <= 0) {
                    throw new IllegalArgumentException("B/C类礼品数量必须大于0");
                }
                if (giftType.equals(GIFT_TYPE_C) && (supplierCode == null || supplierCode.trim().isEmpty())) {
                    throw new IllegalArgumentException("C类礼品供应商编码不能为空");
                }
                break;
                
            default:
                throw new IllegalArgumentException("不支持的礼品类型: " + giftType);
        }
    }
    
    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (source == null) {
            source = "MANUAL";
        }
        if (deliveryType == null) {
            deliveryType = GIFT_TYPE_A.equals(giftType) ? null : 
                          GIFT_TYPE_C.equals(giftType) ? DELIVERY_TYPE_SUPPLIER_DIRECT : DELIVERY_TYPE_LOGISTICS;
        }
        if (quantity == null && !GIFT_TYPE_A.equals(giftType)) {
            quantity = 1;
        }
    }
}

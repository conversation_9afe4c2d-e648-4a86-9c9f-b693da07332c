package com.besttop.marketing.aspect;

import com.alibaba.fastjson.JSON;
import com.besttop.marketing.annotation.DouyinSpiRecord;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinSpiMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSpi;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSpiMessageService;
import com.power.common.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 抖音SPI调用记录切面
 * <p>
 * 拦截 @DouyinSpiRecord 注解，记录SPI调用的详细信息到 t_douyin_spi 表
 * 高并发场景下使用消息队列异步处理
 */
@Slf4j
@Aspect
@Component
public class DouyinSpiRecordAspect implements ApplicationContextAware {

    private final DouyinSpiMapper douyinSpiMapper;

    @Autowired
    private DouyinSpiMessageService messageService;

    private ApplicationContext applicationContext;

    public DouyinSpiRecordAspect(DouyinSpiMapper douyinSpiMapper) {
        this.douyinSpiMapper = douyinSpiMapper;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 检查Spring容器是否正在销毁
     */
    private boolean isApplicationContextDestroying() {
        try {
            if (applicationContext == null) {
                return true;
            }

            // 尝试获取一个Bean来检查容器状态
            // 如果容器正在销毁，这个操作会抛出异常
            applicationContext.getBean(DouyinSpiMessageService.class);
            return false;
        } catch (Exception e) {
            // 如果获取Bean失败，说明容器可能正在销毁
            log.debug("检测到Spring容器可能正在销毁: {}", e.getMessage());
            return true;
        }
    }

    @Around("@annotation(douyinSpiRecord)")
    public Object around(ProceedingJoinPoint joinPoint, DouyinSpiRecord douyinSpiRecord) throws Throwable {

        // 检查Spring容器状态，如果正在销毁则跳过记录
        if (isApplicationContextDestroying()) {
            log.warn("Spring容器正在销毁，跳过SPI记录");
            return joinPoint.proceed();
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.warn("无法获取请求上下文，跳过SPI记录");
            return joinPoint.proceed();
        }

        HttpServletRequest request = attributes.getRequest();

        Map<String, Object> recordParams = new HashMap<>();
        long startTime = System.currentTimeMillis();
        String messageId = null;

        try {
            // 1. 收集请求信息并发送到队列
            recordParams.put("spiType", douyinSpiRecord.spiType());
            recordParams.put("requestIp", IpUtil.getIpAddr(request));
            recordParams.put("requestHeaders", JSON.toJSONString(getRequestHeaders(request)));

            Map<String, Object> requestBody = getRequestBody(joinPoint);
            recordParams.put("requestParams", JSON.toJSONString(requestBody));

            if (requestBody != null && requestBody.containsKey(douyinSpiRecord.businessIdKey())) {
                recordParams.put("businessId", String.valueOf(requestBody.get(douyinSpiRecord.businessIdKey())));
            }

            // 异步发送记录消息
            messageId = messageService.sendRecordMessage(recordParams);
            log.debug("抖音SPI日志记录提交队列，消息ID: {}", messageId);

        } catch (Exception e) {
            log.error("抖音SPI日志记录前置处理异常", e);
            // 即使日志记录失败，也继续执行业务逻辑
        }

        try {
            // 2. 执行业务方法
            Object result = joinPoint.proceed();

            // 3. 记录响应信息（根据抖音官方格式判断成功/失败）
            try {
                if (messageId != null) {
                    Map<String, Object> updateParams = new HashMap<>();
                    updateParams.put("id", messageId);
                    updateParams.put("processResult", JSON.toJSONString(result));
                    updateParams.put("processTime", System.currentTimeMillis() - startTime);

                    // 根据抖音官方格式判断处理状态
                    int processStatus = determineProcessStatus(result);
                    updateParams.put("processStatus", processStatus);

                    // 如果是失败状态，提取错误信息
                    if (processStatus == 2) {
                        String errorMessage = extractErrorMessage(result);
                        updateParams.put("errorMessage", errorMessage);
                    }

                    // 异步发送更新消息
                    messageService.sendRecordUpdateMessage(updateParams);

                    log.debug("抖音SPI记录响应信息已发送: messageId={}, processStatus={}", messageId, processStatus);
                }
            } catch (Exception e) {
                log.error("抖音SPI日志记录响应信息时异常", e);
            }

            return result;
        } catch (Throwable throwable) {
            // 4. 记录异常信息
            try {
                if (messageId != null) {
                    Map<String, Object> updateParams = new HashMap<>();
                    updateParams.put("id", messageId);
                    updateParams.put("processStatus", 2); // 2: 处理失败
                    updateParams.put("errorMessage", throwable.getMessage());
                    updateParams.put("processTime", System.currentTimeMillis() - startTime);
                    
                    // 异步发送更新消息
                    messageService.sendRecordUpdateMessage(updateParams);
                }
            } catch (Exception e) {
                log.error("抖音SPI日志记录异常响应时异常", e);
            }
            // 抛出原始异常，确保事务回滚等正常进行
            throw throwable;
        }
    }

    /**
     * 从切点获取请求体参数
     */
    private Map<String, Object> getRequestBody(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof Map) {
                // 假设请求体是Map类型
                return (Map<String, Object>) arg;
            }
        }
        return null;
    }
    
    /**
     * 获取请求头信息
     */
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                headers.put(name.toLowerCase(), request.getHeader(name));
            }
        }
        return headers;
    }

    /**
     * 根据抖音官方格式判断处理状态
     * 抖音格式：{data: {error_code: 0, description: "success"}, extra: {...}}
     *
     * @param result 响应结果
     * @return 1: 处理成功, 2: 处理失败
     */
    @SuppressWarnings("unchecked")
    private int determineProcessStatus(Object result) {
        try {
            if (result instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) result;

                // 检查是否为抖音官方格式
                if (response.containsKey("data")) {
                    Map<String, Object> data = (Map<String, Object>) response.get("data");
                    if (data != null && data.containsKey("error_code")) {
                        Integer errorCode = (Integer) data.get("error_code");
                        // error_code = 0 表示成功，其他值表示失败
                        return (errorCode != null && errorCode == 0) ? 1 : 2;
                    }
                }
            }

            // 如果不是抖音格式或无法判断，默认认为成功
            return 1;

        } catch (Exception e) {
            log.warn("判断处理状态时异常，默认为成功: {}", e.getMessage());
            return 1;
        }
    }

    /**
     * 从抖音响应中提取错误信息
     *
     * @param result 响应结果
     * @return 错误信息
     */
    @SuppressWarnings("unchecked")
    private String extractErrorMessage(Object result) {
        try {
            if (result instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) result;

                if (response.containsKey("data")) {
                    Map<String, Object> data = (Map<String, Object>) response.get("data");
                    if (data != null) {
                        String description = (String) data.get("description");
                        Integer errorCode = (Integer) data.get("error_code");

                        if (description != null) {
                            return String.format("error_code: %s, description: %s", errorCode, description);
                        }
                    }
                }
            }

            return "未知错误";

        } catch (Exception e) {
            log.warn("提取错误信息时异常: {}", e.getMessage());
            return "提取错误信息失败: " + e.getMessage();
        }
    }
} 
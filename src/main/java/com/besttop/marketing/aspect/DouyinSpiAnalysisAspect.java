package com.besttop.marketing.aspect;

import com.alibaba.fastjson.JSON;
import com.besttop.marketing.annotation.DouyinSpiLog;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSpiAnalysisLog;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSpiAnalysisService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSpiMessageService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;

/**
 * 抖音SPI业务分析日志切面
 * 自动记录SPI接口的调用情况和业务分析数据
 * 高并发场景下使用消息队列异步处理日志记录
 */
@Slf4j
@Aspect
@Component
public class DouyinSpiAnalysisAspect implements ApplicationContextAware {

    @Autowired
    private DouyinSpiAnalysisService analysisService;

    @Autowired
    private DouyinSpiMessageService messageService;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 检查Spring容器是否正在销毁
     */
    private boolean isApplicationContextDestroying() {
        try {
            if (applicationContext == null) {
                return true;
            }

            // 尝试获取一个Bean来检查容器状态
            // 如果容器正在销毁，这个操作会抛出异常
            applicationContext.getBean(DouyinSpiMessageService.class);
            return false;
        } catch (Exception e) {
            // 如果获取Bean失败，说明容器可能正在销毁
            log.debug("检测到Spring容器可能正在销毁: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 环绕通知，记录SPI接口调用的业务分析日志
     * 使用消息队列异步处理，提升高并发场景下的性能
     */
    @Around("@annotation(douyinSpiLog)")
    public Object around(ProceedingJoinPoint point, DouyinSpiLog douyinSpiLog) throws Throwable {
        // 检查Spring容器状态，如果正在销毁则跳过日志记录
        if (isApplicationContextDestroying()) {
            log.warn("Spring容器正在销毁，跳过SPI日志记录");
            return point.proceed();
        }

        // 创建日志记录
        DouyinSpiAnalysisLog analysisLog = new DouyinSpiAnalysisLog();
        String messageId = null;

        try {
            // 提取请求信息
            extractRequestInfo(point, douyinSpiLog, analysisLog);
            
            // 异步发送日志记录消息
            messageId = messageService.sendLogMessage(analysisLog);
            
            // 执行原方法
            Object result = point.proceed();
            
            // 异步处理响应信息
            processResponse(messageId, result);
            
            return result;
        } catch (Exception e) {
            // 记录异常信息
            if (messageId != null) {
                // 构建更新参数
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("id", messageId);
                updateParams.put("responseTime", new Date().getTime());
                updateParams.put("businessResult", "FAIL");
                updateParams.put("businessErrorCode", "SYSTEM_ERROR");
                updateParams.put("businessErrorMsg", e.getMessage());
                
                // 异步发送更新消息
                messageService.sendLogUpdateMessage(updateParams);
            }
            throw e;
        }
    }

    /**
     * 提取请求信息
     */
    private void extractRequestInfo(ProceedingJoinPoint point, DouyinSpiLog douyinSpiLog, DouyinSpiAnalysisLog log) {
        try {
            // 设置SPI类型（从注解获取）
            log.setSpiType(douyinSpiLog.spiType());

            // 获取请求参数
            Object[] args = point.getArgs();
            if (args != null && args.length > 0 && args[0] instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> requestBody = (Map<String, Object>) args[0];
                
                // 提取业务相关字段
                log.setDouyinOrderId((String) requestBody.get("order_id"));
                log.setDouyinOpenId((String) requestBody.get("open_id"));
                log.setStoreCode((String) requestBody.get("store_code"));
                
                // 记录请求时间
                log.setRequestTime(new Date());
            }

            // 获取请求IP和抖音日志ID
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                // 提取 x-bytedance-logid
                String bytedanceLogId = request.getHeader("x-bytedance-logid");
                log.setBytedanceLogId(bytedanceLogId);
                
                String clientIp = request.getHeader("X-Real-IP");
                if (clientIp == null) {
                    clientIp = request.getHeader("X-Forwarded-For");
                }
                if (clientIp == null) {
                    clientIp = request.getRemoteAddr();
                }
                
                // 记录请求相关信息
                Map<String, Object> requestInfo = new HashMap<>();
                requestInfo.put("clientIp", clientIp);
                requestInfo.put("description", douyinSpiLog.description());
                requestInfo.put("args", point.getArgs());
                log.setBusinessDataJson(JSON.toJSONString(requestInfo));
            }
        } catch (Exception e) {
            log.setBusinessErrorMsg("提取请求信息失败: " + e.getMessage());
        }
    }

    /**
     * 处理响应信息，发送异步更新消息
     * 适配抖音官方格式：{data: {error_code: 0, description: "success"}, extra: {...}}
     */
    @SuppressWarnings("unchecked")
    private void processResponse(String messageId, Object result) {
        try {
            if (messageId == null) {
                log.warn("messageId为空，无法更新响应信息");
                return;
            }

            Date responseTime = new Date();
            String businessResult = "FAIL";
            String businessErrorCode = null;
            String businessErrorMsg = null;
            String businessDataJson = null;

            if (result instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) result;

                // 处理抖音官方格式：{data: {...}, extra: {...}}
                if (response.containsKey("data")) {
                    Map<String, Object> data = (Map<String, Object>) response.get("data");
                    if (data != null) {
                        Integer errorCode = (Integer) data.get("error_code");
                        String description = (String) data.get("description");

                        // 根据error_code判断成功失败
                        if (errorCode != null && errorCode == 0) {
                            businessResult = "SUCCESS";
                            businessDataJson = JSON.toJSONString(response);
                        } else {
                            businessResult = "FAIL";
                            businessErrorCode = String.valueOf(errorCode);
                            businessErrorMsg = description;
                        }
                    }
                }
            }

            // 构建更新参数
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("id", messageId);
            updateParams.put("responseTime", responseTime.getTime());
            updateParams.put("businessResult", businessResult);
            updateParams.put("businessErrorCode", businessErrorCode);
            updateParams.put("businessErrorMsg", businessErrorMsg);
            updateParams.put("businessDataJson", businessDataJson);

            log.debug("准备更新响应信息: messageId={}, businessResult={}", messageId, businessResult);

            // 异步发送更新消息
            messageService.sendLogUpdateMessage(updateParams);

            log.info("响应信息更新消息已发送: messageId={}, businessResult={}", messageId, businessResult);

        } catch (Exception e) {
            log.error("处理响应信息失败, messageId: {}", messageId, e);
        }
    }
} 
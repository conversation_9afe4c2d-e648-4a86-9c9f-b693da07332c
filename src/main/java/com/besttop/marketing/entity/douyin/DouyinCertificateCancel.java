package com.besttop.marketing.entity.douyin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 抖音撤销核销记录实体
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_douyin_certificate_cancel")
public class DouyinCertificateCancel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 撤销幂等标识
     */
    private String cancelToken;

    /**
     * 原验券ID
     */
    private String verifyId;

    /**
     * 券码标识
     */
    private String certificateId;

    /**
     * 门店ID
     */
    private String poiId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 券码ID
     */
    private Long couponId;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 撤销时间戳
     */
    private Long cancelTime;

    /**
     * 撤销结果码
     */
    private Integer resultCode;

    /**
     * 撤销结果消息
     */
    private String resultMsg;

    /**
     * 状态：SUCCESS-成功，FAILED-失败
     */
    private String status;

    /**
     * 事务ID
     */
    private Long transactionId;

    /**
     * 请求参数JSON
     */
    private String requestParams;

    /**
     * 响应参数JSON
     */
    private String responseParams;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 状态常量
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
}
package com.besttop.marketing.entity.douyin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 抖音退款审核记录实体
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_douyin_refund_audit_log")
public class DouyinRefundAuditLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后ID
     */
    private String afterSaleId;

    /**
     * 券码标识
     */
    private String certificateId;

    /**
     * 审核结果：1-允许，2-拒绝
     */
    private Integer auditResult;

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 补发券码
     */
    private String code;

    /**
     * 补码凭证信息JSON
     */
    private String voucher;

    /**
     * 券码信息列表JSON
     */
    private String certificateList;

    /**
     * 审核时间戳
     */
    private Long auditTime;

    /**
     * 状态：SUCCESS-成功，FAILED-失败
     */
    private String status;

    /**
     * 请求参数JSON
     */
    private String requestParams;

    /**
     * 响应参数JSON
     */
    private String responseParams;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 状态常量
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
    
    // 审核结果常量
    public static final Integer AUDIT_RESULT_APPROVE = 1; // 允许
    public static final Integer AUDIT_RESULT_REJECT = 2;  // 拒绝
}
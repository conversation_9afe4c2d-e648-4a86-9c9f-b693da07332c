package com.besttop.marketing.controller.schedule;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.BasicOption;
import com.besttop.marketing.model.schedule.ScheduleDefine;
import com.besttop.marketing.service.schedule.ScheduleDefineService;
import com.besttop.marketing.util.CollectionUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 档期活动定义表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@RestController
@RequestMapping("/scheduleDefine")
public class ScheduleDefineController extends BaseController {

    @Autowired
    private ScheduleDefineService scheduleDefineService;

    /**
     * @methodName add
     * @description 添加档期活动定义规则
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/11 15:26
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "添加档期活动定义规则")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) ScheduleDefine define) {
        String message=scheduleDefineService.add(define);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * @methodName update
     * @description 修改档期活动定义
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/11 15:30
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "修改档期活动定义")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) ScheduleDefine define) {
        if (StringUtils.isBlank(define.getId())) {
            return paramError("请选择需要修改的数据");
        }
        String message=scheduleDefineService.update(define);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * @methodName del
     * @description 删除档期活动定义
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/11 16:35
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除档期活动定义")
    public ResultEntity del(@RequestBody ScheduleDefine define) {
        if(CollectionUtil.isEmpty(define.getIds())){
            return paramError("请选择需要删除的数据");
        }
        String message=scheduleDefineService.del(define);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * @methodName findBySeleted
     * @description 模糊查询档期活动定义
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/11 16:52
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody ScheduleDefine define) {
        return success(new PageInfo<>(scheduleDefineService.findBySelected(define)));

    }

    /**
     * @methodName findOption
     * @description 查询档期活动下拉框接口
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/31 10:35
     */
    @RequestMapping(value = "/findOption")
    @RedisCacheConvertEnable
    public ResultEntity findOption(@RequestBody ScheduleDefine define) {
        return success(new PageInfo<>(scheduleDefineService.findOption(define)));
    }

    /***
     * 查询档期活动是否被用于规则单
     * @methodName
     * @description
     * @params
     * @return
     * <AUTHOR>
     * @date 2020/5/15 9:38
     */
    @RequestMapping(value = "/findIsUse")
    public ResultEntity findIsUse(@RequestBody ScheduleDefine define) {
        return success(scheduleDefineService.getBillScheduleRule(define.getCode()));
    }


    /***
     * 费用监控单规则单号查询下拉框
     * @methodName
     * @description
     * @params
     * @return
     * <AUTHOR>
     * @date 2020/12/15 9:38
     */
    @RequestMapping(value = "/findCodeByProvide")
    public ResultEntity findCodeByProvide(@RequestBody ScheduleDefine define) {
        return success(new PageInfo<>(scheduleDefineService.findCodeByProvide(define)));
    }

}

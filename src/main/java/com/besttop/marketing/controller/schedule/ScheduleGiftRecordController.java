package com.besttop.marketing.controller.schedule;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.schedule.param.InfoParam;
import com.besttop.marketing.model.schedule.param.ScheduleGiftRecordParam;
import com.besttop.marketing.service.schedule.ScheduleGiftRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 档期赠品/礼品发放记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@RestController
@RequestMapping("/scheduleGiftRecord")
@Slf4j
public class ScheduleGiftRecordController extends BaseController {

    @Autowired
    private ScheduleGiftRecordService scheduleGiftRecordService;

    /***
     *通过流水号  获取可以参加满增商品
     * @methodName getGifts
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/13 15:37
     */
    @PostMapping("/getGifts")
    @RedisCacheConvertEnable
    public ResultEntity<List<InfoParam>> getGifts(@RequestBody ScheduleGiftRecordParam param) {
        try {
            return scheduleGiftRecordService.getGifts(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("通过流水号获取可以参加满增商品error:{}",e.getStackTrace());
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

    /***
     *通过流水号  获取可回收列表
     * @methodName getGifts
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/13 15:37
     */
    @PostMapping("/getRecycleGifts")
    @RedisCacheConvertEnable
    public ResultEntity<List<InfoParam>> getRecycleGifts(@RequestBody ScheduleGiftRecordParam param) {
        try {
            return scheduleGiftRecordService.getRecycleGifts(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }



    /***
     *发放赠品
     * @methodName grant
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/15 9:17
     */
    @PostMapping("/grant")
    @RedisCacheConvertEnable
    @OperationLog(opName = "发放赠品")
    public ResultEntity grant(@RequestBody InfoParam param) {
        try {
        	if(!param.getOrderCodes().contains(",")) {
        		param.setRecordType("erp:bill_schedule_rule_type:3");
        	} else if(param.getOrderCodes().contains(",")) {
        		param.setRecordType("erp:bill_schedule_rule_type:6");
        	}
            return scheduleGiftRecordService.grantOrRecycle(param, "grant");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

    /***
     *回收赠品
     * @methodName grant
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/15 9:17
     */
    @PostMapping("/recycle")
    @RedisCacheConvertEnable
    @OperationLog(opName = "回收赠品")
    public ResultEntity recycle(@RequestBody InfoParam param) {
        try {
            return scheduleGiftRecordService.grantOrRecycle(param, "recycle");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

    /***
     *查询发放记录
     * @methodName
     * @description
     * @params
     * @return
     * <AUTHOR>
     * @date 2020/4/15 9:18
     */
    @PostMapping("/getTable")
    @RedisCacheConvertEnable
    public ResultEntity<List<InfoParam>> getTable(@RequestBody InfoParam param) {
        try {
            return scheduleGiftRecordService.getTable(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

    /***
     *获取流水号  精确查
     * @methodName getCodeList
     * @description 
     * @params [param]
     * @return com.besttop.common.model.ResultEntity<java.util.List<com.besttop.marketing.model.schedule.param.InfoParam>>
     * <AUTHOR>
     * @date 2020/4/25 11:26
     */
    @PostMapping("/getCodeList")
    public ResultEntity<List<InfoParam>> getCodeList(@RequestBody InfoParam param) {
        try {
            return scheduleGiftRecordService.getCodeList(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

}

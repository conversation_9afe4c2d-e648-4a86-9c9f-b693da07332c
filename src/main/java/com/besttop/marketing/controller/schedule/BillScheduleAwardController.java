package com.besttop.marketing.controller.schedule;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.schedule.BillScheduleAward;
import com.besttop.marketing.model.schedule.param.BillScheduleAwardParam;
import com.besttop.marketing.service.schedule.BillScheduleAwardService;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 顾客激烈配置表(BillScheduleAward)表控制层
 *
 * <AUTHOR>
 * @since 2020-12-21 13:57:42
 */
@RestController
@RequestMapping("/billScheduleAward")
public class BillScheduleAwardController extends BaseController {
    /**
     * 服务对象
     */
    @Resource
    private BillScheduleAwardService billScheduleAwardService;

    /**
     * Description: 分页查询所有数据
     *
     * <AUTHOR>
     * @date: 2020-12-21 14:16
     * @param:[billScheduleAward]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/list")
    public ResultEntity list(@RequestBody BillScheduleAward billScheduleAward) {
        return success(billScheduleAwardService.list(billScheduleAward));
    }

    /**
     * Description:新增
     *
     * <AUTHOR>
     * @date: 2020-12-21 14:19
     * @param:[billBudgetSales]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/add")
    public ResultEntity add(@RequestBody BillScheduleAward billBudgetSales) {
        this.billScheduleAwardService.add(billBudgetSales);
        return success();
    }

    /**
     * Description:修改数据
     *
     * <AUTHOR>
     * @date: 2020-12-21 14:19
     * @param:[billBudgetSales]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/updateList")
    public ResultEntity update(@RequestBody BillScheduleAwardParam billScheduleAwardParam) {
        this.billScheduleAwardService.update(billScheduleAwardParam);
        return success();
    }

    /**
     * Description: 销售分解删除
     *
     * <AUTHOR>
     * @date: 2020-12-21 14:19
     * @param:[billBudgetSales]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/delete")
    public ResultEntity delete(@RequestBody BillScheduleAward billBudgetSales) {
        this.billScheduleAwardService.delete(billBudgetSales);
        return success();
    }
}
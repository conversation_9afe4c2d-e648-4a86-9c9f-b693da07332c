package com.besttop.marketing.controller.manual;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.manual.ManualInvoice;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.service.manual.ManualInvoiceService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 手工开票 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@RestController
@RequestMapping("/manualInvoice")
public class ManualInvoiceController extends BaseController {

    @Autowired
    private ManualInvoiceService manualInvoiceService;

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName addManualInvoice
     * @description 新增手工开票基础信息
     * <AUTHOR>
     * @date 2020/6/18 15:18
     */
    @PostMapping("/addManualInvoice")
    public ResultEntity addManualInvoice(@RequestBody @Validated(GroupAdd.class) ManualInvoice invoice) {
        String str = manualInvoiceService.addManualInvoice(invoice);
        if (str.equals("ERRY")) {
            return paramError("该销售单已经加入过手工开票了");
        }
        return success(str, null);
    }

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryManualInvoice
     * @description 多条件查询手工开票列表
     * <AUTHOR>
     * @date 2020/6/18 17:00
     */
    @PostMapping("/queryManualInvoice")
    @RedisCacheConvertEnable
    public ResultEntity queryManualInvoice(@RequestBody ManualInvoice invoice) {
        return success(new PageInfo<>(manualInvoiceService.queryManualInvoice(invoice)));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryInvoiceOrders
     * @description 查询待开票销售单/预售单
     * <AUTHOR>
     * @date 2020/6/23 14:19
     */
    @PostMapping("/queryInvoiceOrders")
    @RedisCacheConvertEnable
    public ResultEntity queryInvoiceOrders(@RequestBody OrderQueryParam param) {
        return success(new PageInfo<>(manualInvoiceService.queryInvoiceOrders(param)));
    }

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPrintInvoice
     * @description 查询打印发票
     * <AUTHOR>
     * @date 2020/9/1 15:39
     */
    @PostMapping("/queryPrintInvoice")
    @RedisCacheConvertEnable
    public ResultEntity queryPrintInvoice(@RequestBody ManualInvoice invoice) {
        return success(new PageInfo<>(manualInvoiceService.queryPrintInvoice(invoice)));
    }

}

package com.besttop.marketing.controller.manual;


import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.besttop.edp.utils.RedisLockUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.manual.ManualGiftRecord;
import com.besttop.marketing.service.manual.ManualGiftRecordService;
import com.besttop.marketing.util.ErpBaseUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 手动礼品发放记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */
@RestController
@Slf4j
@RequestMapping("/manualGiftRecord")
public class ManualGiftRecordController extends BaseController {

	@Autowired
	private ManualGiftRecordService manualGiftRecordService;
	@Autowired
	private ErpBaseUtil erpBaseUtil;
	@Autowired
	private RedisLockUtil redisLockUtil;

	/**
	 * 礼品打印增加
	 *
	 */
	@PostMapping("/addPrint")
	@OperationLog(opName = "礼品打印增加")
	public ResultEntity addPrint(@RequestBody ManualGiftRecord param) {
		String message = manualGiftRecordService.addPrint(param);
		if (StringUtils.isBlank(message)) {
			return success();
		} else {
			return error(ResultEnum.ADD_ERROR, message);
		}
	}

	/***
	 * 发放礼品
	 * 
	 * @methodName grant
	 * @description
	 * @params [param]
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/11/23 11:07
	 */
	@PostMapping("/grant")
	@RedisCacheConvertEnable
	@OperationLog(opName = "发放礼品")
	public ResultEntity grant(@RequestBody ManualGiftRecord param) {
		String key = "";
		try {
			param.setTag("grant");

			if (StringUtils.isNotBlank(param.getOrderCode())) {
				// 多次发放数据
				List<Map<String, Object>> data = erpBaseUtil.findOutType("erp:gift_is_multiple_times");
				log.info("====grant==data:{}", JSON.toJSONString(data));

				// 处理数据
				String msg = this.processData(param, data);

				if (StringUtils.isNotBlank(msg)) {
					return new ResultEntity(ResultEnum.ERP_MARK_ADD_ERROR, msg);
				}
			}


			// 设置默认的outType
			if (StringUtils.isBlank(param.getOutType())) {
				param.setOutType(CommonEnums.SHOPPING_OUT_TYPE_SPOT.getCode());
			}

			key = redisLockUtil.getAuditExitLock(param.getCustomerCode()+"-"+param.getTelephone()+"-"+param.getGiftsId());
			// 发放礼品
			return manualGiftRecordService.grantOrRecycle(param);
		} catch (Exception e) {
			log.error("错误信息: {}", e.getMessage());
			String msg = "系统错误";
			if(StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("getAuditExitLock")){
				msg = "正在发放中，请稍后再试！";
			}
			return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, msg);
		} finally {
			// 释放资源
			this.releaseResources(param,key);
		}
	}

	private String processData(ManualGiftRecord param, List<Map<String, Object>> data) {
		for (Map<String, Object> d : data) {
			// 检查Redis键和值
			if (CommonEnums.MANUAL_GIFT_IS_MULTIPLE_TIMES_ON.getCode().equals(d.get("redisKey"))
					&& "0".equals(d.get("value"))) {
				QueryWrapper<ManualGiftRecord> paramWrapper = new QueryWrapper<>();
				// 添加查询条件
				this.addScheduleRecordCondition(paramWrapper, param);
				if (StringUtils.isNotBlank(param.getOrderCode())) {
					this.addOrderCodeCondition(paramWrapper, param.getOrderCode());
				}

				// 检查列表是否存在
				List<ManualGiftRecord> list = manualGiftRecordService.list(paramWrapper);

				if (CollectionUtils.isNotEmpty(list)) {
					for (ManualGiftRecord manualGiftRecord : list) {
						// 检查是否可以多次发放
						if (!CommonEnums.SCHEDULE_GIFT_RECORD_TYPE__HUI_SHOU.getCode().equals(manualGiftRecord.getType())
								&& !(1 == manualGiftRecord.getIsRecycle() && manualGiftRecord.getIsFlag() == 0)) {
							String[] orderCodes = param.getOrderCode().split(",");
							Set<String> codes = new HashSet<>();
							for (String code : orderCodes) {
								if (manualGiftRecord.getOrderCode().contains(code)) {
									codes.add(code);
								}
							}
							if(CollectionUtils.isNotEmpty(codes)){
								return  String.format("销售单:[%s]已经有发放记录, 当前系统不允许多次发放礼品!",  String.join(",", codes));
							}

						}
					}
				}
			}
		}
		return null;
	}

	private void addScheduleRecordCondition(QueryWrapper<ManualGiftRecord> paramWrapper, ManualGiftRecord param) {
		if (CollectionUtils.isNotEmpty(param.getScheduleGiftRecordParamList())) {
			paramWrapper.isNotNull("audit_time");
			paramWrapper.isNotNull("audit_by");
		}
		if (StringUtils.isNotBlank(param.getScheduleRuleCode())) {
			paramWrapper.eq("schedule_rule_code", param.getScheduleRuleCode());
		}
	}

	private void addOrderCodeCondition(QueryWrapper<ManualGiftRecord> paramWrapper, String orderCode) {
		String sql = "";
		String[] orders = orderCode.split(",");
		for (String order : orders) {
			sql += " OR order_code LIKE CONCAT('%','" + order + "','%')";
		}
		sql = sql.substring(3);
		paramWrapper.apply("(" + sql + ")");
	}

	private void releaseResources(ManualGiftRecord param,String key) {
		if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
			redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
		}
		if (StringUtils.isNotBlank(key)) {
			redisLockUtil.releaseLock(key);
		}
	}

	/***
	 * 删除记录
	 * 
	 * @methodName deleteById
	 * @description
	 * @params [param]
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/11/23 11:07
	 */
	@PostMapping("/deleteById")
	@RedisCacheConvertEnable
	@OperationLog(opName = "删除记录")
	public ResultEntity deleteById(@RequestBody ManualGiftRecord param) {
		try {
			return manualGiftRecordService.deleteById(param);
		} catch (Exception e) {
			return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
		}
	}

	/***
	 * 回收礼品
	 * 
	 * @methodName recycle
	 * @description
	 * @params [param]
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/11/23 11:08
	 */
	@PostMapping("/recycle")
	@RedisCacheConvertEnable
	@OperationLog(opName = "回收礼品")
	public ResultEntity recycle(@RequestBody ManualGiftRecord param) {
		try {
			param.setTag("recycle");
			return manualGiftRecordService.grantOrRecycle(param);
		} catch (Exception e) {
			return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
		}finally {
			if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
				redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
			}
		}
	}

	/***
	 * 查询发放记录
	 * 
	 * @methodName getTable
	 * @description
	 * @params
	 * @return
	 * <AUTHOR>
	 * @date 2020/4/15 9:18
	 */
	@PostMapping("/getTable")
	@RedisCacheConvertEnable
	public ResultEntity<List<ManualGiftRecord>> getTable(@RequestBody ManualGiftRecord param) {
		try {
			return manualGiftRecordService.getTable(param);
		} catch (Exception e) {
			return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
		}
	}

	/***
	 * 查询规则单匹配
	 * 
	 * @methodName getGifts
	 * @description
	 * @params
	 * @return
	 * <AUTHOR>
	 * @date 2020/4/15 9:18
	 */
	@PostMapping("/getGifts")
	@RedisCacheConvertEnable
	public ResultEntity getGifts(@RequestBody ManualGiftRecord param) {
		try {
			return manualGiftRecordService.getGifts(param);
		} catch (Exception e) {
			return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
		}
	}

	/***
	 * 查询销售单
	 * 
	 * @methodName getOrderInfo
	 * @description
	 * @params
	 * @return
	 * <AUTHOR>
	 * @date 2020/4/15 9:18
	 */
	@PostMapping("/getOrderInfo")
	@RedisCacheConvertEnable
	public ResultEntity getOrderInfo(@RequestBody ManualGiftRecord param) {
		try {
			return manualGiftRecordService.getOrderInfo(param);
		} catch (Exception e) {
			return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
		}
	}
}

package com.besttop.marketing.controller.scores;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.scores.ScoresConvert;
import com.besttop.marketing.service.scores.ScoresConvertService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 营销管理-积分兑换规则 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@RestController
@RequestMapping("/scoresConvert")
public class ScoresConvertController extends BaseController {

    @Autowired
    private ScoresConvertService scoresConvertService;

    /**
     * 新增积分兑换规则
     *
     * @param scoresConvert
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 新增积分兑换规则
     * <AUTHOR>
     * @date 2020/4/15 16:56
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增积分兑换规则")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) ScoresConvert scoresConvert) {
        String message = scoresConvertService.add(scoresConvert);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * 编辑积分兑换规则
     *
     * @param scoresConvert
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 编辑积分兑换规则
     * <AUTHOR>
     * @date 2020/4/15 16:56
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑积分兑换规则")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) ScoresConvert scoresConvert) {
        if (StringUtils.isBlank(scoresConvert.getId())) {
            return paramError("请选择需要修改的数据");
        }
        String message = scoresConvertService.update(scoresConvert);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * 删除兑换规则定义
     *
     * @param scoresConvert
     * @return com.besttop.common.model.ResultEntity
     * @methodName del
     * @description 删除兑换规则定义
     * <AUTHOR>
     * @date 2020/4/15 17:57
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除兑换规则定义")
    public ResultEntity del(@RequestBody ScoresConvert scoresConvert) {
        String message = scoresConvertService.del(scoresConvert);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * 模糊查询列表
     *
     * @param scoresConvert
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBySelected
     * @description 模糊查询列表
     * <AUTHOR>
     * @date 2020/4/15 18:09
     */
    @RequestMapping(value = "/findBySelected")
    public ResultEntity findBySelected(@RequestBody ScoresConvert scoresConvert) {
        return success(new PageInfo<>(scoresConvertService.findBySelected(scoresConvert)));
    }

}

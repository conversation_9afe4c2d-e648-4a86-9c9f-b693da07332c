package com.besttop.marketing.controller.scores;

import cn.hutool.json.JSONObject;
import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.param.CustomerAccountParam;
import com.besttop.marketing.model.customer.result.CustomerAccountResult;
import com.besttop.marketing.model.scores.BaseSkuInfo;
import com.besttop.marketing.model.scores.param.ScoresConvertGiftParam;
import com.besttop.marketing.service.scores.ScoresConvertGiftService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 积分兑换礼品规则(ScoresConvertGift)表控制层
 *
 * <AUTHOR>
 * @since 2021-08-18 15:58:37
 */
@RestController
@RequestMapping("/scoresConvertGift")
@Slf4j
public class ScoresConvertGiftController extends BaseController {
    @Autowired
    private ScoresConvertGiftService scoresConvertGiftService;

    /**
     * @description: 积分礼品回收，入库成功之后 回冲积分 feign接口
     * @param: [code]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/23 14:32
     */
    @PostMapping("/writeDownScore")
    public ResultEntity writeDownScore(@RequestBody CustomerAccount account) {
        try {
            if (StringUtils.isBlank(account.getCode())) {
                throw new RuntimeException("积分回冲，code参数不可为空");
            }
            scoresConvertGiftService.writeDownScore(account.getCode());
            return success();
        } catch (Exception e) {
            log.error(e.getMessage());
            return paramError(e.getMessage());
        }
    }

    /**
     * @description: 查询 关联合同的自营单品以及礼品
     * @param: [info]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/21 16:31
     */
    @PostMapping("/queryContractBasesku")
    @RedisCacheConvertEnable
    public ResultEntity queryContractBasesku(@RequestBody BaseSkuInfo info) {
        return success(scoresConvertGiftService.queryContractBasesku(info));
    }

    /**
     * @description: 查看兑换记录
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/21 10:18
     */
    @PostMapping("/queryGiftRecord")
    @RedisCacheConvertEnable
    public ResultEntity queryGiftRecord(@RequestBody CustomerAccountParam param) {
        try {
            List<CustomerAccountResult> list = scoresConvertGiftService.queryGiftRecord(param);
            return success(new PageInfo<>(list));
        } catch (Exception e) {
            log.error(e.getMessage());
            return paramError(e.getMessage());
        }
    }

    /**
     * @description: 回收礼品
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/21 10:12
     */
    @PostMapping("/recycleGift")
    public ResultEntity recycleGift(@RequestBody CustomerAccountParam param) {
        try {
            scoresConvertGiftService.recycleGift(param);
            return success();
        } catch (Exception e) {
            log.error(e.getMessage());
            return paramError(e.getMessage());
        }
    }

    /**
     * @description: 积分兑换礼品操作
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/20 14:04
     */
    @PostMapping("/exchangeGift")
    public ResultEntity exchangeGift(@RequestBody ScoresConvertGiftParam param) {
        try {
            scoresConvertGiftService.exchangeGift(param);
            return success();
        } catch (Exception e) {
            log.error(e.getMessage());
            return paramError(e.getMessage());
        }
    }

    /**
     * @description: 查询客户可兑换的电子币列表（pc）
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/19 16:47
     */
    @PostMapping("/queryCuntomerConvertRuleGift")
    public ResultEntity queryCuntomerConvertRuleGift(@RequestBody ScoresConvertGiftParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("入参不能为空");
        }
        return success(new PageInfo<>(scoresConvertGiftService.queryCuntomerConvertRuleGift(param)));
    }

    /**
     * @description: 更新
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/19 11:09
     */
    @PostMapping("/update")
    public ResultEntity update(@RequestBody ScoresConvertGiftParam param) {
        String check = scoresConvertGiftService.checkScoresConvert(param);
        if (StringUtils.isBlank(check)) {
            String result = scoresConvertGiftService.update(param);
            return StringUtils.isBlank(result) ? success() : paramError(result);
        } else {
            return paramError(check);
        }
    }

    /**
     * @description: 增加积分兑换礼品规则单
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/18 19:16
     */
    @PostMapping("/add")
    public ResultEntity add(@RequestBody ScoresConvertGiftParam param) {
        String check = scoresConvertGiftService.checkScoresConvert(param);
        if (StringUtils.isBlank(check)) {
            String result = scoresConvertGiftService.add(param);
            return StringUtils.isBlank(result) ? success() : paramError(result);
        } else {
            return paramError(check);
        }
    }

    /**
     * @description: 查询单个
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/18 19:15
     */
    @PostMapping("/findOne")
    public ResultEntity findOne(@RequestBody ScoresConvertGiftParam param) {
        return success(scoresConvertGiftService.findOne(param));
    }

    /**
     * @description: 删除积分兑换礼品规则（可批量）
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/18 18:32
     */
    @PostMapping("/deleteById")
    public ResultEntity deleteById(@RequestBody ScoresConvertGiftParam param) {
        String msg = scoresConvertGiftService.deleteById(param);
        if (StringUtils.isBlank(msg)) {
            return success();
        } else {
            return paramError(msg);
        }
    }

    /**
     * @description: 查询筛选积分兑换礼品
     * @param: [param]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/18 17:28
     */
    @PostMapping("/findBySelected")
    public ResultEntity findBySelected(@RequestBody ScoresConvertGiftParam param) {
        return success(new PageInfo<>(scoresConvertGiftService.findBySelected(param)));
    }
}

package com.besttop.marketing.controller.scores;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.scores.param.ScoresDefineParam;
import com.besttop.marketing.service.scores.ScoresDefineService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 营销管理-积分生成规则定义表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@RestController
@RequestMapping("/scoresDefine")
public class ScoresDefineController extends BaseController {

    @Autowired
    private ScoresDefineService scoresDefineService;

    /**
     * 添加积分生成规则
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 添加积分生成规则
     * <AUTHOR>
     * @date 2020/4/20 16:10
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "添加积分生成规则")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) ScoresDefineParam param) {
        String message = scoresDefineService.add(param);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * 编辑积分生成规则
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 编辑积分生成规则
     * <AUTHOR>
     * @date 2020/4/20 16:28
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑积分生成规则")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) ScoresDefineParam param) {
        String message = scoresDefineService.update(param);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * 删除积分生成规则
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName del
     * @description 删除积分生成规则
     * <AUTHOR>
     * @date 2020/4/20 16:50
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除积分生成规则")
    public ResultEntity del(@RequestBody ScoresDefineParam param) {
        String message = scoresDefineService.del(param);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * 查询详情
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 查询详情
     * <AUTHOR>
     * @date 2020/4/20 17:01
     */
    @RequestMapping(value = "/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody ScoresDefineParam param) {
        if (StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())) {
            return paramError("请传入编码和id");
        }
        return success(scoresDefineService.findDetail(param));
    }

    /**
     * 查询列表
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBySelected
     * @description 查询列表
     * <AUTHOR>
     * @date 2020/4/20 17:01
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody ScoresDefineParam param) {
        return success(new PageInfo<>(scoresDefineService.findBySelected(param)));
    }

    @PostMapping("/queryScores")
    @RedisCacheConvertEnable
    public ResultEntity<?> queryScores(@RequestBody ScoresDefineParam param) {
        if(StringUtils.isBlank(param.getSkuCode())){
            return paramError("请传入sku编码");
        }
        if(StringUtils.isBlank(param.getClassCode())){
            return paramError("请传入品类编码");
        }
        if(StringUtils.isBlank(param.getBrandCode())){
            return paramError("请传入品牌编码");
        }

        return success(scoresDefineService.queryScores(param));
    }

}

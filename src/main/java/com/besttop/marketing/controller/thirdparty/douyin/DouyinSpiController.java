package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.marketing.annotation.DouyinSpiLog;
import com.besttop.marketing.annotation.DouyinSpiRecord;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSpiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * 抖音回调SPI控制器
 * 处理抖音平台对EDP系统的回调请求, 遵循"综合到店提货券"三方码场景
 */
@RestController
@RequestMapping("/spi/douyin")
@Slf4j
public class DouyinSpiController extends BaseController {

    @Autowired
    private DouyinSpiService douyinSpiService;

    /**
     * 创单接口
     * 提货券同步外部创单
     * 抖音通知EDP创建订单
     */
    @PostMapping("/create-order")
   @DouyinSpiLog(spiType = "CREATE_ORDER", description = "抖音创单通知接口")
   @DouyinSpiRecord(spiType = "CREATE_ORDER", businessIdKey = "order_id")
    public Map<String, Object> createOrder(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音创单通知: {}", requestBody);
            return douyinSpiService.handleCreateOrder(requestBody);
        } catch (Exception e) {
            log.error("处理抖音创单通知失败", e);
            return buildErrorResponse(500,"处理创单通知失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单通知接口
     * 通知外部商家取消订单
     * 抖音通知EDP用户已取消订单
     */
    @PostMapping("/notify-cancel")
    @DouyinSpiLog(spiType = "NOTIFY_CANCEL", description = "抖音取消订单通知接口")
    @DouyinSpiRecord(spiType = "NOTIFY_CANCEL", businessIdKey = "order_id")
    public Map<String, Object> notifyCancel(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音取消订单通知: {}", requestBody);
            return douyinSpiService.handleNotifyCancel(requestBody);
        } catch (Exception e) {
            log.error("处理抖音取消订单通知失败", e);
            return buildErrorResponse(500,"处理取消订单通知失败：" + e.getMessage());
        }
    }
    /**
     * 支付成功通知接口
     * 通知外部商家支付成功
     * 抖音通知EDP订单已支付
     */
    @PostMapping("/notify-payment")
    @DouyinSpiLog(spiType = "NOTIFY_PAYMENT", description = "抖音支付成功通知接口")
    @DouyinSpiRecord(spiType = "NOTIFY_PAYMENT", businessIdKey = "order_id")
    public Map<String, Object> notifyPayment(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音支付成功通知: {}", requestBody);
            return douyinSpiService.handleNotifyPayment(requestBody);
        } catch (Exception e) {
            log.error("处理抖音支付成功通知失败", e);
            return buildErrorResponse(500,"处理支付成功通知失败：" + e.getMessage());
        }
    }

    /**
     * 退款申请接口
     * 抖音请求EDP商家退款申请
     * 抖音请求EDP审核退款申请
     */
    @PostMapping("/apply-refund")
   @DouyinSpiLog(spiType = "APPLY_REFUND", description = "抖音退款申请接口")
   @DouyinSpiRecord(spiType = "APPLY_REFUND", businessIdKey = "biz_uniq_key")
    public Map<String, Object> applyRefund(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音退款申请请求: {}", requestBody);
            return douyinSpiService.handleApplyRefund(requestBody);
        } catch (Exception e) {
            log.error("处理抖音退款申请请求失败", e);
            return buildErrorResponse(500,"处理退款申请失败：" + e.getMessage());
        }
    }

    /**
     * 退款结果同步接口退款结果同步外部商家
     * 抖音将最终退款结果通知EDP
     */
    @PostMapping("/notify-refund")
    @DouyinSpiLog(spiType = "SYNC_REFUND_RESULT", description = "抖音退款结果同步接口")
    public Map<String, Object> syncRefundResult(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音退款结果同步请求: {}", requestBody);
            return douyinSpiService.handleSyncRefundResult(requestBody);
        } catch (Exception e) {
            log.error("处理抖音退款结果同步请求失败", e);
            return buildErrorResponse(500,"处理退款结果同步失败：" + e.getMessage());
        }
    }

    /**
     * 退款信息同步接口
     * 抖音主动向服务商同步退款状态信息
     * 此接口用于非服务商发起的审核场景，如超时未收到服务商审核结果、客服退款和来客审核等情况
     */
    @PostMapping("/notify-refund-info")
    @DouyinSpiLog(spiType = "SYNC_REFUND_INFO", description = "抖音退款信息同步接口")
    @DouyinSpiRecord(spiType = "SYNC_REFUND_INFO", businessIdKey = "order_id")
    public Map<String, Object> notifyRefundInfo(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音退款信息同步请求: {}", requestBody);
            return douyinSpiService.handleNotifyRefundInfo(requestBody);
        } catch (Exception e) {
            log.error("处理抖音退款信息同步请求失败", e);
            return buildErrorResponse(500,"处理退款信息同步失败：" + e.getMessage());
        }
    }

    /**
     * 发券接口
     * 抖音平台调用此接口请求EDP系统生成券码
     */
    @PostMapping("/issue-coupon")
   @DouyinSpiLog(spiType = "ISSUE_COUPON", description = "抖音发券通知接口")
   @DouyinSpiRecord(spiType = "ISSUE_COUPON", businessIdKey = "order_id")
    public Map<String, Object> issueCoupon(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音发券请求: {}", requestBody);
            return douyinSpiService.handleIssueCoupon(requestBody);
        } catch (Exception e) {
            log.error("处理抖音发券请求失败", e);
            return buildErrorResponse(500,"处理发券失败：" + e.getMessage());
        }
    }

    /**
     * 三方码-预下单
     * 提货券同步外部创单
     * 抖音通知EDP创建订单
     */
    @PostMapping("/pre-order")
    @DouyinSpiLog(spiType = "PRE_ORDER", description = "抖音三方码预创单通知接口")
    @DouyinSpiRecord(spiType = "PRE_ORDER", businessIdKey = "order_id")
    public Map<String, Object> preOrder(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音创单通知: {}", requestBody);
            return douyinSpiService.preOrder(requestBody);
        } catch (Exception e) {
            log.error("处理抖音创单通知失败", e);
            return buildErrorResponse(500,"处理创单通知失败：" + e.getMessage());
        }
    }

    private Map<String, Object> buildErrorResponse(int errorCode, String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("err_no", errorCode);
        response.put("err_msg", errorMessage);
        return response;
    }
} 
package com.besttop.marketing.controller.thirdparty.douyin;

import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCouponMapper;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCouponRuleMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponRule;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSyncService;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.List;

import com.besttop.marketing.model.thirdparty.douyin.param.CertificatePrepareRequest;
import com.besttop.marketing.model.thirdparty.douyin.param.VerifyCouponRequest;
import com.besttop.marketing.model.thirdparty.douyin.param.CancelVerificationRequest;
import com.besttop.marketing.model.thirdparty.douyin.param.IssueCouponCallbackRequest;
import com.besttop.marketing.model.thirdparty.douyin.param.RefundAuditCallbackRequest;
import com.besttop.marketing.model.thirdparty.douyin.param.SyncVerificationRequest;
import com.besttop.marketing.model.thirdparty.douyin.param.CertificateStatusRequest;
import com.besttop.marketing.mapper.common.DaoMapper;

/**
 * 抖音同步控制器
 * 提供手动同步接口
 */
@RestController
@RequestMapping("/douyin/sync")
@Slf4j
public class DouyinSyncController extends BaseController {

    private final DouyinSyncService douyinSyncService;
    private final DouyinCouponMapper douyinCouponMapper;
    private final DouyinCouponRuleMapper douyinCouponRuleMapper;
    private final LoginCacheUtil loginCacheUtil;
    private final DaoMapper daoMapper;

    @Autowired
    public DouyinSyncController(DouyinSyncService douyinSyncService,
                                DouyinCouponMapper douyinCouponMapper,
                                DouyinCouponRuleMapper douyinCouponRuleMapper, 
                                LoginCacheUtil loginCacheUtil,
                                DaoMapper daoMapper) {
        this.douyinSyncService = douyinSyncService;
        this.douyinCouponMapper = douyinCouponMapper;
        this.douyinCouponRuleMapper = douyinCouponRuleMapper;
        this.loginCacheUtil = loginCacheUtil;
        this.daoMapper = daoMapper;
    }

    /**
     * 验券准备接口 (EDP调用抖音)
     * 扫码或手工输入券码时，获取券的详细信息，为后续的核销做准备
     *
     * @param request 包含券码明文和加密数据
     * @return 验券准备结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/certificate-prepare")
    public ResultEntity certificatePrepare(@RequestBody CertificatePrepareRequest request) {
        try {
            log.info("开始调用抖音验券准备接口, code={}, encryptedData={}",
                    request.getCode(), request.getEncryptedData());

            // 参数校验
            if (StringUtils.isEmpty(request.getCode()) && StringUtils.isEmpty(request.getEncryptedData())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "code和encryptedData必须提供一个");
            }

            String storeCode = loginCacheUtil.getStoreCode();

            if (StringUtils.isEmpty(storeCode)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到当前登录的门店信息");
            }

            // 从数据库获取抖音门店ID
            String poiId = daoMapper.findLifePoi(storeCode);
            if (StringUtils.isEmpty(poiId)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到门店对应的抖音门店ID");
            }
            // 调用验券准备服务
            Map<String, Object> result = douyinSyncService.certificatePrepare(request.getCode(), request.getEncryptedData(), poiId);

            // 判断结果
            if ((boolean) result.getOrDefault("success", false)) {
                return success(result);
            } else {
                Integer errorCode = (Integer) result.getOrDefault("error_code", 2119002);
                String errorMessage = (String) result.getOrDefault("error_message", "验券准备失败");
                return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMessage + " [" + errorCode + "]");
            }
        } catch (Exception e) {
            log.error("调用抖音验券准备接口异常", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 验券接口 (EDP调用抖音)
     * 用于向抖音验证并核销券码
     * 
     * 根据抖音开放平台文档：
     * 1. 提货券抖音券码的核销需要先调用验券准备接口，再调用本接口。
     * 2. 可支持多个批量验券，需为同一个订单，不可跨订单验券。
     * 3. error_code、result 均为 0 代表验券成功。
     * 4. error_code非0时则表示调用验券接口失败，建议服务商侧主动发起重试，建议间隔5s发起。
     *    第一次调用error_code非0，第二次调用error_code=0且result=1208或2，也可以代表验券成功
     *
     * @param request 包含券码明文和验证Token等信息
     * @return 验券结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/verify-coupon")
    public ResultEntity verifyCoupon(@RequestBody VerifyCouponRequest request) {
        try {
            log.info("开始调用抖音验券接口, request={}", JSON.toJSONString(request));

            // 检查必要参数
            if ((request.getCodes() == null || request.getCodes().isEmpty()) &&
                (request.getEncryptedCodes() == null || request.getEncryptedCodes().isEmpty()) &&
                (request.getCodeWithTimeList() == null || request.getCodeWithTimeList().isEmpty())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未提供任何券码信息");
            }
            
            if (StringUtils.isEmpty(request.getVerifyToken())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "验证Token不能为空，请先调用验券准备接口获取");
            }

            // 获取当前登录门店编码
            String storeCode = loginCacheUtil.getStoreCode();
            if (StringUtils.isEmpty(storeCode)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到当前登录的门店信息");
            }
            
            // 从数据库获取抖音门店ID
            String poiId = daoMapper.findLifePoi(storeCode);
            if (StringUtils.isEmpty(poiId)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到门店对应的抖音门店ID");
            }

            // 调用验券服务（使用完整参数版本）
            Map<String, Object> result = douyinSyncService.verifyCoupon(request, poiId);
            
            if ((boolean) result.getOrDefault("success", false)) {
                // 成功调用API，但需检查验券结果
                if ((boolean) result.getOrDefault("verify_success", false)) {
                    log.info("验券成功: verifyId={}", result.get("verify_id"));
                    return success(result);
                } else {
                    // API调用成功但验券失败
                    String errorMsg = (String) result.getOrDefault("verify_msg", "验券失败");
                    log.warn("验券失败: msg={}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                }
            } else {
                // API调用失败
                String errorMsg = (String) result.getOrDefault("error_message", "验券接口调用失败");
                Boolean shouldRetry = (Boolean) result.getOrDefault("should_retry", false);
                Integer errorCode = (Integer) result.getOrDefault("error_code", 0);
                
                if (shouldRetry) {
                    log.warn("验券接口调用失败，建议重试: errorCode={}, msg={}", errorCode, errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg + "，请稍后重试");
                } else {
                    log.error("验券接口调用失败: errorCode={}, msg={}", errorCode, errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                }
            }
        } catch (Exception e) {
            log.error("调用抖音验券接口异常", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }
    
    /**
     * 撤销核销接口 (EDP调用抖音)
     * 当核销操作有误时，可调用此接口撤销核销记录
     * 注意: 验券超过一个小时就不可再撤销
     *
     * @param request 包含核销ID、凭证ID和批量撤销信息等
     * @return 撤销结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/cancel-verification")
    public ResultEntity cancelVerification(@RequestBody CancelVerificationRequest request) {
        try {
            log.info("开始调用抖音撤销核销接口, request={}",JSON.toJSONString(request));
            // 检查必要参数 - 至少需要提供一种撤销方式
            boolean hasBasicParams = StringUtils.isNotEmpty(request.getVerifyId()) && StringUtils.isNotEmpty(request.getCertificateId());

//            boolean hasBatchCancelInfoList = request.getBatchCancelInfoList() != null &&
//                                            !request.getBatchCancelInfoList().isEmpty();
//
//            if (!hasBasicParams && !hasBatchCancelInfoList) {
//                return error(ResultEnum.ERP_MARK_ADD_ERROR, "撤销核销需要提供核销ID和凭证ID，或批量撤销信息");
//            }
            
            // 获取当前登录门店编码
            String storeCode = loginCacheUtil.getStoreCode();
            if (StringUtils.isEmpty(storeCode)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到当前登录的门店信息");
            }
            
            // 从数据库获取抖音门店ID
            String poiId = daoMapper.findLifePoi(storeCode);
            if (StringUtils.isEmpty(poiId)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到门店对应的抖音门店ID");
            }
            
            // 调用撤销核销服务（使用完整参数版本）
            Map<String, Object> result = douyinSyncService.cancelVerification(request, poiId);
            
            if ((boolean) result.getOrDefault("success", false)) {
                // 获取撤销结果详情
                List<Map<String, Object>> cancelResults = (List<Map<String, Object>>) result.get("cancel_results");
                if (cancelResults != null && !cancelResults.isEmpty()) {
                    log.info("撤销核销成功: 共撤销{}条记录", cancelResults.size());
                } else {
                    log.info("撤销核销成功: verifyId={}", request.getVerifyId());
                }
                return success(result);
            } else {
                // 获取错误信息并判断是否可重试
                String errorMsg = (String) result.getOrDefault("error_message", "撤销核销失败");
                Boolean shouldRetry = (Boolean) result.getOrDefault("should_retry", false);
                
                // 特殊错误处理
                if (errorMsg.contains("超过一个小时")) {
                    log.warn("撤销核销失败: 验券超过一个小时不可撤销");
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, "验券超过一个小时不可撤销");
                } else if (errorMsg.contains("无核销记录")) {
                    log.warn("撤销核销失败: 无核销记录可撤销");
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, "无核销记录可撤销");
                } else if (shouldRetry) {
                    log.warn("撤销核销接口调用失败，建议重试: msg={}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg + "，请稍后重试");
                } else {
                    log.error("撤销核销失败: msg={}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                }
            }
        } catch (Exception e) {
            log.error("调用抖音撤销核销接口异常", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 发券回调接口 (EDP调用抖音)
     * 用于通知抖音发券结果
     * 
     * 根据抖音开放平台文档：
     * 1. 抖音侧先申请服务商发码，如果能同步返回结果则不需要本接口，如果异步返回结果则调用本接口通知抖音。
     * 2. 异步发码，发码失败的场景，也需要在本接口中返回 result=2 的结果。
     * 3. 10分钟后未调用本接口，抖音会发起退款申请。
     *
     * @param request 包含抖音订单ID、发券结果、券码列表、失败原因代码和失败原因描述
     * @return 回调结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/issue-coupon-callback")
    public ResultEntity issueCouponCallback(@RequestBody IssueCouponCallbackRequest request) {
        try {
            log.info("开始调用抖音发券回调接口, request={}",JSON.toJSONString(request));

            // 参数校验
            if (StringUtils.isEmpty(request.getOrderId())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "抖音订单ID不能为空");
            }
            
            if (request.getResult() == null) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "发券结果不能为空");
            }
            
            if (request.getResult() == 1 && (request.getCodes() == null || CollectionUtils.isEmpty(request.getCodes()))) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "发券成功时必须提供券码列表");
            }
            
            if (request.getResult() == 2 && request.getFailReason() == null) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "发券失败时必须提供失败原因代码");
            }
            
            // 如果提供了自定义失败原因描述，使用它
            if (request.getResult() == 2 && request.getFailReason() == 20 && StringUtils.isNotEmpty(request.getFailReasonDesc())) {
                log.info("使用自定义失败原因描述: {}", request.getFailReasonDesc());
            }
            
            // 调用发券回调服务
            Map<String, Object> callbackResult = douyinSyncService.issueCouponCallback(request);
            
            if ((boolean) callbackResult.getOrDefault("success", false)) {
                return success(callbackResult);
            } else {
                // 获取错误信息并判断是否需要重试
                String errorMsg = (String) callbackResult.getOrDefault("error_message", "发券回调失败");
                Boolean shouldRetry = (Boolean) callbackResult.getOrDefault("should_retry", false);
                
                // 特殊错误处理
                if (errorMsg.contains("超时未回调")) {
                    log.warn("发券回调失败: 超时未回调自动发码失败，已发起退款，不要再回调");
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, "超时未回调自动发码失败，已发起退款");
                } else if (shouldRetry) {
                    log.warn("发券回调失败，建议重试: {}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                } else {
                    log.error("发券回调失败: {}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                }
            }
        } catch (Exception e) {
            log.error("调用抖音发券回调接口异常", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 券状态批量查询接口 (EDP调用抖音)
     * 查询抖音券码和三方券码的状态
     *
     * @param request 包含加密券码和订单ID
     * @return 查询结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/certificate-status")
    public ResultEntity certificateStatus(@RequestBody CertificateStatusRequest request) {
        try {
            log.info("开始调用抖音券状态批量查询接口, encryptedCode={}, orderId={}", request.getEncryptedCode(), request.getOrderId());

            // 参数校验
            if (StringUtils.isEmpty(request.getEncryptedCode()) && StringUtils.isEmpty(request.getOrderId())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "加密券码和订单ID至少提供一个");
            }

            String storeCode = loginCacheUtil.getStoreCode();
            if (StringUtils.isEmpty(storeCode)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到当前登录的门店信息");
            }

            // 调用券状态查询服务
            Map<String, Object> result = douyinSyncService.queryCertificateStatus(request.getEncryptedCode(), request.getOrderId());

            // 判断结果
            if ((boolean) result.getOrDefault("success", false)) {
                return success(result);
            } else {
                // 获取错误信息并判断是否需要重试
                String errorMsg = (String) result.getOrDefault("error_message", "券状态查询失败");
                Boolean shouldRetry = (Boolean) result.getOrDefault("should_retry", false);

                if (shouldRetry) {
                    log.warn("券状态查询失败，建议重试: {}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                } else {
                    log.error("券状态查询失败: {}", errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                }
            }
        } catch (Exception e) {
            log.error("调用抖音券状态批量查询接口异常", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 退款审核回调接口 (EDP调用抖音)
     * 用于通知抖音退款审核结果
     * 
     * 根据抖音开放平台文档：
     * 1. 用于通知抖音退款审核结果，支持同意或拒绝退款。
     * 2. 拒绝退款时必须提供拒绝原因。
     * 3. extra.error_code、data.error_code 均为 0 代表回调成功。
     *
     * @param request 包含抖音售后单ID、订单ID、审核结果和拒绝原因等
     * @return 回调结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/refund-audit-callback")
    public ResultEntity refundAuditCallback(@RequestBody RefundAuditCallbackRequest request) {
        try {
            log.info("开始调用抖音退款审核回调接口, request={}", JSON.toJSONString(request));
            
            // 参数校验
            if (StringUtils.isEmpty(request.getBizUniqKey())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "抖音售后单ID不能为空");
            }
            
            if (request.getAuditResult() == null) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "审核结果不能为空");
            }
            
            if (request.getAuditResult() == 2 && StringUtils.isEmpty(request.getReason())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "拒绝退款时必须提供拒绝原因");
            }
            
            // 检查必填的订单信息
            if (StringUtils.isEmpty(request.getOrderId())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "抖音订单ID不能为空");
            }
            
            if (StringUtils.isEmpty(request.getOrderOutId())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "外部订单ID不能为空");
            }
            
            // 检查退款信息列表（如果有）
            if (request.getRefundInfoList() != null && !request.getRefundInfoList().isEmpty()) {
                for (RefundAuditCallbackRequest.RefundInfo refundInfo : request.getRefundInfoList()) {
                    if (StringUtils.isEmpty(refundInfo.getOrderItemId())) {
                        log.warn("退款信息中缺少订单项ID");
                    }
                    
                    // 检查凭证信息
                    if (refundInfo.getCertificate() != null && !refundInfo.getCertificate().isEmpty()) {
                        for (RefundAuditCallbackRequest.Certify certify : refundInfo.getCertificate()) {
                            if (StringUtils.isEmpty(certify.getCertificateId()) && StringUtils.isEmpty(certify.getCode())) {
                                log.warn("凭证信息中缺少凭证ID和券码");
                            }
                        }
                    }
                }
            }
            
            // 调用退款审核回调服务
            Map<String, Object> callbackResult = douyinSyncService.refundAuditCallback(request);
            
            if ((boolean) callbackResult.getOrDefault("success", false)) {
                log.info("退款审核回调成功: bizUniqKey={}, auditResult={}, orderId={}", 
                        request.getBizUniqKey(), request.getAuditResult(), request.getOrderId());
                return success(callbackResult);
            } else {
                // 获取错误信息并判断是否需要重试
                String errorMsg = (String) callbackResult.getOrDefault("error_message", "退款审核回调失败");
                Boolean shouldRetry = (Boolean) callbackResult.getOrDefault("should_retry", false);
                
                // 根据是否需要重试返回不同的错误类型
                if (shouldRetry) {
                    log.warn("退款审核回调失败，建议重试: bizUniqKey={}, orderId={}, msg={}", 
                            request.getBizUniqKey(), request.getOrderId(), errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg + "，请稍后重试");
                } else {
                    log.error("退款审核回调失败: bizUniqKey={}, orderId={}, msg={}", 
                            request.getBizUniqKey(), request.getOrderId(), errorMsg);
                    return error(ResultEnum.ERP_MARK_ADD_ERROR, errorMsg);
                }
            }
        } catch (Exception e) {
            log.error("调用抖音退款审核回调接口异常: bizUniqKey={}, orderId={}", 
                    request.getBizUniqKey(), request.getOrderId(), e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }


    /**
     * 手动触发核销状态同步到抖音
     *
     * @param request 包含券码
     * @return 同步结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/verification")
    public ResultEntity syncVerification(@RequestBody SyncVerificationRequest request) {
        try {
            log.info("手动触发核销状态同步, couponCode={}", request.getCouponCode());
            
            // 查询券信息
            DouyinCoupon coupon = douyinCouponMapper.selectByCouponCode(request.getCouponCode());
            if (coupon == null) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到指定券码");
            }
            
            // 检查券状态
            if (!"1".equals(coupon.getCouponStatus())) { // 1=已使用
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "该券未被核销，无法同步核销状态");
            }
            DouyinCouponRule rule = douyinCouponRuleMapper.selectById(coupon.getCouponRuleId());
            // 执行同步
            boolean result = douyinSyncService.syncVerificationStatus(coupon,rule);
            if (result) {
                return success(true);
            } else {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "核销状态同步失败，请查看日志了解详情");
            }
        } catch (Exception e) {
            log.error("手动触发核销状态同步异常: couponCode={}", request.getCouponCode(), e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "系统异常: " + e.getMessage());
        }
    }



} 
package com.besttop.marketing.controller.thirdparty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.marketing.model.thirdparty.nuonuo.param.NuoNuoElecronicInvoiceInfo;
import com.besttop.marketing.model.thirdparty.param.Email;
import com.besttop.marketing.service.thirdparty.NuoNuoInvoiceService;
import lombok.extern.slf4j.Slf4j;
import nuonuo.open.sdk.NNException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Set;

/**
 * <p>Title: NuoNuoInvoiceController</p>
 * <p>Description: NuoNuoInvoiceController 诺诺申请电子发票接口</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2019/1/21 18:18 Create by ChenWei
 * </pre>
 */
@Slf4j
@RestController
@RequestMapping("/nuonuo/invoice")
public class NuoNuoInvoiceController {

    @Autowired
    private NuoNuoInvoiceService nuoNuoInvoiceService;
    @Autowired
    private RedisLockUtil redisLockUtil;

    /**
     * @param invoiceInfo
     * @return com.besttop.common.model.ResultEntity<java.util.Set < java.util.Map < java.lang.String, java.lang.Object>>>
     * @methodName requestBillingManual
     * @description 手工开票非销售单
     * <AUTHOR>
     * @date 2020/6/18 16:12
     */
    @PostMapping("/requestBillingManual")
    @ResponseBody
    public ResultEntity<Set<Map<String, Object>>> requestBillingManual(@RequestBody @Validated(GroupAdd.class) NuoNuoElecronicInvoiceInfo invoiceInfo) {
        log.info("NuoNuoInvoiceController.requestBillingManual()");
        return nuoNuoInvoiceService.requestBillingManual(invoiceInfo);
    }


    /**
     * @methodName requestBilling
     * @description 请求开具发票V2.0.0
     * @param invoiceInfo
     * @return com.besttop.common.model.ResultEntity<java.util.Set<java.util.Map<java.lang.String,java.lang.Object>>>
     * <AUTHOR>
     * @date 2020/6/30 14:09
     */
    @PostMapping("/requestBilling")
    @ResponseBody
    public ResultEntity<Set<Map<String, Object>>> requestBilling(@RequestBody @Validated(GroupAdd.class) NuoNuoElecronicInvoiceInfo invoiceInfo) {
        log.info("NuoNuoInvoiceController.requestBilling()V2.0.0");
        return nuoNuoInvoiceService.requestBilling(invoiceInfo);
    }


    @PostMapping("/queryInvoiceResult")
    @ResponseBody
    public ResultEntity<JSONObject> queryInvoiceResult(@RequestBody NuoNuoElecronicInvoiceInfo invoiceInfo){
        return nuoNuoInvoiceService.queryInvoiceResult(invoiceInfo);
    }

    @PostMapping("/requestBillingRetry")
    @ResponseBody
    public ResultEntity<JSONObject> requestBillingRetry(@RequestBody NuoNuoElecronicInvoiceInfo invoiceInfo){
        String key = "";
        try {
            key = redisLockUtil.getAuditExitLock(invoiceInfo.getInvoiceSerialNum()+"requestBillingRetry");
            return nuoNuoInvoiceService.requestBillingRetry(invoiceInfo);
        } catch (Exception e) {
            log.info("requestBillingRetry === error{}", JSON.toJSON(e.getStackTrace()));
            throw new NNException("诺诺重推发票异常: content =["+JSONObject.toJSON(e.getMessage())+"]"+e);
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }

    @PostMapping("/sendEmail")
    @ResponseBody
    public ResultEntity sendEmail(@RequestBody Email email){
        if (StringUtils.isBlank(email.getEmailAddress())){
            return new ResultEntity(ResultEnum.ERP_MARK_PARAM_ERROR,"请输入邮箱");
        }
        if (StringUtils.isBlank(email.getContent())){
            return new ResultEntity(ResultEnum.ERP_MARK_PARAM_ERROR,"发送内容不能为空");
        }
        return nuoNuoInvoiceService.sendEmail(email);
    }


    /**
     * @methodName requestBillingAcpp
     * @description 请求开具发票V2.0.0
     * @param invoiceInfo
     * @return com.besttop.common.model.ResultEntity<java.util.Set<java.util.Map<java.lang.String,java.lang.Object>>>
     * <AUTHOR>
     * @date 2020/6/30 14:09
     */
    @PostMapping("/requestBillingAcpp")
    @ResponseBody
    public ResultEntity<Set<Map<String, Object>>> requestBillingAcpp(@RequestBody @Validated(GroupAdd.class) NuoNuoElecronicInvoiceInfo invoiceInfo) {
        log.info("NuoNuoInvoiceController.requestBillingAcpp()V2.0.0");
        return nuoNuoInvoiceService.requestBillingAcpp(invoiceInfo);
    }
}

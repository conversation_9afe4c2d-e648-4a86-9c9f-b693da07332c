package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponRule;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponRuleService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 抖音券规则单 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/douyin/coupon-rules")
public class DouyinCouponRuleController extends BaseController {

    @Autowired
    private DouyinCouponRuleService douyinCouponRuleService;

    /**
     * 创建新的抖音券规则单，提交后状态为'待同步'
     *
     * @param rule 抖音券规则单对象
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/addRule")
    public ResultEntity addRule(@RequestBody @Valid DouyinCouponRule rule) {
        try {
            return success(douyinCouponRuleService.add(rule));
        } catch (Exception e) {
            log.error("新增抖音券规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改抖音券规则单，已启用的规则单修改后会触发重新向抖音推送和审核
     *
     * @param rule 抖音券规则单对象
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/updateRule")
    public ResultEntity updateRule(@RequestBody @Valid DouyinCouponRule rule) {
        try {
            return success(douyinCouponRuleService.update(rule));
        } catch (Exception e) {
            log.error("修改抖音券规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除券规则单 仅'待审核'状态可删
     *
     * @param rule 券规则单ID
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/delete")
    public ResultEntity delete(@RequestBody @Valid DouyinCouponRule rule) {
        try {
            if(rule.getId() == null || rule.getId().isEmpty()) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "券规则单ID不能为空");
            }
            douyinCouponRuleService.delete(rule.getId());
            return success();
        } catch (Exception e) {
            log.error("删除抖音券规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 查询券规则单详情
     * 
     * @param rule 券规则单ID
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/detail")
    @RedisCacheConvertEnable
    public ResultEntity detail(@RequestBody @Valid DouyinCouponRule rule) {
        try {
            if(rule.getId() == null || rule.getId().isEmpty()) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "券规则单ID不能为空");
            }
            return success(douyinCouponRuleService.detail(rule.getId()));
        } catch (Exception e) {
            log.error("查询抖音券规则单详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询详情失败：" + e.getMessage());
        }
    }

    /**
     * 查询券规则单列表
     * 支持分页、按store_code、状态、日期等筛选
     * 
     * @param params 查询参数
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/list")
    @RedisCacheConvertEnable
    public ResultEntity list(@RequestBody DouyinCouponRule params) {
        try {
            return success(new PageInfo<>(douyinCouponRuleService.list(params)));
        } catch (Exception e) {
            log.error("查询抖音券规则单列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询列表失败：" + e.getMessage());
        }
    }

    /**
     * 终止券规则单
     * 将券规则单状态变为'已终止'，可能需要调用抖音接口下架商品
     * 
     * @param rule 券规则单ID
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/cancel")
    public ResultEntity cancel(@RequestBody @Valid DouyinCouponRule rule) {
        try {
            if(rule.getId() == null || rule.getId().isEmpty()) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "券规则单ID不能为空");
            }
            douyinCouponRuleService.cancel(rule.getId());
            return success();
        } catch (Exception e) {
            log.error("终止抖音券规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "终止失败：" + e.getMessage());
        }
    }

    /**
     * 同步库存到抖音
     * 手动触发指定规则的库存同步到抖音
     * 
     * @param id 券规则单ID
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/sync-stock")
    public ResultEntity syncStock(@RequestParam("id") String id) {
        try {
            douyinCouponRuleService.syncStock(id);
            return success();
        } catch (Exception e) {
            log.error("同步抖音券规则单库存失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "同步库存失败：" + e.getMessage());
        }
    }
} 
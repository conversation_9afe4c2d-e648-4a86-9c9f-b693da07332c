package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.dto.gift.DouyinGiftVerifyRequest;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponVerifyResult;
import com.besttop.marketing.dto.gift.DouyinGiftCancelRequest;
import com.besttop.marketing.dto.gift.DouyinGiftCancelResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinGiftVerificationRecord;
import com.besttop.marketing.model.thirdparty.douyin.DouyinGiftRefundRecord;
import com.besttop.marketing.model.manual.ManualGiftRecord;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinGiftVerificationService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinGiftRefundService;
import com.besttop.marketing.service.manual.ManualGiftRecordService;
import com.besttop.marketing.util.SpringContext;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抖音券控制器
 * 提供券相关查询和操作API
 */
@RestController
@RequestMapping("/douyin")
@Slf4j
public class DouyinCouponController extends BaseController {

    @Autowired
    private DouyinCouponService douyinCouponService;

    /**
     * 查询券列表
     *
     * @param params 查询参数，支持按订单、用户、状态、门店等筛选
     * @return 券列表结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/coupons/list")
    public ResultEntity listCoupons(@RequestBody DouyinCoupon params) {
        try {
            log.info("查询抖音券列表: {}", params);
            return success(douyinCouponService.listCoupons(params));
        } catch (Exception e) {
            log.error("查询抖音券列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询券详情
     *
     * @param params 包含couponId或couponCode的参数
     * @return 券详情结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/coupons/detail")
    public ResultEntity getCouponDetail(@RequestBody Map<String, Object> params) {
        try {
            String couponId = params.containsKey("couponId") ? String.valueOf(params.get("couponId")) : 
                            params.containsKey("couponCode") ? String.valueOf(params.get("couponCode")) : "";
            log.info("查询抖音券详情: {}", couponId);
            return success(douyinCouponService.getCouponDetail(couponId));
        } catch (Exception e) {
            log.error("查询抖音券详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询详情失败：" + e.getMessage());
        }
    }

    /**
     * 核销抖音券
     * 核销成功后会异步同步核销状态到抖音平台
     *
     * @param params 核销参数
     * @return 核销结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/coupon/verify")
    public ResultEntity verifyCoupon(@RequestBody DouyinGiftVerifyRequest request) {
        try {

            log.info("核销抖音券: {}", request.getCouponCode());
            
            DouyinCouponVerifyResult result = douyinCouponService.verifyCoupon(request);

            if (result.isSuccess()) {
                log.info("核销成功: {}, 类型: {}", request.getCouponCode(), result.getVerificationType());
                return success(result);
            } else {
                log.warn("核销失败: {}, 原因: {}", request.getCouponCode(), result.getMessage());
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "核销失败：" + result.getMessage());
            }
        } catch (Exception e) {
            log.error("核销抖音券失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "核销失败：" + e.getMessage());
        }
    }

    /**
     * 撤销礼品券核销
     */
    @PostMapping("/coupon/gift/cancel")
    public ResultEntity cancelGiftVerification(@RequestBody DouyinGiftCancelRequest request) {
        try {
            log.info("撤销礼品券核销: couponCode={}, storeCode={}, staffId={}",
                    request.getCouponCode(), request.getStoreCode(), request.getStaffId());

            // 调用礼品券撤销服务
            DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);
            DouyinGiftCancelResult result = giftService.cancelGiftVerification(request);

            if (result.isSuccess()) {
                log.info("礼品券核销撤销成功: couponCode={}", request.getCouponCode());
                return success(result);
            } else {
                log.warn("礼品券核销撤销失败: couponCode={}, error={}",
                        request.getCouponCode(), result.getMessage());
                return error(ResultEnum.ERP_MARK_ADD_ERROR, result.getMessage());
            }

        } catch (Exception e) {
            log.error("撤销礼品券核销失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "撤销失败：" + e.getMessage());
        }
    }

    /**
     * 查询抖音礼品核销记录
     */
    @PostMapping("/gift/verification/list")
    public ResultEntity listGiftVerificationRecords(@RequestBody Map<String, Object> params) {
        try {
            log.info("查询抖音礼品核销记录: {}", params);
            // 调用礼品核销记录查询服务
            DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);

            return success(new PageInfo<>(giftService.listVerificationRecords(params)));
        } catch (Exception e) {
            log.error("查询抖音礼品核销记录失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询礼品退款记录
     */
    @PostMapping("/gift/refund/list")
    public ResultEntity listGiftRefundRecords(@RequestBody Map<String, Object> params) {
        try {
            log.info("查询抖音礼品退款记录: {}", params);

            // 调用礼品退款记录查询服务
            DouyinGiftRefundService refundService = SpringContext.getBean(DouyinGiftRefundService.class);

            return success(new PageInfo<>(refundService.listRefundRecords(params)));
        } catch (Exception e) {
            log.error("查询抖音礼品退款记录失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询礼品券详情（包含核销和发放信息）
     */
    @PostMapping("/gift/detail")
    public ResultEntity getGiftCouponDetail(@RequestBody Map<String, Object> params) {
        try {
            String couponCode = String.valueOf(params.getOrDefault("couponCode", ""));
            log.info("查询抖音礼品券详情: {}", couponCode);

            // 构建完整的礼品券信息
            Map<String, Object> result = new HashMap<>();

            // 1. 基础券信息
            Map<String, Object> couponDetail = douyinCouponService.getCouponDetail(couponCode);
            result.put("coupon", couponDetail);

            // 2. 核销记录
            DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);
            DouyinGiftVerificationRecord verificationRecord = giftService.getVerificationRecord(couponCode);
            result.put("verification", verificationRecord);

            // 3. 礼品发放记录
            if (verificationRecord != null && StringUtils.isNotEmpty(verificationRecord.getGiftRecordId())) {
                ManualGiftRecordService manualGiftRecordService = SpringContext.getBean(ManualGiftRecordService.class);
                ManualGiftRecord giftRecord = manualGiftRecordService.getById(verificationRecord.getGiftRecordId());
                result.put("giftRecord", giftRecord);
            }

            // 4. 退款记录
            DouyinGiftRefundService refundService = SpringContext.getBean(DouyinGiftRefundService.class);
            List<DouyinGiftRefundRecord> refundRecords = refundService.getRefundRecords(couponCode);
            result.put("refundRecords", refundRecords);

            return success(result);
        } catch (Exception e) {
            log.error("查询抖音礼品券详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据顾客编码查询顾客已核销的抖音礼品券
     */
    @PostMapping("/gift/verification/customer")
    public ResultEntity listCustomerGiftVerifications(@RequestBody Map<String, Object> params) {
        try {
            String customerCode = String.valueOf(params.getOrDefault("customerCode", ""));
            log.info("查询顾客已核销的抖音礼品券: {}", customerCode);

            // 调用服务查询
            DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);
            List<DouyinGiftVerificationRecord> records = giftService.listCustomerGiftVerifications(customerCode);

            return success(new PageInfo<>(records));
        } catch (Exception e) {
            log.error("查询顾客已核销的抖音礼品券失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询失败：" + e.getMessage());
        }
    }

}
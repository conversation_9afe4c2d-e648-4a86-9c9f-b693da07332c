package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.marketing.annotation.DouyinSpiLog;
import com.besttop.marketing.annotation.DouyinSpiRecord;
import com.besttop.marketing.service.thirdparty.douyin.DouyinProSpiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 抖音Pro版SPI控制器
 * 处理抖音平台Pro版功能的回调请求，包括三方码发布、核销团购、订单查询等
 */
@RestController
@RequestMapping("/spi/douyin/pro")
@Slf4j
public class DouyinProSpiController extends BaseController {

    @Autowired
    private DouyinProSpiService douyinProSpiService;

    /**
     * 预下单接口
     * 抖音请求EDP进行预下单处理，包括库存检查、价格计算、用户限购验证
     */
    @PostMapping("/precreate-order")
    @DouyinSpiLog(spiType = "PRECREATE_ORDER", description = "抖音Pro版预下单接口")
    @DouyinSpiRecord(spiType = "PRECREATE_ORDER", businessIdKey = "pre_order_id")
    public Map<String, Object> precreateOrder(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音Pro版预下单请求: {}", requestBody);
            return douyinProSpiService.handlePrecreateOrder(requestBody);
        } catch (Exception e) {
            log.error("处理抖音Pro版预下单请求失败", e);
            return buildErrorResponse(500, "处理预下单失败：" + e.getMessage());
        }
    }

    /**
     * 三方码发券接口
     * 抖音请求EDP生成三方券码，支持同步和异步两种模式
     */
    @PostMapping("/create-coupon")
    @DouyinSpiLog(spiType = "CREATE_THIRD_PARTY_COUPON", description = "抖音Pro版三方码发券接口")
    @DouyinSpiRecord(spiType = "CREATE_THIRD_PARTY_COUPON", businessIdKey = "order_id")
    public Map<String, Object> createCoupon(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音Pro版发券请求: {}", requestBody);
            return douyinProSpiService.handleCreateCoupon(requestBody);
        } catch (Exception e) {
            log.error("处理抖音Pro版发券请求失败", e);
            return buildErrorResponse(500, "处理发券失败：" + e.getMessage());
        }
    }

    /**
     * 退款申请接口
     * 抖音请求EDP进行退款审核，支持自动审核和人工审核
     */
    @PostMapping("/refund-apply")
    @DouyinSpiLog(spiType = "REFUND_APPLY_PRO", description = "抖音Pro版退款申请接口")
    @DouyinSpiRecord(spiType = "REFUND_APPLY_PRO", businessIdKey = "refund_id")
    public Map<String, Object> refundApply(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音Pro版退款申请请求: {}", requestBody);
            return douyinProSpiService.handleRefundApply(requestBody);
        } catch (Exception e) {
            log.error("处理抖音Pro版退款申请请求失败", e);
            return buildErrorResponse(500, "处理退款申请失败：" + e.getMessage());
        }
    }

    /**
     * 信息同步接口
     * 抖音同步退款审核状态变更、超时自动审核等信息
     */
    @PostMapping("/refund-notice")
    @DouyinSpiLog(spiType = "REFUND_NOTICE_PRO", description = "抖音Pro版信息同步接口")
    @DouyinSpiRecord(spiType = "REFUND_NOTICE_PRO", businessIdKey = "order_id")
    public Map<String, Object> refundNotice(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音Pro版信息同步请求: {}", requestBody);
            return douyinProSpiService.handleRefundNotice(requestBody);
        } catch (Exception e) {
            log.error("处理抖音Pro版信息同步请求失败", e);
            return buildErrorResponse(500, "处理信息同步失败：" + e.getMessage());
        }
    }

    /**
     * 订单状态查询接口
     * 抖音查询EDP侧订单状态信息，确保两边数据状态一致
     */
    @PostMapping("/order/order-query")
    @DouyinSpiLog(spiType = "ORDER_STATUS_QUERY", description = "抖音Pro版订单状态查询接口")
    @DouyinSpiRecord(spiType = "ORDER_STATUS_QUERY", businessIdKey = "order_ids")
    public Map<String, Object> orderStatusQuery(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("接收到抖音Pro版订单状态查询请求: {}", requestBody);
            return douyinProSpiService.handleOrderStatusQuery(requestBody);
        } catch (Exception e) {
            log.error("处理抖音Pro版订单状态查询请求失败", e);
            return buildErrorResponse(500, "处理订单状态查询失败：" + e.getMessage());
        }
    }

    /**
     * 构建错误响应
     */
    private Map<String, Object> buildErrorResponse(int errorCode, String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("err_no", errorCode);
        response.put("err_msg", errorMessage);
        return response;
    }
}
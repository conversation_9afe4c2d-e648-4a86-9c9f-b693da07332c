package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.service.thirdparty.douyin.DouyinAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 抖音授权管理控制器
 * 处理授权Token刷新和授权门店列表查询等操作
 */
@RestController
@RequestMapping("/douyin/auth")
@Slf4j
public class DouyinAuthController extends BaseController {

    @Autowired
    private DouyinAuthService douyinAuthService;

    /**
     * 刷新门店授权Token
     * 根据门店编码刷新对应的抖音授权Token
     * @return 刷新结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/refresh")
    public ResultEntity refreshAuthToken() {
        try {
            boolean result = douyinAuthService.refreshAuthToken();
            return success(result);
        } catch (Exception e) {
            log.error("刷新授权Token异常", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "刷新授权Token发生异常: " + e.getMessage());
        }
    }

} 
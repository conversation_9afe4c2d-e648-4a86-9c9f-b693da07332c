package com.besttop.marketing.controller.thirdparty;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.mapper.card.CardDetailMapper;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.enums.PayEnums;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import com.besttop.marketing.model.thirdparty.param.RefundmentParams;
import com.besttop.marketing.model.thirdparty.result.PaymentResult;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import com.besttop.marketing.service.thirdparty.RefundmentService;
import com.besttop.marketing.util.CheckUtil;
import com.besttop.marketing.util.CollectionUtil;
import com.besttop.marketing.util.Tools;
import com.besttop.redis.model.RedisMultiLockObject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: RefundController</p>
 * <p>Description: RefundController 退款</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/13 16:21 Create by Sissi
 * </pre>
 */
@Slf4j
@RestController
@RequestMapping("/refund")
public class RefundmentController extends BaseController {

    @Autowired
    private RefundmentService refundmentService;
    @Autowired
    private ShoppingOrderService orderService;
    @Autowired
    private CardDetailMapper cardDetailMapper;
    @Autowired
    private RedisLockUtil redisLockUtil;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payRefund
     * @description 支付退款
     * <AUTHOR>
     * @date 2020/5/3 11:12
     */
    @PostMapping("/payRefund")
    @OperationLog(opName = "支付退款")
    public ResultEntity payRefund(@RequestBody RefundmentParams param) {
        RedisMultiLockObject lockObject = null;
    	log.info("====payRefund==param:{}", JSON.toJSONString(param));
    	PaymentResult payRefund = null;
    	try {
            // 批量锁定
            log.info("payRefund获取缓存锁");
            lockObject = this.redisLockUtil.getPaySaveLock(param.getOrderCodes(),false);
            if (null == lockObject) {
                log.info("payRefund获取缓存锁失败");
                return paramError("退款中，请勿重复操作");
            }
            String msg = refundmentService.checkRefundParam(param, "payRefund");
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            payRefund = refundmentService.payRefund(param);
//            return payRefund;
        } catch (Exception e) {
            log.info("====payRefund==退款失败==error:{}", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        } finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("payRefund释放缓存锁 orderCodes={}",param.getOrderCodes());
        }
        return success(payRefund);
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundOrder
     * @description 退款
     * <AUTHOR>
     * @date 2020/5/4 10:55
     */
    @PostMapping("/refundOrder")
    @OperationLog(opName = "退款")
    public ResultEntity refundOrder(@RequestBody @Validated(GroupAdd.class) RefundmentParams param) {
        RedisMultiLockObject lockObject = null;
        try {
            QueryWrapper<ShoppingOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("code", param.getOrderCodes());
            List<ShoppingOrder> orders = orderService.list(queryWrapper);
            if (CollectionUtil.isEmpty(orders)) {
                return paramError("单号无效");
            }
            // 批量锁定
            log.info("refundOrder获取缓存锁");
            lockObject = this.redisLockUtil.getPaySaveLock(param.getOrderCodes());
            if (null == lockObject) {
                log.info("refundOrder获取缓存锁失败");
                return paramError("退款中，请勿重复操作");
            }

            for (ShoppingOrder sourceOrder : orders) {
                if(sourceOrder.getStatus().equals(CommonEnums.SHOPPING_ORDER_STATUS_C.getCode())){
                    return paramError("订单已支付，不能退款，请生成退款单再操作");
                }
                if(sourceOrder.getStatus().equals(CommonEnums.SHOPPING_ORDER_STATUS_F.getCode())){
                    return  paramError("订单已退款完成");
                }
            }
            if (!orders.get(0).getType().equals(CommonEnums.SHOPPING_ORDER_TYPE_J.getCode())) {
                String msg = refundmentService.checkRefundParam(param, "refund");
                if (StringUtils.isNotBlank(msg)) {
                    return paramError(msg);
                }
            } else {
                List<RefundmentParam> list = param.getRefundmentParamList();
                if (CollectionUtil.isNotEmpty(list)) {
                    for (RefundmentParam refundmentParam : list) {
                        if (PayEnums.VALUECARD_PAY.getCode().equals(refundmentParam.getPayType())) {
                            QueryWrapper<CardDetail> qw = new QueryWrapper<>();
                            qw.eq("card_no", refundmentParam.getCardNo());
                            CardDetail detail = cardDetailMapper.selectOne(qw);
                            if (detail != null && !detail.getMagneticStripe().equals(refundmentParam.getTrackNumber())) {
                                return paramError("磁条号、卡号信息不是同一张卡，无法退款");
                            }
                            if (detail != null && Tools.isExpire(detail.getExpireTime())) {
                                return paramError("储值卡已过期，无法退款");
                            }
                        }
                    }
                }
            }
            PaymentResult result = refundmentService.refundOrder(param);
            
            ShoppingPayRecord record = result.getShoppingPayRecords().get(0);
            result
            	.setNewStatus(record.getStatus())
            	.setNewStatusName(record.getNewStatusName());
            return success(result);
//        	return result;
        } catch (Exception e) {
        	log.info("====refundOrder==退款失败==error:{}", JSON.toJSONString(e.getStackTrace()));
            ResultEntity resultEntity = error(ResultEnum.ERP_MARK_ADD_ERROR, "退款失败");
            return resultEntity;
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("refundOrder释放缓存锁 orderCodes={}",param.getOrderCodes());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundSave
     * @description 保存退款
     * <AUTHOR>
     * @date 2020/5/4 15:50
     */
    @PostMapping("refundSave")
    @OperationLog(opName = "保存退款")
    public ResultEntity refundSave(@RequestBody @Validated(GroupUpdate.class) RefundmentParams param) {
        RedisMultiLockObject lockObject = null;
        try {
            // 批量锁定
            log.info("refundSave获取缓存锁");
            lockObject = this.redisLockUtil.getPaySaveLock(param.getOrderCodes());
            if (null == lockObject) {
                log.info("refundSave获取缓存锁失败");
                return paramError("退款中，请勿重复操作");
            }
            String msg = refundmentService.checkRefundSaveParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            return success(refundmentService.refundSave(param));
        } catch (Exception e) {
            log.error("保存退款失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("refundSave释放缓存锁 orderCodes={}",param.getOrderCodes());
            if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
            }
        }
    }


    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundNoOriginalToApp
     * @description 小贝收银退款非原路返回
     * <AUTHOR>
     * @date 2020/5/4 10:55
     */
    @PostMapping("/refundNoOriginalToApp")
    @OperationLog(opName = "小贝收银退款非原路返回")
    public ResultEntity refundNoOriginalToApp(@RequestBody @Validated(GroupAdd.class) RefundmentParams param) {
        try {
            String msg = refundmentService.checkRefundParam(param, "refundApp");
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            PaymentResult result = refundmentService.refundNoOriginalToApp(param);
            return success(result);
        } catch (Exception e) {
            log.error("退款失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

}

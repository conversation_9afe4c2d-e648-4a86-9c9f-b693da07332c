package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.annotation.DouyinSpiLog;
import com.besttop.marketing.annotation.DouyinSpiRecord;
import com.besttop.marketing.dto.douyin.request.*;
import com.besttop.marketing.dto.douyin.response.*;
import com.besttop.marketing.mapper.common.DaoMapper;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSyncProService;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 抖音同步Pro版控制器
 * 处理抖音平台的核销团购相关接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Slf4j

@RestController
@RequestMapping("/douyin/sync/pro")
public class DouyinSyncProController extends BaseController {

    @Autowired
    private DouyinSyncProService douyinSyncProService;
    @Autowired
    private LoginCacheUtil loginCacheUtil;
    @Autowired
    private DaoMapper daoMapper;

    /**
     * 验券接口
     * 同时适用于抖音券码与三方券码
     * 抖音券码的核销需要先调用准备接口，再调用本接口
     * 三方券码的核销直接调用本接口即可
     *//*

    @PostMapping("/certificate/verify")
    @DouyinSpiLog(spiType = "certificate_verify", description = "验券")
    @DouyinSpiRecord(spiType = "certificate_verify", businessIdKey = "verify_token")
    public ResultEntity<DouyinCertificateVerifyResponse> certificateVerify(@RequestBody DouyinCertificateVerifyRequest request) {

        log.info("[抖音验券] 开始处理验券请求, verify_token: {}, poi_id: {}",
                request.getVerifyToken(), request.getPoiId());

        try {
            // 获取当前登录门店编码
            String storeCode = loginCacheUtil.getStoreCode();
            if (StringUtils.isEmpty(storeCode)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到当前登录的门店信息");
            }

            // 从数据库获取抖音门店ID
            String poiId = daoMapper.findLifePoi(storeCode);
            if (StringUtils.isEmpty(poiId)) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未找到门店对应的抖音门店ID");
            }

            request.setPoiId(poiId);
            DouyinCertificateVerifyResponse response = douyinSyncProService.handleCertificateVerify(request);

            log.info("[抖音验券] 验券处理完成, verify_token: {}, error_code: {}",
                    request.getVerifyToken(), response.getData().getErrorCode());

            return success(response);

        } catch (Exception e) {
            log.error("[抖音验券] 验券处理异常, verify_token: {}", request.getVerifyToken(), e);

            // 构建错误响应，符合抖音官方格式
            DouyinCertificateVerifyResponse errorResponse = new DouyinCertificateVerifyResponse();

            DouyinCertificateVerifyResponse.VerifyData data = new DouyinCertificateVerifyResponse.VerifyData();
            data.setErrorCode(9999);
            data.setDescription("验券处理失败: " + e.getMessage());
            errorResponse.setData(data);

            DouyinCertificateVerifyResponse.Extra extra = new DouyinCertificateVerifyResponse.Extra();
            extra.setErrorCode(9999);
            extra.setDescription("验券处理失败: " + e.getMessage());
            extra.setLogid("log_" + System.currentTimeMillis());
            extra.setNow(System.currentTimeMillis() / 1000);
            errorResponse.setExtra(extra);

            return error(ResultEnum.ERROR,errorResponse);
        }
    }

    *//**
     * 撤销核销接口
     * 针对误核销之后的撤销操作
     * 有时间限制，验券超过一个小时就不可再撤销
     *//*

    @PostMapping("/certificate/cancel")
    @DouyinSpiLog(spiType = "certificate_cancel", description = "撤销核销")
    @DouyinSpiRecord(spiType = "certificate_cancel", businessIdKey = "verify_id")
    public ResultEntity<DouyinCertificateCancelResponse> certificateCancel(
            @RequestBody DouyinCertificateCancelRequest request) {
        
        log.info("[抖音撤销核销] 开始处理撤销核销请求, verify_id: {}, certificate_id: {}", 
                request.getVerifyId(), request.getCertificateId());
        
        try {
            DouyinCertificateCancelResponse response = douyinSyncProService.handleCertificateCancel(request);
            
            log.info("[抖音撤销核销] 撤销核销处理完成, verify_id: {}, error_code: {}", 
                    request.getVerifyId(), response.getData().getErrorCode());
            
            return success(response);
            
        } catch (Exception e) {
            log.error("[抖音撤销核销] 撤销核销处理异常, verify_id: {}", request.getVerifyId(), e);
            return error(ResultEnum.ERROR, "撤销核销处理失败: " + e.getMessage());
        }
    }*/

    /**
     * 退款审核回调接口
     * 退款申请后，商家/服务商统一退款或拒绝退款的回调通知
     * 若在退款申请时能明确给出结果则不需要再回调
     */

    /*@PostMapping("/refund/audit")
    @DouyinSpiLog(spiType = "refund_audit", description = "退款审核回调")
    @DouyinSpiRecord(spiType = "refund_audit", businessIdKey = "after_sale_id")
    public ResultEntity<DouyinRefundAuditResponse> refundAudit(
            @RequestBody DouyinRefundAuditRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("[抖音退款审核回调] 开始处理退款审核回调, after_sale_id: {}, result: {}", 
                request.getAfterSaleId(), request.getResult());
        
        try {
            DouyinRefundAuditResponse response = douyinSyncProService.handleRefundAudit(request);
            
            log.info("[抖音退款审核回调] 退款审核回调处理完成, after_sale_id: {}, error_code: {}", 
                    request.getAfterSaleId(), response.getData().getErrorCode());
            
            return success(response);
            
        } catch (Exception e) {
            log.error("[抖音退款审核回调] 退款审核回调处理异常, after_sale_id: {}", request.getAfterSaleId(), e);
            return error(ResultEnum.ERROR, "退款审核回调处理失败: " + e.getMessage());
        }
    }*/
}
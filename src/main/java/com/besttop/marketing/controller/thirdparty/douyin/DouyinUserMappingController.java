package com.besttop.marketing.controller.thirdparty.douyin;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.thirdparty.douyin.DouyinUserMapping;
import com.besttop.marketing.service.thirdparty.douyin.DouyinUserMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 抖音用户映射控制器
 * 管理抖音用户与EDP用户的关联
 */
@RestController
@RequestMapping("/douyin/user-mappings")
@Slf4j
public class DouyinUserMappingController extends BaseController {

    @Autowired
    private DouyinUserMappingService douyinUserMappingService;

    /**
     * 查询用户映射列表
     *
     * @param params 查询参数
     * @return 用户映射列表
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/list")
    public ResultEntity list(@RequestBody Map<String, Object> params) {
        try {
            log.info("查询抖音用户映射列表: {}", params);
            return success(douyinUserMappingService.listUserMappings(params));
        } catch (Exception e) {
            log.error("查询抖音用户映射列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询列表失败：" + e.getMessage());
        }
    }

    /**
     * 新增/绑定用户映射
     *
     * @param userMapping 用户映射信息
     * @return 绑定结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/bind")
    public ResultEntity bind(@RequestBody DouyinUserMapping userMapping) {
        try {
            log.info("绑定抖音用户: {}", userMapping);
            return success(douyinUserMappingService.bindUser(userMapping));
        } catch (Exception e) {
            log.error("绑定抖音用户失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解绑用户映射
     *
     * @param params 解绑参数，至少包含mappingId
     * @return 解绑结果
     */
    @SuppressWarnings("rawtypes")
    @PostMapping("/unbind")
    public ResultEntity unbind(@RequestBody Map<String, Object> params) {
        try {
            String mappingId = String.valueOf(params.get("mappingId"));
            log.info("解绑抖音用户映射: {}", mappingId);
            return success(douyinUserMappingService.unbindUser(mappingId));
        } catch (Exception e) {
            log.error("解绑抖音用户映射失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "解绑失败：" + e.getMessage());
        }
    }
} 
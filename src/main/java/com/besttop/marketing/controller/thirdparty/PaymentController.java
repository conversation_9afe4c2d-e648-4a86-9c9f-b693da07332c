package com.besttop.marketing.controller.thirdparty;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.besttop.marketing.model.enums.PayEnums;
import com.besttop.marketing.service.dmall.DmallPayPushOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.pay.PayParam;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.model.shopping.param.OrderParam;
import com.besttop.marketing.model.shopping.param.PayOrderParam;
import com.besttop.marketing.model.thirdparty.param.PaymentMallParam;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.model.thirdparty.result.PaymentResult;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import com.besttop.marketing.service.thirdparty.PaymentService;
import com.besttop.marketing.util.PayOrderRedisLockUtil;
import com.besttop.redis.model.RedisMultiLockObject;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <p>
 * Title: PaymentController
 * </p>
 * <p>
 * Description: PaymentController 支付
 * </p>
 * <p>
 * Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 *
 * <pre>
 * Histroy:
 *       2020/3/4 10:07 Create by Sissi
 *          </pre>
 */
@Slf4j
@RestController
@RequestMapping("/payment")
public class PaymentController extends BaseController {

    @Autowired
    private PaymentService paymentService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private PayOrderRedisLockUtil payOrderRedisLockUtil;
	@Lazy
	@Autowired
	private ShoppingOrderService orderService;
    @Autowired
    private DmallPayPushOrderService dmallPayPushOrderService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payOrder
     * @description 支付订单
     * <AUTHOR>
     * @date 2020/4/10 14:47
     */
    @PostMapping("/payOrder")
    @OperationLog(opName = "支付订单")
    public ResultEntity payOrder(@RequestBody @Validated(GroupAdd.class) PayOrderParam param) {
        if (null == param.getOrderCodes() || param.getOrderCodes().size() == 0) {
            return paramError("----支付单号不能为空 !");
        }
        String payOrderLock = null;
        PaymentResult result = null;
        try {

            // 获取PayOrder锁
            log.info("[PaymentController] ----payOrder获取缓存锁.params:{}", JSON.toJSONString(param));
            payOrderLock = payOrderRedisLockUtil.getPayOrderLock(param.getOrderCodes().get(0));
            if (null == payOrderLock) {
                log.info("[PaymentController]----payOrder获取缓存锁失败.orderCode:" + JSON.toJSONString(param.getOrderCodes()));
                return paramError("支付中，请勿重复操作");
            }

            String msg = paymentService.checkPayParam(param);
            if (StringUtils.isNotBlank(msg)) {
                log.info("[PaymentController]----payOrder checkPayParam失败.orderCode:" + JSON.toJSONString(param.getOrderCodes()) + " msg:" + msg);
                return paramError(msg);
            }
            result = paymentService.payOrder(param);
//            ShoppingPayRecord data = result.getRecord();
//            if(result.getStatus() == 1) {
//            	result.setFlag(1);
//            }else {
//            	result.setFlag(-1);
//            }
            
        	result
        		.setNewStatus(result.getRecord().getStatus())
                .setQrcode(result.getRecord().getQrcode())
        		.setNewStatusName(result.getRecord().getNewStatusName());
            if(StringUtils.isBlank(result.getPayNumber())){
                result.setPayNumber(result.getRecord().getPayNumber());
            }
            if(StringUtils.isBlank(result.getPayVoucher())){
                result.setPayVoucher(result.getRecord().getPayVoucher());
            }
        	
            if (result.getFlag() == 1) {
            	log.info("====payOrder==result:{}", JSON.toJSONString(result));
                return success(result);
            } else {
                log.info("[PaymentController]----payOrder payOrder失败.orderCode:" + JSON.toJSONString(param.getOrderCodes()) + " msg:" + result.getMsg());
                return error(ResultEnum.ERP_MARK_ADD_ERROR, result.getMsg());
            }
        } catch (Exception e) {
            log.info("[PaymentController] 支付失败.params:" + JSON.toJSONString(param) + " error:" + JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        } finally {

            log.info("[PaymentController]----payOrder释放缓存锁 orderCodes={}", param.getOrderCodes());
            if(null != result && param.getPaymentParamList().stream()
                    .anyMatch(record ->record.getPayType().equals(PayEnums.DM_PAY_DQ.getCode()))){
                log.info("[PaymentController]----payOrder payNumber={}", result.getRecord().getPayNumber());
                dmallPayPushOrderService.getDmallOderInfo(result.getRecord().getPayNumber(),result.getRecord().getId());
            }
            redisLockUtil.releaseLock(payOrderLock);
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updatePaySatus
     * @description 更新支付状态
     * <AUTHOR>
     * @modifier Lex.Shaoli.Zhu
     * @modifyLog1 欠款还款时未更新还款流水(return_number), 导致一次还款出现多个还款流水号, 退款时无法查全支付记录
     * @modifyMethod1 com.besttop.marketing.service.thirdparty.impl.PaymentServiceImpl.updatePaySatus(PaymentParam)
     * @date 2021/1/6 15:08
     */
    @PostMapping("/updatePaySatus")
    @OperationLog(opName = "更新支付状态")
    public ResultEntity updatePaySatus(@RequestBody PaymentParam param) {
        if (StringUtils.isBlank(param.getPayVoucher())) {
            return paramError("支付凭证不能为空");
        }
//        String msg = paymentService.updatePaySatus(param);
        PaymentResult updatePay = paymentService.updatePaySatusWithPayRecord(param);
        ShoppingPayRecord data = updatePay.getRecord();
        updatePay
        	.setNewStatus(data.getStatus())
        	.setNewStatusName(data.getNewStatusName());
        
        if (updatePay.getFlag() != 1) {
            return paramError(updatePay.getMessage());
        }
        return success(updatePay);
//        return updatePaySatusWithPayRecord;
    }


    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName lottery
     * @description 手动抽奖
     * <AUTHOR>
     * @date 2021/05/25 11:17
     */
    @PostMapping("/manualLottery")
    @OperationLog(opName = "手动抽奖")
    public ResultEntity manualLottery(@RequestBody PayOrderParam param) {
        if (null != param) {
            log.info("====manualLottery====param:{}", JSON.toJSONString(param));
            if (CollectionUtils.isEmpty(param.getOrderCodes())) {
                return error(ResultEnum.COMMON_PARAM_ERROR, "订单号不能为空 ! ");
            }
        } else {
            return error(ResultEnum.COMMON_PARAM_ERROR, "请求参数为空! ");
        }
        try {
            this.paymentService.manualLottery(param.getOrderCodes());
        } catch (Exception e) {
            log.info("====抽奖发生异常, param:{}, exception:{}", JSON.toJSONString(param),
                    JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.COMMON_ERROR, "抽奖异常  ! ");
        }
        return success();
    }

	@PostMapping("/checkOrders")
	@OperationLog(opName = "检查订单能不能支付")
	public ResultEntity checkPayOrders(@RequestBody @Validated(GroupAdd.class) PayOrderParam param) {
		if (CollectionUtils.isEmpty(param.getOrderCodes())) {
			return error(ResultEnum.ERP_MARK_PARAM_ERROR, "待支付订单号不能为空 ！");
		}
		return this.paymentService.checkPayOrders(param.getOrderCodes());
	}

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName paySave
     * @description 保存收款
     * <AUTHOR>
     * @date 2020/4/10 14:47
     */
    @PostMapping("/paySave")
    @OperationLog(opName = "保存收款")
    public ResultEntity paySave(@RequestBody @Validated(GroupAdd.class) PayOrderParam param) {
        long l = System.currentTimeMillis();
        RedisMultiLockObject lockObject = null;
        PaymentResult result = null;
        try {
            // 批量锁定
            log.info("paySave获取缓存锁");
            lockObject = this.redisLockUtil.getPaySaveLock(param.getOrderCodes());
            if (null == lockObject) {
                log.info("paySave获取缓存锁失败");
                return paramError("支付中，请勿重复操作");
            }
            long jiaoyan = System.currentTimeMillis();
            String msg = this.paymentService.checkPaySaveParam(param);
            log.info("---校验保存入参合法性用时---" + (System.currentTimeMillis() - jiaoyan));
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            log.info("---支付接口用时间---" + (System.currentTimeMillis() - l));
            
            log.info("====paySave=begin");
            result = this.paymentService.paySave(param);
            
            log.info("====paySave=end=result:{}", JSON.toJSONString(result));
            if (result.getFlag() == 1) {
                return success(result);
            } else {
//                return error(ResultEnum.ERP_MARK_ADD_ERROR, result.getMsg());
            	log.info("====保存收款失败==msg:{}", result.getMsg());
                return error(ResultEnum.ERP_MARK_PARAM_ERROR, result.getMsg());
            }
        } catch (Exception e) {
            log.info("====保存收款失败==error:{}", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_MARK_PARAM_ERROR, e.getMessage());
        } finally {
            redisLockUtil.releaseBatchLock(lockObject);
            if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
            }
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payMallOrder
     * @description 小商城支付（只允许微信一次性支付）
     * <AUTHOR>
     * @date 2020/7/29 15:27
     */
    @PostMapping("/payMallOrder")
    @OperationLog(opName = "小商城支付")
    public ResultEntity payMallOrder(@RequestBody PaymentMallParam param) {
        try {
            String msg = paymentService.checkPayMallParam(param);
            if (msg != null) {
                return paramError(msg);
            }
            if (StringUtils.isEmpty(param.getOrderNo())) {
                return paramError("商户号orderNo不能为空!");
            }
            return success(paymentService.payMallOrder(param));
        } catch (Exception e) {
//            e.getStackTrace();
//            log.error("小商城支付异常" + e.getMessage(), e.getMessage());
            log.info("====payMallOrder==小商城支付异常==error:{}", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payApplet
     * @description 小程序商城支付请求
     * <AUTHOR>
     * @date 2020/8/12 15:52
     */
    @PostMapping("/payApplet")
    @OperationLog(opName = "小程序商城支付请求")
    public ResultEntity payApplet(@RequestBody PaymentMallParam param) {
        try {
            String msg = paymentService.checkPayMallParam(param);
            if (StringUtils.isEmpty(param.getOpenId())) {
                return paramError("微信小程序用户openId不能为空!");
            }
            if (StringUtils.isEmpty(param.getPayConfigCode())) {
                return paramError("支付配置编码不能为空!");
            }
            if (StringUtils.isEmpty(param.getStoreCode())) {
                return paramError("制单机构不能为空!");
            }
            if (msg != null) {
                return paramError(msg);
            }
            return paymentService.payApplet(param);
        } catch (Exception e) {
        	log.info("====payApplet==error:{}", JSON.toJSONString(e.getStackTrace()));
            log.error("小商城支付异常" + e.getMessage(), e.getMessage());
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payCheckApplet
     * @description 小程序商城支付验证
     * <AUTHOR>
     * @date 2020/8/14 10:12
     */
    @PostMapping("/payCheckApplet")
    @OperationLog(opName = "小程序商城支付验证")
    public ResultEntity payCheckApplet(@RequestBody PayParam param) {
        try {
            if (StringUtils.isEmpty(param.getOrderNo())) {
                return paramError("商户号orderNo不能为空!");
            }
            if (StringUtils.isEmpty(param.getNonceStr())) {
                return paramError("nonceStr不能为空!");
            }
            if (StringUtils.isEmpty(param.getTimeStamp())) {
                return paramError("timeStamp不能为空!");
            }
            if (StringUtils.isEmpty(param.getSign())) {
                return paramError("sign不能为空!");
            }
            return paymentService.payCheckApplet(param);
        } catch (Exception e) {
            log.info("====payCheckApplet==error:{}", JSON.toJSONString(e.getStackTrace()));
            log.error("小商城支付验证异常" + e.getMessage(), e.getMessage());
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName cardUse
     * @description TODO
     * <AUTHOR>
     * @date 2020/12/25 14:27
     */
    @PostMapping("/cardUse")
    public ResultEntity cardUse(@RequestBody PaymentMallParam param) {
        if (StringUtils.isBlank(param.getOpenId())) {
            return paramError("二维码不能为空");
        }
        if (null == param.getPayAmount() || param.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
            return paramError("请输入消费金额");
        }
        String msg = paymentService.cardUse(param);
        if (StringUtils.isNotBlank(msg)) {
            return paramError(msg);
        }
        return success();
    }
    
    @PostMapping("/pushOrder")
    public ResultEntity pushOrder(@RequestBody Map<String, List<String>> param) {
//    	if(MapUtil.isNotEmpty(param)) {
    		this.orderService.pushOrder(param);
//    	} 
    	return success();
    }


    @PostMapping("/queryDmallPayStatus")
    public ResultEntity queryDmallPayStatus(@RequestBody PayOrderParam param) {
        if(StringUtils.isBlank(param.getPayNumber())){
            return error(ResultEnum.ERP_MARK_PARAM_ERROR, "支付流水不能为空 ！");
        }
        PaymentResult queryPay = this.paymentService.queryDmallPayStatus(param.getPayNumber());
        return success(queryPay.getRecord());
    }


    @PostMapping("/queryDmallPayResult")
    public ResultEntity queryDmallPayResult(@RequestBody PayOrderParam param) {
        if(StringUtils.isBlank(param.getPayNumber())){
            return error(ResultEnum.ERP_MARK_PARAM_ERROR, "支付流水不能为空 ！");
        }
        PaymentResult queryPay = this.paymentService.queryDmallPayResult(param.getPayNumber());
        return success(queryPay.getRecord());
    }
}

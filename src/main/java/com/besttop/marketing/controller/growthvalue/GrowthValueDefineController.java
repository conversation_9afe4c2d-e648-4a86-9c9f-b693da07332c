package com.besttop.marketing.controller.growthvalue;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.growthvalue.param.GrowthValueDefineParam;
import com.besttop.marketing.service.growthvalue.GrowthValueDefineService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成长值生成规则定义表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
@RestController
@RequestMapping("/growthValueDefine")
public class GrowthValueDefineController extends BaseController {

    @Autowired
    private GrowthValueDefineService growthValueDefineService;

    @RequestMapping(value = "/add")
    @OperationLog(opName = "添加成长值生成规则")
    public ResultEntity<?> add(@RequestBody @Validated(GroupAdd.class) GrowthValueDefineParam param) {
        Boolean success = growthValueDefineService.add(param);
        if (!success) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }

        return success("添加成功");
    }

    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑成长值生成规则")
    public ResultEntity<?> update(@RequestBody @Validated(GroupUpdate.class) GrowthValueDefineParam param) {
        Boolean success = growthValueDefineService.update(param);
        if (!success) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }

        return success("修改成功");
    }

    @RequestMapping(value = "/delete")
    @OperationLog(opName = "删除成长值生成规则")
    public ResultEntity<?> delete(@RequestBody GrowthValueDefineParam param) {
        Boolean success = growthValueDefineService.delete(param);
        if (!success) {
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }

        return success("删除成功");
    }

    @RequestMapping(value = "/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity<?> findDetail(@RequestBody GrowthValueDefineParam param) {
        if (StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())) {
            return paramError("请传入编码和id");
        }
        return success(growthValueDefineService.findById(param));
    }

    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity<?> findBySelected(@RequestBody GrowthValueDefineParam param) {
        return success(new PageInfo<>(growthValueDefineService.findBySelected(param)));
    }
}

package com.besttop.marketing.controller.growthvalue;


import com.besttop.common.controller.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成长值生成规则定义明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
@RestController
@RequestMapping("/growthValueDefineDetail")
public class GrowthValueDefineDetailController extends BaseController {

}

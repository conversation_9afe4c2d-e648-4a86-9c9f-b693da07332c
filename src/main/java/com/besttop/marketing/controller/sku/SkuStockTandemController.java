package com.besttop.marketing.controller.sku;

import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.sku.SkuStockTandem;
import com.besttop.marketing.model.sku.SkuTandemRecord;
import com.besttop.marketing.model.sku.param.TendemParam;
import com.besttop.marketing.service.sku.SkuTandemRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/skuStockTandem")
public class SkuStockTandemController extends BaseController {

    @Autowired
    private SkuTandemRecordService tandemRecordService;



    /***
     *
     * @methodName andOrUpdate
     * @description 换货单新增串号
     * @params [stockTandem]
     * @return ResultEntity
     * <AUTHOR>
     * @date 2021/2/04 10:15
     */
    @OperationLog(opName = "换货单新增串号")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody TendemParam param) {
        try {
            String str = tandemRecordService.add(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /***
     *
     * @methodName andOrUpdate
     * @description 换货单新增串号
     * @params [stockTandem]
     * @return ResultEntity
     * <AUTHOR>
     * @date 2021/2/04 10:15
     */
    @OperationLog(opName = "查询串号")
    @PostMapping("/selectTandemRecord")
    @RedisCacheConvertEnable
    public ResultEntity selectTandemRecord(@RequestBody SkuTandemRecord param) {
        return success(tandemRecordService.selectTandemRecord(param));
    }

    /***
     *
     * @methodName del
     * @description 删除串号
     * @params [stockTandem]
     * @return ResultEntity
     * <AUTHOR>
     * @date 2021/2/04 10:15
     */
    @OperationLog(opName = "删除串号")
    @PostMapping("/del")
    public ResultEntity del(@RequestBody SkuTandemRecord param) {
        return success(tandemRecordService.del(param));
    }

    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }

    /***
     *
     * @methodName andOrUpdate
     * @description 查询在库串号
     * @params [stockTandem]
     * @return ResultEntity
     * <AUTHOR>
     * @date 2021/2/04 10:15
     */
    @OperationLog(opName = "查询在库串号")
    @PostMapping("/queryTandem")
    @RedisCacheConvertEnable
    public ResultEntity queryTandem(@RequestBody SkuStockTandem param) {
        return success(tandemRecordService.queryTandem(param));
    }
}

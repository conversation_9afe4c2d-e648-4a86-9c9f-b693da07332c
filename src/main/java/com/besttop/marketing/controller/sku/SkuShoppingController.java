package com.besttop.marketing.controller.sku;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.sku.SkuShopping;
import com.besttop.marketing.model.sku.param.SkuPriceParam;
import com.besttop.marketing.service.sku.SkuShoppingService;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: ShoppingSkuController</p >
 * <p>Description: ShoppingSkuController TODO</p >
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 * 2020-03-11 13:57:39  Create by striver
 *</pre>
*/
@RestController
@RequestMapping("/skuShopping")
public class SkuShoppingController extends BaseController {

    @Autowired
    private SkuShoppingService skuShoppingService;

    /**
     *查询热门商品
     * @methodName selectSku
     * @description 查询热门商品
     * @params [skuShopping]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/11 14:10
     */
    @PostMapping("/selectPopularSku")
    @RedisCacheConvertEnable
    public ResultEntity selectPopularSku(@RequestBody SkuShopping skuShopping){
       return success(skuShoppingService.selectPopularSku(skuShopping));
    }

    /**
     *查询热门商品
     * @methodName selectSku
     * @description 查询可销售商品(串号)
     * @params [skuShopping]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/11 14:10
     */
    @PostMapping("/selectSalableSkuBarcode")
    @RedisCacheConvertEnable
    public ResultEntity selectSalableSkuBarcode(@RequestBody SkuShopping skuShopping){
        return success(skuShoppingService.selectSalableSkuBarcode(skuShopping));
    }

    /**
     *查询可销售商品
     * @methodName selectSku
     * @description 查询可销售商品
     * @params [skuShopping]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/11 14:10
     */
    @PostMapping("/selectSalableSku")
    @RedisCacheConvertEnable
    public ResultEntity selectSalableSku(@RequestBody SkuShopping skuShopping){
        return success(skuShoppingService.selectSalableSku(skuShopping));
    }

    /**
     * @methodName findDetail
     * @description 查询sku的详情
     * @param skuShopping
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/21 14:41
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody SkuShopping skuShopping){
        if(StringUtils.isBlank(skuShopping.getId())){
            return paramError("请传入销售记录id");
        }
        return success(skuShoppingService.findDetail(skuShopping));
    }

    /**
     * 查询机构商品sku零售价格
     * @param skuPriceParam
     * @return
     */
    @PostMapping("/selectStoreSku")
    @RedisCacheConvertEnable
    public ResultEntity selectStoreSku(@RequestBody SkuPriceParam skuPriceParam){
        if (CollectionUtils.isEmpty(skuPriceParam.getStoreCodes())) {
            return new ResultEntity<>(ResultEnum.ERP_PURC_QUERY_ERROR, "请选择生效机构");
        }
        return success(skuShoppingService.selectStoreSku(skuPriceParam));
    }
}

package com.besttop.marketing.controller.sku;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.sku.SkuStockLedger;
import com.besttop.marketing.model.sku.result.ShoppingProviderResult;
import com.besttop.marketing.service.logistics.LogisticsRouteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 实物库存表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@RestController
@RequestMapping("/skuStockMatter")
public class SkuStockMatterController extends BaseController {

    @Autowired
    private LogisticsRouteService logisticsRouteService;

    /**
     * 查询线路是否匹配
     */
    @PostMapping("/select")
    public ResultEntity<SkuStockLedger> selectSaleableQuantity(@RequestBody ShoppingProviderResult skuStockLedger){
        return success(logisticsRouteService.matchingLine(skuStockLedger.getContractCode(),skuStockLedger.getRatio(),skuStockLedger.getQtty()));
    }

}

package com.besttop.marketing.controller.sku;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.marketing.model.sku.SkuStockLedger;
import com.besttop.marketing.service.sku.SkuStockLedgerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 财务库存表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@RestController
@RequestMapping("/skuStockLedger")
public class SkuStockLedgerController extends BaseController {
    @Autowired
    private SkuStockLedgerService skuStockLedgerService;

    /**
     * @methodName addCustomerAddress
     * @description  SKU可销售数量查询
     * @params [address]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/11 15:26
     */
    @PostMapping("/selectSaleableQuantity")
    public ResultEntity<SkuStockLedger> selectSaleableQuantity(@RequestBody @Validated(GroupAdd.class) SkuStockLedger skuStockLedger){
        return success(skuStockLedgerService.selectSaleableQuantity(skuStockLedger));
    }
}

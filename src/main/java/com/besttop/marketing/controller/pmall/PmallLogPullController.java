package com.besttop.marketing.controller.pmall;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.pmall.PmallLogPull;
import com.besttop.marketing.model.pmall.PmallLogPush;
import com.besttop.marketing.service.pmall.PmallLogPullService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * pmall拉去日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@RestController
@RequestMapping("/pmallLogPull")
public class PmallLogPullController extends BaseController {
    @Autowired
    private PmallLogPullService pmallLogPullService;


    @PostMapping("/findPmallLogPull")
    public ResultEntity findPmallLogPull(@RequestBody PmallLogPull pmallLogPull) {

        return success(pmallLogPullService.findPmallLogPull(pmallLogPull));
    }
}

package com.besttop.marketing.controller.pmall;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.edp.config.RedisLockConstant;
import com.besttop.marketing.model.pmall.PmallManual;
import com.besttop.marketing.model.pmall.PmallOrderMainQuery;
import com.besttop.marketing.model.pmall.query.PmallPullOrderRequest;
import com.besttop.marketing.service.pmall.PmallOrderMainService;
import com.besttop.marketing.service.pmall.PmallService;
import com.besttop.redis.utils.RedisLockUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * pmall&EDP订单关联关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@RestController
@RequestMapping("/pmallOrderMain")
public class PmallOrderMainController extends BaseController {

    @Autowired
    private PmallOrderMainService pmallOrderMainService;
    @Autowired
    private PmallService pmallService;
    @Autowired
    private RedisLockUtils redisLockUtils;

    // PMALL单据列表
    @PostMapping("/queryList")
    public ResultEntity queryList(@RequestBody PmallOrderMainQuery param) {
        try {
            PageHelper.startPage(param.getPage() == null ? 0 : param.getPage(), param.getRows() == null ? 20 : param.getRows());
            return success(new PageInfo<>(pmallOrderMainService.queryList(param)));
        } catch (Exception e) {
            return error(ResultEnum.ERROR, e.getMessage());
        }
    }

    // 手工处理PMALL订单
    @PostMapping("/manual")
    public ResultEntity manual(@RequestBody PmallOrderMainQuery param) {
        try {
            if(StringUtils.isEmpty(param.getMainId())){
                return paramError("mainId不能为空");
            }
            return success(pmallOrderMainService.manual(param));
        } catch (Exception e) {
            return error(ResultEnum.ERROR, e.getMessage());
        }
    }

    // 手工拉去PMALL订单
    @PostMapping("/pull")
    public ResultEntity pull(@RequestBody PmallManual param) {

        String key = "pull:pmall:0000001";

        try {

            if(redisLockUtils.getLock(key, (long) RedisLockConstant.LockNotWait, (long) RedisLockConstant.LockLeaseTime_180, TimeUnit.SECONDS)){
                PmallPullOrderRequest request = new PmallPullOrderRequest();
                request.setUpdate_time_start(param.getStartTime());
                request.setUpdate_time_end(param.getEndTime());
                return success(pmallService.pullOrderPmall(request));
            }else {
                return error(ResultEnum.ERROR, "获取锁失败");
            }

        } catch (Exception e) {
            return error(ResultEnum.ERROR, e.getMessage());
        }finally {
            redisLockUtils.unLock(key);
        }

    }

    @PostMapping("/stData")
    public ResultEntity stData(@RequestBody PmallOrderMainQuery param) {
        try {

            if (StringUtils.isEmpty(param.getOrderCode())) {
                return paramError("订单号不能为空");
            }
            String msg = pmallOrderMainService.doStData(param);

            if("ok".equals(msg)){
                return success();
            }
            return paramError(msg);
        } catch (Exception e) {
            return error(ResultEnum.ERROR, e.getMessage());
        }
    }

}

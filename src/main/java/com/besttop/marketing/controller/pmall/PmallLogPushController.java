package com.besttop.marketing.controller.pmall;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.channel.param.ChannelSaleParam;
import com.besttop.marketing.model.pmall.PmallLogPush;
import com.besttop.marketing.service.pmall.PmallLogPushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * pmall推送日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@RestController
@RequestMapping("/pmallLogPush")
public class PmallLogPushController extends BaseController {

    @Autowired
    private PmallLogPushService pmallLogPushService;


    @PostMapping("/findPmallLogPush")
    public ResultEntity find(@RequestBody PmallLogPush pmallLogPush) {
        return success(pmallLogPushService.findPmallLogPush(pmallLogPush));
    }
}

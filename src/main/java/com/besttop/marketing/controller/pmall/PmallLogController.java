package com.besttop.marketing.controller.pmall;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.pmall.PmallLog;
import com.besttop.marketing.service.pmall.PmallLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * pmall订单日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@RestController
@RequestMapping("/pmallLog")
public class PmallLogController extends BaseController {

    @Autowired
    private PmallLogService pmallLogService;


    @PostMapping("/findPmallLog")
    public ResultEntity findPmallLog(@RequestBody PmallLog pmallLog) {
        return success(pmallLogService.findPmallLog(pmallLog));
    }
}

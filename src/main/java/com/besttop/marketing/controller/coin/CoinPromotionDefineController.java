package com.besttop.marketing.controller.coin;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.coin.CoinPromotionDefine;
import com.besttop.marketing.model.coin.CoinPromotionDefineParam;
import com.besttop.marketing.service.coin.CoinPromotionDefineService;
import com.besttop.marketing.util.CollectionUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 电子币促销券名称定义表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@RestController
@RequestMapping("/coinPromotionDefine")
public class CoinPromotionDefineController extends BaseController {

    @Autowired
    private CoinPromotionDefineService defineService;

    /**
     * @methodName add
     * @description 新增电子币
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/12 9:26
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增电子币")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) CoinPromotionDefine define) {
        String message=defineService.add(define);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * @methodName update
     * @description 编辑电子货币
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/12 9:59
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑电子币")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) CoinPromotionDefine define) {
        if (StringUtils.isBlank(define.getId())) {
            return paramError("请选择需要修改的数据");
        }
        String message=defineService.update(define);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * @methodName del
     * @description 删除电子币
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/12 10:06
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除电子币")
    public ResultEntity del(@RequestBody CoinPromotionDefine define) {
        if(CollectionUtil.isEmpty(define.getIds())){
            return paramError("请选择需要删除的数据");
        }
        String message=defineService.del(define);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/12 10:17
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody CoinPromotionDefineParam define) {
        return success(new PageInfo<>(defineService.findBySelected(define)));

    }

    /**
     * @methodName findOption
     * @description 查询下拉框接口
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/27 15:29
     */
    @RequestMapping(value = "/findOption")
    public ResultEntity findOption(@RequestBody CoinPromotionDefine define) {
        return success(new PageInfo<>(defineService.findOption(define)));
    }

}

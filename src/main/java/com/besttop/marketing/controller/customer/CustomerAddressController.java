package com.besttop.marketing.controller.customer;


import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.customer.CustomerAddress;
import com.besttop.marketing.model.customer.param.CustomerAddressParam;
import com.besttop.marketing.model.logistics.LogisticsRoute;
import com.besttop.marketing.service.customer.CustomerAddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

import java.util.List;

/**
 * <p>
 * 顾客收货地址表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-04
 */
@Slf4j
@RestController
@RequestMapping("/customerAddress")
public class CustomerAddressController extends BaseController {

    @Autowired
    private CustomerAddressService addressService;

    /**
     * @methodName addCustomerAddress
     * @description 新增顾客收地址
     * @param address
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/4 16:43
     */
    @PostMapping("/addCustomerAddress")
    @OperationLog(opName = "新增顾客收地址")
    public ResultEntity addCustomerAddress(@RequestBody @Validated(GroupAdd.class) CustomerAddress address){
        try{
            addressService.addCustomerAddress(address);
        }catch (Exception e){
            log.error("新增顾客收地址失败");
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"新增顾客收地址失败");
        }
        return success();
    }

    /**
     * @methodName updateCustomerAddress
     * @description 编辑顾客收货地址
     * @param address
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/5 15:17
     */
    @PostMapping("/updateCustomerAddress")
    @OperationLog(opName = "编辑顾客收货地址")
    public ResultEntity updateCustomerAddress(@RequestBody @Validated(GroupUpdate.class) CustomerAddress address){
        try{
            addressService.updateCustomerAddress(address);
        }catch (Exception e){
            log.error("编辑顾客收地址失败");
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"编辑顾客收地址失败");
        }
        return success();
    }

    /**
     * @methodName deleteCustomerAddress
     * @description 删除顾客收货地址
     * @param address
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/5 15:26
     */
    @PostMapping("/deleteCustomerAddress")
    @OperationLog(opName = "删除顾客收货地址")
    public ResultEntity deleteCustomerAddress(@RequestBody CustomerAddress address){
        if (CollectionUtils.isEmpty(address.getIds())){
            return paramError("请选择要删除的收货地址");
        }
        try {
            addressService.deleteCustomerAddress(address);
        }catch (Exception e){
            log.error("删除顾客收货地址失败");
            return error(ResultEnum.ERP_MARK_DEL_ERROR,"删除顾客收货地址失败");
        }
        return success();
    }

    /**
     * @methodName queryCustomerAddress
     * @description 查询顾客收货地址
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/5 15:46
     */
    @PostMapping("/queryCustomerAddress")
    public ResultEntity queryCustomerAddress(@RequestBody CustomerAddressParam param){
        return success(addressService.queryCustomerAddress(param));
    }

    /**
     * @methodName findCustomerAddress
     * @description 多条件查询顾客收货地址
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/5 15:46
     */
    @PostMapping("/findCustomerAddress")
    public ResultEntity findCustomerAddress(@RequestBody CustomerAddressParam param){
        String str = addressService.findCustomerAddress(param);
        if(str.equals("ok")){
            return success();
        }else {
            return paramError("没有此地址");
        }
    }

    /**
     * 顾客收获地址匹配线路
     *
     * @param param 顾客收货地址
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @since 2021/09/06 17:46
     */
    @PostMapping("/findAddressMatchingLine")
    public ResultEntity findAddressMatchingLine(@RequestBody CustomerAddressParam param){
        List<LogisticsRoute> logisticsRoute = addressService.findAddressMatchingLine(param);
        return success(logisticsRoute);
    }

}

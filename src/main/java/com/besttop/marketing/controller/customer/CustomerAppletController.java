package com.besttop.marketing.controller.customer;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.customer.param.CustomerOrderAppletParam;
import com.besttop.marketing.model.logistics.LogisticsDeliver;
import com.besttop.marketing.service.logistics.LogisticsDeliverService;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 顾客小程序我的订单
 * </p>
 */
@RestController
@RequestMapping("/customerApplet")
public class CustomerAppletController extends BaseController {

    @Autowired
    private ShoppingOrderService shoppingOrderService;
    @Autowired
    private LogisticsDeliverService logisticsDeliverService;

    /**
     * 顾客小程序查询我的订单接口
     *
     * @param customerOrderAppletParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOrderApplet
     * @description 顾客小程序查询我的订单接口
     * <AUTHOR>
     * @date 2020/5/27 11:33
     */
    @PostMapping("/findOrderApplet")
    @RedisCacheConvertEnable
    public ResultEntity findOrderApplet(@RequestBody CustomerOrderAppletParam customerOrderAppletParam) {
        if (StringUtils.isEmpty(customerOrderAppletParam.getCustomerCode())) {
            return paramError("请传入顾客编码查询");
        }
        return success(shoppingOrderService.findOrderApplet(customerOrderAppletParam));
    }

    /**
     * 顾客小程序查询我的订单明细及物流信息接口
     *
     * @param customerOrderAppletParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOrderDetailApplet
     * @description 顾客小程序查询我的订单明细及物流信息接口
     * <AUTHOR>
     * @date 2020/5/27 14:19
     */
    @PostMapping("/findOrderDetailApplet")
    @RedisCacheConvertEnable
    public ResultEntity findOrderDetailApplet(@RequestBody CustomerOrderAppletParam customerOrderAppletParam) {
        if (StringUtils.isEmpty(customerOrderAppletParam.getCode())) {
            return paramError("请选择单据");
        }
        return success(shoppingOrderService.findOrderDetailApplet(customerOrderAppletParam));
    }

    /**
     * 商城小程序查询顾客我的订单接口
     *
     * @param customerOrderAppletParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCustomerOrderApplet
     * @description 商城小程序查询顾客我的订单接口
     * <AUTHOR>
     * @date 2020/7/24 10:06
     */
    @PostMapping("/findCustomerOrderApplet")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerOrderApplet(@RequestBody CustomerOrderAppletParam customerOrderAppletParam) {
        if (StringUtils.isEmpty(customerOrderAppletParam.getCustomerCode())) {
            return paramError("请传入顾客编码查询");
        }
        return success(shoppingOrderService.findCustomerOrderApplet(customerOrderAppletParam));
    }

    /**
     * Description: 未评价数量
     *
     * <AUTHOR>
     * @date: 2020-11-25 16:42
     * @param:[customerOrderAppletParam]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/findCustomerAppraiseCount")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerAppraiseCount(@RequestBody CustomerOrderAppletParam customerOrderAppletParam) {
        if (StringUtils.isEmpty(customerOrderAppletParam.getCustomerCode())) {
            return paramError("请传入顾客编码查询");
        }
        return success(shoppingOrderService.findCustomerAppraiseCount(customerOrderAppletParam));
    }

    /**
     * 商城小程序查询顾客我的订单详情
     *
     * @param customerOrderAppletParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCustomerOrderDetailApplet
     * @description TODO  商城小程序查询顾客我的订单详情
     * <AUTHOR>
     * @date 2020/7/27 9:43
     */
    @PostMapping("/findCustomerOrderDetailApplet")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerOrderDetailApplet(@RequestBody CustomerOrderAppletParam customerOrderAppletParam) {
        if (StringUtils.isEmpty(customerOrderAppletParam.getCode())) {
            return paramError("请选择单据");
        }
        return success(shoppingOrderService.findCustomerOrderDetailApplet(customerOrderAppletParam));
    }

    /**
     * 商城小程序确认签收
     *
     * @param logisticsDeliver
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateSignNoVerification
     * @description TODO 商城小程序确认签收
     * <AUTHOR>
     * @date 2020/7/29 14:37
     */
    @OperationLog(opName = "商城小程序确认签收")
    @PostMapping("/updateSignNoVerification")
    public ResultEntity updateSignNoVerification(@RequestBody LogisticsDeliver logisticsDeliver) {
        if (StringUtils.isEmpty(logisticsDeliver.getSourceBillCode())) {
            return paramError("未传入销售单号");
        }
        try {
            logisticsDeliverService.updateSignNoVerification(logisticsDeliver);
        } catch (Exception e) {
            return error(ResultEnum.ERP_LOGIS_AUDIT_ERROR, e.getMessage());
        }
        return success();
    }

}

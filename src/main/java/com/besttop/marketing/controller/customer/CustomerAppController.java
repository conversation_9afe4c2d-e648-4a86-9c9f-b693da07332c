package com.besttop.marketing.controller.customer;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.customer.param.CustomerAccountParam;
import com.besttop.marketing.model.scores.ScoresConvert;
import com.besttop.marketing.model.shopping.ShoppingCustomerRecord;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.service.customer.CustomerAccountService;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 顾客小程序 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/customerApp")
public class CustomerAppController extends BaseController {

    @Autowired
    private CustomerAccountService customerAccountService;
    @Autowired
    private ShoppingOrderService shoppingOrderService;

    /**
     * 统计各种账户的总计
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName countByType
     * @description 统计各种账户的总计
     * <AUTHOR>
     * @date 2020/4/20 18:58
     */
    @PostMapping("/countByType")
    public ResultEntity countByType(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(customerAccountService.countByType(param));
    }

    /**
     * 查询电子币
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDzb
     * @description 查询电子币
     * <AUTHOR>
     * @date 2020/4/21 15:53
     */
    @PostMapping("/findDzb")
    @RedisCacheConvertEnable
    public ResultEntity findDzb(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return customerAccountService.findDzb(param);
    }

    /**
     * 查询储值卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCzk
     * @description 查询储值卡
     * <AUTHOR>
     * @date 2020/4/21 16:16
     */
    @PostMapping("/findCzk")
    @RedisCacheConvertEnable
    public ResultEntity findCzk(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return customerAccountService.findCzk(param);
    }

    /**
     * 查询订金
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDj
     * @description 查询订金
     * <AUTHOR>
     * @date 2020/4/21 17:02
     */
    @PostMapping("/findDj")
    @RedisCacheConvertEnable
    public ResultEntity findDj(@RequestBody ShoppingOrder param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return shoppingOrderService.findOrderByCustomerCode(param);
    }

    /**
     * 查询优惠券
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findYhq
     * @description 查询优惠券
     * <AUTHOR>
     * @date 2020/4/21 17:58
     */
    @PostMapping("/findYhq")
    @RedisCacheConvertEnable
    public ResultEntity findYhq(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(customerAccountService.findYhq(param));
    }

    /**
     * 查询导购促销券
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDgcxq
     * @description 查询导购促销券
     * <AUTHOR>
     * @date 2020/4/21 17:58
     */
    @PostMapping("/findDgcxq")
    @RedisCacheConvertEnable
    public ResultEntity findDgcxq(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(customerAccountService.findDgcxq(param));
    }

    /**
     * 查询服务券
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findFwq
     * @description 查询服务券
     * <AUTHOR>
     * @date 2020/4/21 18:54
     */
    @PostMapping("/findFwq")
    @RedisCacheConvertEnable
    public ResultEntity findFwq(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(customerAccountService.findFwq(param));
    }

    /**
     * 查询延保服务
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findYbfw
     * @description 查询延保服务
     * <AUTHOR>
     * @date 2020/4/21 18:58
     */
    @PostMapping("/findYbfw")
    @RedisCacheConvertEnable
    public ResultEntity findYbfw(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(customerAccountService.findYbfw(param));
    }

    /**
     * 查询延保或者服务劵
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findYbfw
     * @description 查询延保或者服务劵
     * <AUTHOR>
     * @date 2020/8/7 18:58
     */
    @PostMapping("/findYbfwOrFwq")
    @RedisCacheConvertEnable
    public ResultEntity findYbfwOrFwq(@RequestBody CustomerAccountParam param) {
        try{
            return customerAccountService.findYbfwOrFwq(param);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 核销延保或者服务劵
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findYbfw
     * @description 查询延保或者服务劵
     * <AUTHOR>
     * @date 2020/8/7 18:58
     */
    @PostMapping("/aduitYbfwOrFwq")
    @RedisCacheConvertEnable
    public ResultEntity aduitYbfwOrFwq(@RequestBody CustomerAccountParam param) {

        try {
            if (StringUtils.isBlank(param.getCode())) {
                return paramError("劵编码无效");
            }
            String str = customerAccountService.aduitYbfwOrFwq(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success("验收成功");
        } else {
            return paramError(str);
        }
    }

    /**
     * 查询顾客购物记录
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCustomerShopping
     * @description 查询顾客购物记录
     * <AUTHOR>
     * @date 2020/4/21 18:58
     */
    @PostMapping("/findCustomerShopping")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerShopping(@RequestBody ShoppingCustomerRecord param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(customerAccountService.findCustomerShopping(param));
    }


    /**
     * 查询积分明细
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findJfmx
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/27 14:06
     */
    @PostMapping("/findJfmx")
    @RedisCacheConvertEnable
    public ResultEntity findJfmx(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return customerAccountService.findJfmx(param);
    }

    /**
     * 查询积分超值兑
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findJfczd
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/27 14:06
     */
    @PostMapping("/findJfczd")
    @RedisCacheConvertEnable
    public ResultEntity findJfczd(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return customerAccountService.findJfczd(param);
    }


    /**
     * 积分兑换
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName redeemPoints
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/27 14:06
     */
    @PostMapping("/redeemPoints")
    @RedisCacheConvertEnable
    public ResultEntity redeemPoints(@RequestBody ScoresConvert param) {

        try {
            if (StringUtils.isBlank(param.getCustomerCode())) {
                return paramError("请传入顾客编码");
            }
            if (customerAccountService.redeemPoints(param)) {
                return success();
            } else {
                return paramError("积分兑换失败");
            }

        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }

    }

    /**
     * 领取优惠券
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName receiveCoupon
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/27 14:06
     */
    @PostMapping("/receiveCoupon")
    @RedisCacheConvertEnable
    public ResultEntity receiveCoupon(@RequestBody ScoresConvert param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        if (customerAccountService.receiveCoupon(param)) {
            return success();
        } else {
            return paramError("领取优惠券失败");
        }
    }


    /**
     * 导购领取优惠券
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName receiveCoupon
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/27 14:06
     */
    @PostMapping("/dgReceiveCoupon")
    @RedisCacheConvertEnable
    public ResultEntity dgReceiveCoupon(@RequestBody ScoresConvert param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        if (customerAccountService.dgReceiveCoupon(param)) {
            return success();
        } else {
            return paramError("领取优惠券失败");
        }
    }

    /**
     * 查询可领取优惠券
     *
     * @param [param]
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCoupon
     * @description TODO
     * <AUTHOR>
     * @date 2020/6/23 16:49
     */
    @PostMapping("/findCoupon")
    @RedisCacheConvertEnable
    public ResultEntity findCoupon(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        if (null == param.getCustomerType()) {
            return paramError("参数异常");
        }
        return customerAccountService.findCoupon(param);

    }
}

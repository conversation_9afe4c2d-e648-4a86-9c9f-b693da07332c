package com.besttop.marketing.controller.customer;


import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.customer.CustomerInvoice;
import com.besttop.marketing.service.customer.CustomerInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 顾客税票信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Slf4j
@RestController
@RequestMapping("/customerInvoice")
public class CustomerInvoiceController extends BaseController {

    @Autowired
    private CustomerInvoiceService invoiceService;

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName addInvoice
     * @description 新增税票信息
     * <AUTHOR>
     * @date 2020/4/9 15:15
     */
    @PostMapping("/addInvoice")
    @OperationLog(opName = "新增税票信息")
    public ResultEntity addInvoice(@RequestBody @Validated(GroupAdd.class) CustomerInvoice invoice) {
        try {
            if (invoiceService.addInvoice(invoice)) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增税票信息失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增税票信息失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增税票信息失败");
    }

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateInvoice
     * @description 更新税票信息
     * <AUTHOR>
     * @date 2020/4/9 15:15
     */
    @PostMapping("/updateInvoice")
    @OperationLog(opName = "更新税票信息")
    public ResultEntity updateInvoice(@RequestBody @Validated(GroupUpdate.class) CustomerInvoice invoice) {
        try {
            if (StringUtils.isBlank(invoice.getId())) {
                return paramError("请选择修改的数据");
            }
            if (invoiceService.updateInvoice(invoice)) {
                return success();
            }
        } catch (Exception e) {
            log.error("更新税票信息失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "更新税票信息失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "更新税票信息失败");
    }

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteInvoice
     * @description 删除税票信息
     * <AUTHOR>
     * @date 2020/4/9 15:15
     */
    @PostMapping("/deleteInvoice")
    @OperationLog(opName = "删除税票信息")
    public ResultEntity deleteInvoice(@RequestBody CustomerInvoice invoice) {
        try {
            if (StringUtils.isEmpty(invoice.getId())) {
                return paramError("请选择删除的数据");
            }
            if (invoiceService.deleteInvoice(invoice)) {
                return success();
            }
        } catch (Exception e) {
            log.error("删除税票信息失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "删除税票信息失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "删除税票信息失败");
    }

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryInvoice
     * @description 查询税票信息
     * <AUTHOR>
     * @date 2020/4/9 15:15
     */
    @PostMapping("/queryInvoice")
    public ResultEntity queryInvoice(@RequestBody CustomerInvoice invoice) {
        return success(invoiceService.queryInvoice(invoice));
    }

}

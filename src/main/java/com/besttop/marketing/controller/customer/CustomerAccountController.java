package com.besttop.marketing.controller.customer;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.CustomerCouponLock;
import com.besttop.marketing.model.customer.param.CustomerAccountParam;
import com.besttop.marketing.model.customer.param.GiveAccountParam;
import com.besttop.marketing.model.customer.result.CustomerNews;
import com.besttop.marketing.model.customer.result.CustomerNums;
import com.besttop.marketing.model.scores.ScoresConvert;
import com.besttop.marketing.service.customer.CustomerAccountService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <p>
 * 顾客账户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/customerAccount")
public class CustomerAccountController extends BaseController {

    @Autowired
    private CustomerAccountService customerAccountService;


    /**
     * @description: 更新打印次数
     * @param: [param] --> code
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/8/17 11:12
     */
    @PostMapping("/updatePrint")
    public ResultEntity updatePrint(@RequestBody CustomerAccountParam param) {
        String msg = customerAccountService.updatePrint(param);
        if (StringUtils.isBlank(msg)) {
            return success();
        }
        return paramError(msg);
    }


    /**
     * 查询顾客地址  税票信息  购买金额接口
     *
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectCustomerNews
     * @description 查询顾客地址  税票信息  购买金额接口
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/4/9 17:42
     */
    @PostMapping("/selectCustomerNews")
    public ResultEntity<CustomerNews> selectCustomerNews(@RequestBody CustomerAccount customerAccount) {
        if (StringUtils.isEmpty(customerAccount.getCustomerCode())) {
            return paramError("客户编码为空");
        }
        return success(customerAccountService.selectCustomerNews(customerAccount.getCustomerCode()));
    }

    /**
     * @return com.besttop.common.model.ResultEntity<com.besttop.marketing.model.customer.result.CustomerNews>
     * @methodName selectCustomer
     * @description 查询当前登录人顾客统计 及顾客开单统计
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/4/10 16:41
     */
    @PostMapping("/selectCustomer")
    public ResultEntity<CustomerNums> selectCustomer() {
        return success(customerAccountService.selectCustomer());
    }

    /**
     * @param customerAccount
     * @return com.besttop.common.model.ResultEntity
     * @methodName findByType
     * @description 根据类型查询客户账户
     * <AUTHOR>
     * @date 2020/4/11 14:58
     */
    @PostMapping("/findByType")
    @RedisCacheConvertEnable
    public ResultEntity findByType(@RequestBody CustomerAccountParam customerAccount) {
        return customerAccountService.findByType(customerAccount);
    }

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findByType
     * @description 查询客户购买记录
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/5/15 10:03
     */
    @PostMapping("/findCustomerTransactions")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerTransactions(@RequestBody CustomerAccountParam customerAccount) {
        if (StringUtils.isEmpty(customerAccount.getCustomerCode())) {
            return paramError("顾客编码不能为空");
        }
        return success(new PageInfo<>(customerAccountService.findCustomerTransactions(customerAccount)));
    }

    /**
     * 内部小程序查询顾客是否可以领券
     *
     * @param customerAccount
     * @return com.besttop.common.model.ResultEntity
     * @methodName findIsReceiveCoupons
     * @description 内部小程序查询顾客是否可以领券
     * <AUTHOR>
     * @date 2020/6/5 16:15
     */
    @PostMapping("/findIsReceiveCoupons")
    @RedisCacheConvertEnable
    public ResultEntity findIsReceiveCoupons(@RequestBody CustomerAccountParam customerAccount) {
        if (StringUtils.isEmpty(customerAccount.getCustomerCode())) {
            return paramError("顾客编码不能为空");
        }
        if (StringUtils.isEmpty(customerAccount.getPromotionCode())) {
            return paramError("券编码不能为空");
        }
        return success(customerAccountService.findIsReceiveCoupons(customerAccount));
    }

    /**
     * 内部小程序顾客领券
     *
     * @param customerAccount
     * @return com.besttop.common.model.ResultEntity
     * @methodName findIsReceiveCoupons
     * @description 内部小程序顾客领券
     * <AUTHOR>
     * @date 2020/6/8 11:26
     */
    @PostMapping("/addReceiveCustomerCoupon")
    @OperationLog(opName = "内部小程序顾客领券")
    public ResultEntity addReceiveCustomerCoupon(@RequestBody CustomerAccountParam customerAccount) {
        if (StringUtils.isEmpty(customerAccount.getCustomerCode())) {
            return paramError("顾客编码不能为空");
        }
        if (StringUtils.isEmpty(customerAccount.getGiveUserCode())) {
            customerAccount.setGiveUserCode("0000");
        }
        if (customerAccount.getGiveUserCode().equals(customerAccount.getCustomerCode())) {
            return paramError("赠送和领用人相同，不能领取");
        }
        if (StringUtils.isEmpty(customerAccount.getPromotionCode())) {
            return paramError("券编码不能为空");
        }
        try {
            customerAccountService.addReceiveCustomerCoupon(customerAccount);
        } catch (Exception e) {
            return paramError(e.getMessage());
        }
        return success();
    }

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateCustomerCouponStatus
     * @description 修改促销券状态（status 只接受 转增中，未使用 两种类型的入参）
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/6/8 14:21
     */
    @PostMapping("/updateCustomerCouponStatus")
    public ResultEntity updateCustomerCouponStatus(@RequestBody CustomerAccountParam customerAccount) {
        if (StringUtils.isEmpty(customerAccount.getCode())) {
            return paramError("促销券编码不能为空");
        }
        if (StringUtils.isEmpty(customerAccount.getStatus())) {
            return paramError("修改状态不能为空");
        }
        String str = customerAccountService.updateCustomerCouponStatus(customerAccount);
        if (str.equals("ok")) {
            return success();
        } else {
            return paramError(str);
        }
    }

    /**
     * Description:
     *
     * <AUTHOR>
     * @date: 2020-11-30 11:21
     * @param:[customerAccount]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/getByCode")
    public ResultEntity getByCode(@RequestBody CustomerAccountParam customerAccount) {
        if (StringUtils.isEmpty(customerAccount.getCode())) {
            return paramError("促销券编码不能为空");
        }
        return success(customerAccountService.getByCode(customerAccount));

    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryCustomerCoins
     * @description 根据顾客编码和电子币类型查询电子币
     * <AUTHOR>
     * @date 2020/6/8 15:21
     */
    @PostMapping("/queryCustomerCoins")
    public ResultEntity queryCustomerCoins(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("顾客编码不能为空");
        }
        if (CollectionUtils.isEmpty(param.getCoinTypeCodes())) {
            return paramError("电子币类型不能为空");
        }
        return success(customerAccountService.queryCustomerCoins(param));
    }

    /**
     * 根据客户编码查询客户剩余积分（pc）
     *
     * @param
     * @return
     * @methodName
     * @description TODO
     * <AUTHOR>
     * @date 2020/7/2 18:27
     */
    @PostMapping("/queryCuntomerJf")
    public ResultEntity queryCuntomerJf(@RequestBody CustomerAccount param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("入参不能为空");
        }
        return success(customerAccountService.queryCustomerJf(param));
    }

    /**
     * 查询客户可兑换的电子币列表（pc）
     *
     * @param
     * @return
     * @methodName
     * @description TODO
     * <AUTHOR>
     * @date 2020/7/2 18:27
     */
    @PostMapping("/queryCuntomerDzb")
    public ResultEntity queryCuntomerDzb(@RequestBody ScoresConvert param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("入参不能为空");
        }
        return customerAccountService.queryCuntomerDzb(param);
    }

    /**
     * 查询客户兑换的电子币历史记录（pc）
     *
     * @param
     * @return
     * @methodName
     * @description TODO
     * <AUTHOR>
     * @date 2020/7/2 18:27
     */
    @PostMapping("/findCustomerDzbHistory")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerDzbHistory(@RequestBody CustomerAccountParam customerAccount) {
        return customerAccountService.findCustomerDzbHistory(customerAccount);
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryCustomerScores
     * @description 查询顾客积分
     * <AUTHOR>
     * @date 2020/11/12 10:55
     */
    @PostMapping("/queryCustomerScores")
    @RedisCacheConvertEnable
    public ResultEntity queryCustomerScores(@RequestBody CustomerAccountParam param) {
        return success(customerAccountService.queryCustomerScores(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addOrReduceScores
     * @description 手动增加或减少积分
     * <AUTHOR>
     * @date 2020/11/12 14:52
     */
    @PostMapping("/addOrReduceScores")
    @RedisCacheConvertEnable
    public ResultEntity addOrReduceScores(@RequestBody CustomerAccountParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("顾客编码不能为空");
        }
        if (param.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            return paramError("积分不能为0");
        }
        return success(customerAccountService.addOrReduceScores(param));
    }

    /**
     * @param account
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateExpireDate
     * @description 顾客卡包修改有效期
     * <AUTHOR>
     * @date 2020/12/8 10:15
     */
    @PostMapping("/updateExpireDate")
    @OperationLog(opName = "顾客卡包修改有效期")
    public ResultEntity updateExpireDate(@RequestBody CustomerAccount account) {
        if (StringUtils.isBlank(account.getCode())) {
            return paramError("账号编码不能为空");
        }
        String msg = customerAccountService.updateExpireDate(account);
        if (StringUtils.isNotBlank(msg)) {
            return paramError(msg);
        }
        return success();
    }

    /**
     * @param account
     * @return com.besttop.common.model.ResultEntity
     * @methodName verification
     * @description 服务券，延保券核销
     * <AUTHOR>
     * @date 2021/4/1 10:15
     */
    @PostMapping("/verification")
    @OperationLog(opName = "核销顾客服务券或延保券")
    public ResultEntity verification(@RequestBody CustomerAccount account) {
        if (StringUtils.isBlank(account.getCode())) {
            return paramError("账号编码不能为空");
        }
        //根据code获取顾客账户类型
        String msg = customerAccountService.updateVerification(account);
        if (StringUtils.isNotBlank(msg)) {
            return paramError(msg);
        }
        return success();
    }
    /**
     * 下拉框
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOption
     * @description 下拉框
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findOption")
    @RedisCacheConvertEnable
    public ResultEntity<CustomerAccount> findOption(@RequestBody CustomerAccountParam param) {
        return (customerAccountService.findOption(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName giveAccount
     * @description 赠送电子币/促销券/积分
     * <AUTHOR>
     * @date 2020/12/22 14:53
     */
    @PostMapping("/giveAccount")
    @OperationLog(opName = "赠送电子币/促销券/积分")
    public ResultEntity giveAccount(@RequestBody @Validated(GroupAdd.class) GiveAccountParam param) {
        String msg = customerAccountService.giveAccount(param);
        if (StringUtils.isNotBlank(msg)){
            return paramError(msg);
        }
        return success();
    }

    /**
     * 锁定、解锁促销券
     * @param param
     * @return
     */
    @PostMapping("/lock")
    @OperationLog(opName = "锁定、解锁促销券")
    public ResultEntity lock(@RequestBody CustomerCouponLock param) {
        String msg = customerAccountService.lock(param);
        if (StringUtils.isNotBlank(msg)){
            return paramError(msg);
        }
        return success();
    }
}

package com.besttop.marketing.controller.customer;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.customer.CustomerAccountLog;
import com.besttop.marketing.service.customer.CustomerAccountLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 顾客账户日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/customerAccountLog")
public class CustomerAccountLogController extends BaseController {

    @Autowired
    private CustomerAccountLogService customerAccountLogService;

    /**
     * 顾客日志查询
     *
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectCustomerNews
     * @description 查询顾客地址  税票信息  购买金额接口
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/5/13 17:42
     */
    @PostMapping("/select")
    @RedisCacheConvertEnable
    public ResultEntity<CustomerAccountLog> select(@RequestBody CustomerAccountLog customerAccountLog) {
        if (StringUtils.isEmpty(customerAccountLog.getCustomerCode())) {
            return paramError("客户编码为空");
        }
        return success(customerAccountLogService.selectCustomerAccountLog(customerAccountLog));
    }
}

package com.besttop.marketing.controller.customer;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.customer.CustomerAppraise;
import com.besttop.marketing.service.customer.CustomerAppraiseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 顾客评价记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@RestController
@RequestMapping("/customerAppraise")
public class CustomerAppraiseController extends BaseController {

    @Autowired
    private CustomerAppraiseService customerAppraiseService;

    /**
     * 销售单新增顾客评价
     *
     * @param customerAppraise
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 销售单新增顾客评价
     * <AUTHOR>
     * @date 2020/5/27 17:58
     */
    @PostMapping("/add")
    @OperationLog(opName = "销售单新增顾客评价")
    public ResultEntity add(@RequestBody CustomerAppraise customerAppraise) {
        if (StringUtils.isEmpty(customerAppraise.getSourceCode())) {
            return paramError("请传入销售单号");
        }
        if (StringUtils.isEmpty(customerAppraise.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        try {
            if (customerAppraiseService.add(customerAppraise)) {
                return success();
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return paramError("评价失败");
    }

    /**
     * 商城小程序销售单新增顾客评价
     *
     * @param customerAppraise
     * @return com.besttop.common.model.ResultEntity
     * @methodName addCustomerAppraise
     * @description TODO  商城小程序销售单新增顾客评价
     * <AUTHOR>
     * @date 2020/7/29 10:33
     */
    @PostMapping("/addCustomerAppraise")
    @OperationLog(opName = "商城小程序销售单新增顾客评价")
    public ResultEntity addCustomerAppraise(@RequestBody CustomerAppraise customerAppraise) {
        if (StringUtils.isEmpty(customerAppraise.getSourceCode())) {
            return paramError("请传入销售单号");
        }
        if (StringUtils.isEmpty(customerAppraise.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        try {
            if (customerAppraiseService.addCustomerAppraise(customerAppraise)) {
                return success();
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return paramError("评价失败");
    }

    /**
     * 商城小程序销售单新增顾客评价
     *
     * @param customerAppraise
     * @return com.besttop.common.model.ResultEntity
     * @methodName addCustomerAppraise
     * @description   商城小程序销售单新增顾客评价
     * <AUTHOR>
     * @date 2020/7/29 10:33
     */
    @PostMapping("/addCustomerAppraiseV2")
    @OperationLog(opName = "商城小程序销售单新增顾客评价V2")
    public ResultEntity addCustomerAppraiseV2(@RequestBody CustomerAppraise customerAppraise) {
        if (StringUtils.isEmpty(customerAppraise.getSourceCode())) {
            return paramError("请传入销售单号");
        }
        if (StringUtils.isEmpty(customerAppraise.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        try {
            if (customerAppraiseService.addCustomerAppraiseV2(customerAppraise)) {
                return success();
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return paramError("评价失败");
    }

    /**
     * 根据顾客编码和销售单号商城小程序销售单查询顾客评价
     *
     * @param customerAppraise
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCustomerAppraise
     * @description TODO 根据顾客编码和销售单号商城小程序销售单查询顾客评价
     * <AUTHOR>
     * @date 2020/8/5 14:34
     */
    @PostMapping("/findCustomerAppraise")
    @RedisCacheConvertEnable
    public ResultEntity findCustomerAppraise(@RequestBody CustomerAppraise customerAppraise) {
        if (StringUtils.isEmpty(customerAppraise.getSourceCode()) && StringUtils.isEmpty(customerAppraise.getCustomerCode())) {
            return paramError("请选择单据或输入顾客");
        }
        return success(customerAppraiseService.findCustomerAppraise(customerAppraise));
    }

    /**
     * Description: 是否有评价
     *
     * <AUTHOR>
     * @date: 2020-09-01 10:25
     * @param:[customerAppraise]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/isAppraise")
    @RedisCacheConvertEnable
    public ResultEntity isAppraise(@RequestBody CustomerAppraise customerAppraise) {
        if (StringUtils.isEmpty(customerAppraise.getSourceCode())) {
            return success();
        }
        return success(customerAppraiseService.isAppraise(customerAppraise));
    }

}

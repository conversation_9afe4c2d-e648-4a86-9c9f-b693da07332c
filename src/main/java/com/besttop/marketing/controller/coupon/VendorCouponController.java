package com.besttop.marketing.controller.coupon;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.feign.BigmemberFeignClient;
import com.besttop.marketing.model.bill.BillVonderCouponRule;
import com.besttop.marketing.model.bill.BillVonderCouponRuleDetail;
import com.besttop.marketing.model.coupon.param.DMallQueryCouponDefineParam;
import com.besttop.marketing.service.coupon.VendorCouponService;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/vendorCoupon")
public class VendorCouponController extends BaseController {
	
	@Autowired
	private VendorCouponService vendorCouponService;
//	@Autowired
//	private BigmemberFeignClient bigmemberFeign;
	
    @PostMapping("/queryCouponDefines")
    public ResultEntity<List<Map<String, Object>>> queryCustomerCoupons(@RequestBody DMallQueryCouponDefineParam param) {
    	log.info("====queryCustomerCoupons==");
    	
//    	ResultEntity<List<Map<String, Object>>> queryVonderCouponDefines = this.vendorCouponService.queryVonderCouponDefines(param);
//        return success(queryVonderCouponDefines);
    	return this.vendorCouponService.queryVonderCouponDefines(param);
    }
    
    @PostMapping("/schedule/addUpdateAudit")
    public ResultEntity addOrUpdateOrAuditSchedule(@RequestBody BillVonderCouponRule param) {
    	log.info("====addOrUpdateOrAuditSchedule==");
    	ResultEntity result = null; 	
    	String msg = this.vendorCouponService.addOrUpdateOrAuditSchedule(param);
    	if(StringUtils.isNotBlank(msg)) {
    		result = error(ResultEnum.ERROR, msg);
    	} else {
    		result = success();
    	}
    	return result;
    }
    
    @PostMapping("/schedule/delTerminal")
    public ResultEntity delOrTerminalVendorCouponSchedule(@RequestBody BillVonderCouponRule param) {
    	log.info("====delOrTerminalVendorCouponSchedule==");
    	
    	ResultEntity result = null; 	
    	if(StringUtils.isBlank(param.getId()) && StringUtils.isBlank(param.getCode())) {
    		return paramError("ID或CODE, 二选一必须传一个, 不能都为空");
	    }
    	
    	String msg = this.vendorCouponService.delOrTerminalVendorCouponSchedule(param);
    	if(StringUtils.isNotBlank(msg)) {
    		result = error(ResultEnum.ERROR, msg);
    	} else {
    		result = success();
    	}
    	return result;
    }
    
    
    @PostMapping("/querySchedule")
    @RedisCacheConvertEnable
    public ResultEntity querySchedule(@RequestBody BillVonderCouponRule param) {
    	log.info("====querySchedule==");
    	try {
			PageInfo<BillVonderCouponRule> page = this.vendorCouponService.querySchedule(param);
			return success(page);
		} catch (Exception e) {
			log.info("====querySchedule==error:{}", JSON.toJSONString(e.getStackTrace()));
			return error(ResultEnum.ERROR);
		}
    }
    
    @PostMapping("/queryScheduleDetail")
    @RedisCacheConvertEnable
    public ResultEntity queryScheduleDetail(@RequestBody BillVonderCouponRuleDetail param) {
    	log.info("====queryScheduleDetail==");
    	if(StringUtils.isBlank(param.getRuleCode())) {
    		return paramError("规则单号不能为空");
    	}
    	try {
			PageInfo<BillVonderCouponRuleDetail> page = this.vendorCouponService.queryScheduleDetail(param);
			return success(page);
		} catch (Exception e) {
			log.info("====queryScheduleDetail==error:{}", JSON.toJSONString(e.getStackTrace()));
			return error(ResultEnum.ERROR);
		}
    }
}

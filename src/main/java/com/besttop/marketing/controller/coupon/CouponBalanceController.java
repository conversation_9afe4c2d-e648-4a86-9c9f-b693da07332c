package com.besttop.marketing.controller.coupon;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupQuery;
import com.besttop.marketing.model.coupon.param.CouponBalanceParam;
import com.besttop.marketing.service.coupon.CouponBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 机构优惠券余额表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
@RestController
@RequestMapping("/coupon/couponBalance")
public class CouponBalanceController extends BaseController {

    @Autowired
    private CouponBalanceService couponBalanceService;

    /**
     * @methodName query
     * @description 查询A/B账户
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/14 16:54
     */
    @RequestMapping(value = "/query")
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class) CouponBalanceParam param) {
        return success(couponBalanceService.query(param));
    }
}

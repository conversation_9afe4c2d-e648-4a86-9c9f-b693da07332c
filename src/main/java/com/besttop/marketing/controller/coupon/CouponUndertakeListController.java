package com.besttop.marketing.controller.coupon;

import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.marketing.model.coupon.CouponUndertakeList;
import com.besttop.marketing.model.coupon.BatchCouponUndertakeRequest;
import com.besttop.marketing.service.coupon.ICouponUndertakeListService;
import com.besttop.redis.annotation.Idempotent;
import org.redisson.api.RLock;
import com.github.pagehelper.PageInfo;

import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequestMapping("/coupon/undertakeList")
public class CouponUndertakeListController extends BaseController {

    private final ICouponUndertakeListService couponUndertakeListService;

    @Autowired
    public CouponUndertakeListController(ICouponUndertakeListService couponUndertakeListService) {
        this.couponUndertakeListService = couponUndertakeListService;
    }

    // 批量订单挂券承担
    @SuppressWarnings("rawtypes")
    @PostMapping("/batchBind")
    @Idempotent(key = "#request.orderCode", expireTime = 60, manualRelease = true)
    public ResultEntity batchBind(@RequestBody BatchCouponUndertakeRequest request, RLock lock) {
        try {
            return success(couponUndertakeListService.batchAdd(request));
        } catch (Exception e) {
            log.error("批量新增挂券承担列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "挂券失败：" + e.getMessage());
        } finally {
            // 手动释放锁，延迟 5 秒
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("手动释放锁: {}", "CouponUndertakeListController:batchBind:" + request.getOrderCode());
            }
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/detail")
    @RedisCacheConvertEnable
    public ResultEntity detail(@RequestBody CouponUndertakeList param) {
        try {
            if (StringUtils.isEmpty(param.getOrderCode())) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "订单号不能为空");
            }
            return success(couponUndertakeListService.detail(param.getOrderCode()));
        } catch (Exception e) {
            log.error("查询挂券承担列表详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询详情失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/list")
    @RedisCacheConvertEnable
    public ResultEntity list(@RequestBody CouponUndertakeList query) {
        try {
            return success(new PageInfo<>(couponUndertakeListService.list(query)));
        } catch (Exception e) {
            log.error("查询挂券承担列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询列表失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/offset")
    @Idempotent(key = "'offset:'+#list?.orderCode", expireTime = 60, manualRelease = true)
    public ResultEntity offset(@RequestBody CouponUndertakeList list, RLock lock) {
        try {
            return success(couponUndertakeListService.offset(list,false));
        } catch (Exception e) {
            log.error("冲销挂券承担列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "冲销失败：" + e.getMessage());
        }finally {
            // 手动释放锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("手动释放锁: {}", "CouponUndertakeListController:offset:" + list.getOrderCode());
            }
        }
    }
} 
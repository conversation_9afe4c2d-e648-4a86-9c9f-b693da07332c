package com.besttop.marketing.controller.coupon;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.exception.CommonException;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.param.BigMemberVendorCouponParam;
import com.besttop.marketing.model.bill.param.CouponItem;
import com.besttop.marketing.model.bill.result.BigMemberVendorCouponResult;
import com.besttop.marketing.service.coupon.BigMemberCouponService;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/bigMemberCoupon")
public class BigMemberCouponController extends BaseController {
	
	@Autowired
	private BigMemberCouponService bigMemberCouponService;
	
    @PostMapping("/queryCustomerCoupons")
    @RedisCacheConvertEnable
    public ResultEntity<List<BigMemberVendorCouponResult>> queryCustomerCoupons(@RequestBody BigMemberVendorCouponParam param) {
    	List<BigMemberVendorCouponResult> coupons = this.bigMemberCouponService.queryCustomerCoupons(param);
        return success(coupons);
    }
    
    @PostMapping("/issueCoupon")
    @RedisCacheConvertEnable
    public ResultEntity< Map<String, List<CouponItem>>> issueCoupon(@RequestBody BigMemberVendorCouponParam param) {
    	if (StringUtils.isBlank(param.getCustomerCode())) {
            throw new CommonException("没有获取到顾客信息");
        }
    	
        
        if (CollectionUtils.isEmpty(param.getCoupons())) {
            throw new CommonException("请传入规则定义编码及服务编码集合");
        }
        
        Map<String, List<CouponItem>> issueCoupon = this.bigMemberCouponService.issueCoupon(param);
        log.info("====issueCoupon==issueCoupon:{}", JSON.toJSONString(issueCoupon));
        return success(issueCoupon);
    }
    
    @PostMapping("/queryVendorCoupons")
    @RedisCacheConvertEnable
    public ResultEntity<List<BigMemberVendorCouponResult>> queryAllVendorCoupon(@RequestBody BigMemberVendorCouponParam param) {
    	List<BigMemberVendorCouponResult> coupons = this.bigMemberCouponService.queryVendorCoupons(param);
        return success(coupons);
    }
}

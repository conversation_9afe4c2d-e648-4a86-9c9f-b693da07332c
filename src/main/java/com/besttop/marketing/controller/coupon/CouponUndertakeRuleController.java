package com.besttop.marketing.controller.coupon;

import com.besttop.common.model.ResultEntity;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.marketing.model.coupon.CouponUndertakeRule;
import com.besttop.marketing.model.coupon.dto.CouponUndertakeRuleMatchDTO;
import com.besttop.marketing.service.coupon.ICouponUndertakeRuleService;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;



@Slf4j
@RestController
@RequestMapping("/coupon/undertakeRule")
public class CouponUndertakeRuleController extends BaseController {

    private final ICouponUndertakeRuleService couponUndertakeRuleService;

    @Autowired
    public CouponUndertakeRuleController(ICouponUndertakeRuleService couponUndertakeRuleService) {
        this.couponUndertakeRuleService = couponUndertakeRuleService;
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody CouponUndertakeRule rule) {
        try {
            String result = couponUndertakeRuleService.add(rule);
            return success(result);
        } catch (Exception e) {
            log.error("新增挂券承担规则失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"新增失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/update")
    public ResultEntity update(@RequestBody CouponUndertakeRule rule) {
        try {
            String result = couponUndertakeRuleService.update(rule);
            return success(result);
        } catch (Exception e) {
            log.error("修改挂券承担规则失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"修改失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/delete")
    public ResultEntity delete(@RequestBody CouponUndertakeRule reRule) {
        try {
            return success(couponUndertakeRuleService.delete(reRule.getId()));
        } catch (Exception e) {
            log.error("删除挂券承担规则失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"删除失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/detail")
    @RedisCacheConvertEnable
    public ResultEntity detail(@RequestBody CouponUndertakeRule reRule) {
        try {
            if (reRule == null || reRule.getId() == null) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "参数错误：id不能为空");
            }
            return success(couponUndertakeRuleService.detail(reRule.getId()));
        } catch (Exception e) {
            log.error("查询挂券承担规则详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"查询详情失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/list")
    @RedisCacheConvertEnable
    public ResultEntity list(@RequestBody CouponUndertakeRule query) {
        try {
            return success(new PageInfo<>(couponUndertakeRuleService.list(query)));
        } catch (Exception e) {
            log.error("查询挂券承担规则列表失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"查询列表失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/cancel")
    public ResultEntity cancel(@RequestBody CouponUndertakeRule reRule) {
        try {
            return success(couponUndertakeRuleService.cancel(reRule.getId()));
        } catch (Exception e) {
            log.error("终止挂券承担规则失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR,"终止失败：" + e.getMessage());
        }
    }

    @SuppressWarnings("rawtypes")
    @PostMapping("/match")
    public ResultEntity match(@RequestBody CouponUndertakeRuleMatchDTO matchDTO) {
        try {
            CouponUndertakeRule rule = couponUndertakeRuleService.match(matchDTO);
            if (rule == null) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, "未匹配到挂券承担规则");
            }
            return success(rule);
        } catch (Exception e) {
            log.error("匹配挂券承担规则失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "匹配失败：" + e.getMessage());
        }
    }

    // 延期挂券规则单
    @PostMapping("/extension")
    @SuppressWarnings("rawtypes")
    public ResultEntity extension(@RequestBody CouponUndertakeRule rule) {
        try {
            return success(couponUndertakeRuleService.extension(rule));
        } catch (Exception e) {
            log.error("延期挂券规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "延期失败：" + e.getMessage());
        }
    }

    // 查询收银网报编码
    @PostMapping("/findAccountCode")
    @SuppressWarnings("rawtypes")
    public ResultEntity findAccountCode() {
        return success(couponUndertakeRuleService.findAccountCode());
    }
} 
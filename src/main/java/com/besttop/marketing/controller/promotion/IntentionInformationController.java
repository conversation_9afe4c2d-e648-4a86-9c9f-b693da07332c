package com.besttop.marketing.controller.promotion;

import com.alibaba.fastjson.JSONObject;
import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.promotion.IntentionInformation;
import com.besttop.marketing.service.promotion.IntentionInformationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: IntentionInformationController
 * @Description: TODO
 * @Author: Luxi
 * @Date: 2022/5/27 13:57
 * @Version: 1.0
 */
@RestController
@RequestMapping("/intentionInformation")
@Slf4j
public class IntentionInformationController extends BaseController {

    @Autowired
    private IntentionInformationService service;

    /**
     * Description: 意向登记
     *
     * @param param:
     * @return: com.besttop.common.model.ResultEntity
     * @Initiator: 邓雄峰
     * @Iteration: 新百0527
     * @Author: Luxi
     * @Date: 2022/5/27 14:16
     */
    @PostMapping("/add")
    @OperationLog(opName = "意向登记")
    public ResultEntity add(@RequestBody IntentionInformation param) {
        try {
            String msg = service.add(param);
            if (HttpStatus.OK.getReasonPhrase().equals(msg)) {
                return success();
            }
            return paramError(msg);
        } catch (Exception e) {
            log.info("<intentionInformation/add>-Message->{}", JSONObject.toJSONString(e.getMessage()));
            log.info("<intentionInformation/add>-StackTrace->{}", JSONObject.toJSONString(e.getStackTrace()));
            return paramError(e.getMessage());
        }
    }

    /**
     * Description: 根据发放单号回显预约意向
     *
     * @param param:
     * @return: com.besttop.common.model.ResultEntity
     * @Initiator: 邓雄峰
     * @Iteration: 新百0527
     * @Author: Luxi
     * @Date: 2022/5/27 15:42
     */
    @PostMapping("/findByCode")
    @OperationLog(opName = "根据发放单号回显预约意向")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody IntentionInformation param) {
        return success(service.findByCode(param));
    }

}

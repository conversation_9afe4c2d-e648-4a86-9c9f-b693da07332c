package com.besttop.marketing.controller.promotion;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.promotion.param.PromotionPublishParam;
import com.besttop.marketing.service.promotion.PromotionPublishService;
import com.besttop.marketing.util.CollectionUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 促销券投放规则定义表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-26
 */
@RestController
@RequestMapping("/promotionPublish")
public class PromotionPublishController extends BaseController {

    @Autowired
    private PromotionPublishService publishService;

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * 新增促销券投放规则定义
     *
     * @methodName add
     * @description TODO
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 17:05
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增促销券投放规则定义")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) PromotionPublishParam param) {
        String message=publishService.add(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 编辑促销券投放规则定义
     *
     * @methodName update
     * @description 编辑促销券投放规则定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 14:43
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑促销券投放规则定义")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) PromotionPublishParam param) {
        try {
            if(StringUtils.isBlank(param.getId())){
                return paramError("请选择需要修改的数据");
            }
            String message=publishService.update(param);
            if(StringUtils.isBlank(message)){
                return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
            }else if("成功".equalsIgnoreCase(message)){
                return success();
            }else{
                return paramError(message);
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * 删除促销券投放规则定义
     *
     * @methodName del
     * @description 删除促销券投放规则定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 14:48
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除促销券投放规则定义")
    public ResultEntity del(@RequestBody PromotionPublishParam param) {
        try {
            if (CollectionUtil.isEmpty(param.getIds())) {
                return paramError("请选择需要删除的数据");
            }
            if (publishService.del(param)>0) {
                return success();
            }
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * 审核促销券投放规则定义
     *
     * @methodName audit
     * @description 审核促销券投放规则定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 14:53
     */
    @RequestMapping(value = "/audit")
    @OperationLog(opName = "审核促销券投放规则定义")
    public ResultEntity audit(@RequestBody PromotionPublishParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择需要审核的数据");
            }
            if(null==param.getEndTime()){
                return paramError("请传入结束日期");
            }
            String message=publishService.audit(param);
            if(StringUtils.isBlank(message)){
                return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
            }else if("成功".equalsIgnoreCase(message)){
                return success();
            }else{
                return paramError(message);
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * 终止促销券投放规则定义
     *
     * @methodName terminal
     * @description 终止促销券投放规则定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 15:01
     */
    @RequestMapping(value = "/terminal")
    @OperationLog(opName = "终止促销券投放规则定义")
    public ResultEntity terminal(@RequestBody PromotionPublishParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择需要审核的数据");
            }
            param.setStatus(CommonEnums.PROMOTION_PUBLISH_STOP.getCode());
            param.setCancelTime(new Date());
            param.setCancelBy(loginCacheUtil.getUserCode());
            if(publishService.updateById(param)){
                return success();
            }
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * 模糊查询列表
     *
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 15:03
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody PromotionPublishParam param) {
        return success(new PageInfo<>(publishService.findBySelected(param)));
    }

    /**
     * @methodName queryPromotion
     * @description 查询促销券--促销券销售
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/2 17:04
     */
    @RequestMapping(value = "/queryPromotion")
    @RedisCacheConvertEnable
    public ResultEntity queryPromotion(@RequestBody PromotionPublishParam param){
        return success(new PageInfo<>(publishService.queryPromotion(param)));
    }
}

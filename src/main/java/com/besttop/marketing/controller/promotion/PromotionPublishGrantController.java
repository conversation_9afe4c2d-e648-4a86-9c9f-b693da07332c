package com.besttop.marketing.controller.promotion;


import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.promotion.PromotionPublish;
import com.besttop.marketing.model.promotion.param.PromotionPublishGrantParam;
import com.besttop.marketing.model.promotion.param.PromotionPublishParam;
import com.besttop.marketing.service.promotion.PromotionPublishGrantService;
import com.besttop.redis.model.RedisMultiLockObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 促销券发放详情管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@RestController
@RequestMapping("/promotionPublishGrant")
@Slf4j
public class PromotionPublishGrantController extends BaseController {

    @Autowired
    private PromotionPublishGrantService promotionPublishGrantService;

    @Autowired
    RedisLockUtil redisLockUtil;

    /***
     *查询促销券
     * @methodName findPromotionPublish
     * @description 
     * @params [promotionPublish]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/3 15:48
     */
    @PostMapping("/findPromotionPublishPc")
    @RedisCacheConvertEnable
    public ResultEntity<List<PromotionPublish>> findPromotionPublishPc(@RequestBody PromotionPublishParam promotionPublish) {
        return promotionPublishGrantService.findPromotionPublishPc(promotionPublish);
    }
    
    @PostMapping("/findPromotionPublish")
    @RedisCacheConvertEnable
    public ResultEntity<List<PromotionPublish>> findPromotionPublish(@RequestBody PromotionPublishParam promotionPublish) {
        return promotionPublishGrantService.findPromotionPublish(promotionPublish);
    }

    /***
     *促销券发放
     * @methodName grant
     * @description
     * @params [promotionPublish]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/3 16:48(*^_^*)TING:
     * /promotionPublishGrant/grant
     */
    @PostMapping("/grant")
    @OperationLog(opName = "促销券发放")
    public ResultEntity grant(@RequestBody PromotionPublishParam promotionPublish) {
        try {
            return promotionPublishGrantService.grant(promotionPublish);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_ROLE_ERROR,e.getMessage());
        }
    }

    /***
     *促销券退回
     * @methodName sendBack
     * @description
     * @params [promotionPublish]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/4 9:18
     */
    @PostMapping("/sendBack")
    @OperationLog(opName = "促销券退回")
    public ResultEntity sendBack(@RequestBody PromotionPublishParam promotionPublish) {
        return promotionPublishGrantService.sendBack(promotionPublish);
    }

    /***
     *查询发放列表
     * @methodName findGrantInfo
     * @description
     * @params [promotionPublish]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/3 18:09
     */
    @PostMapping("/findGrantInfo")
    @RedisCacheConvertEnable
    public ResultEntity<List<PromotionPublishGrantParam>> findGrantInfo(@RequestBody PromotionPublishGrantParam promotionPublish) {
        return promotionPublishGrantService.findGrantInfo(promotionPublish);
    }

    /**
     * Description: 删除发放记录
     *
     * @param param:
     * @return: com.besttop.common.model.ResultEntity
     * @Initiator: 费旭伟
     * @Iteration: 海达突击01
     * @Author: Luxi
     * @Date: 2022/4/20 10:32
     */
    @OperationLog(opName = "删除发放记录")
    @PostMapping("/deleteInfo")
    public ResultEntity deleteInfo(@RequestBody PromotionPublishGrantParam param) {
        try {
            String msg = promotionPublishGrantService.deleteInfo(param);
            if (HttpStatus.OK.getReasonPhrase().equals(msg)) {
                return success();
            }
            return paramError(msg);
        } catch (Exception e) {
            log.error("</promotionPublishGrant/deleteInfo> =Error= {}", JSON.toJSONString(e.getStackTrace()));
            log.info("/promotionPublishGrant/deleteInfo> =Info= {}", e.getMessage());
            return paramError(e.getMessage());
        }
    }

    /**
     * Description: 回收促销券
     *
     * @param param:
     * @return: com.besttop.common.model.ResultEntity
     * @Initiator: 费旭伟
     * @Iteration: 海达突击01
     * @Author: Luxi
     * @Date: 2022/4/20 14:38
     */
    @OperationLog(opName = "回收促销券")
    @PostMapping("/recycleInfo")
    public ResultEntity recycleInfo(@RequestBody PromotionPublishGrantParam param) {
        try {
            return promotionPublishGrantService.recycleInfo(param);
        } catch (Exception e) {
            log.error("</promotionPublishGrant/recycleInfo> =Error= {}", JSON.toJSONString(e.getStackTrace()));
            log.info("/promotionPublishGrant/recycleInfo> =Info= {}", e.getMessage());
            return paramError(e.getMessage());
        }
    }

    /**
     * Description:小程序促销券保存收款
     *
     * @param param: 单参数 促销券发放单号
     * @return: com.besttop.common.model.ResultEntity
     * @Initiator: 邓雄峰
     * @Iteration: 新百0426
     * @Author: Luxi
     * @Date: 2022/5/6 14:27
     */
    @OperationLog(opName = "小程序促销券收款")
    @PostMapping("/payGrantSave")
    public ResultEntity payGrantSave(@RequestBody PromotionPublishGrantParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("请选择支付单据");
        }
        RedisMultiLockObject lockObject = null;
        try {
            // 批量锁定
            log.info("payGrantSave获取缓存锁");
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getCode()), false);
            if (null == lockObject) {
                log.info("payGrantSave获取缓存锁失败");
                return paramError("收款中，请勿重复操作");
            }
            // 收款操作
            return promotionPublishGrantService.payGrantSave(param);
        } catch (Exception e) {
            log.info("收款失败:{}", JSON.toJSON(e.getStackTrace()));
            return paramError(e.getMessage());
        } finally {
            redisLockUtil.releaseBatchLock(lockObject);
        }
    }

    /**
     * Description: 小程序促销券退款
     *
     * @param param: 单参数 促销券回收单号
     * @return: com.besttop.common.model.ResultEntity
     * @Initiator: 邓雄峰
     * @Iteration: 新百0426
     * @Author: Luxi
     * @Date: 2022/5/9 10:23
     */
    @OperationLog(opName = "小程序促销券退款")
    @PostMapping("/refundGrantSave")
    public ResultEntity refundGrantSave(@RequestBody PromotionPublishGrantParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("请选择退款单据");
        }

        RedisMultiLockObject lockObject = null;
        try {
            // 批量锁定
            log.info("refundGrantSave获取缓存锁");
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getCode()), false);
            if (null == lockObject) {
                log.info("refundGrantSave获取缓存锁失败");
                return paramError("退款中，请勿重复操作");
            }
            // 退款操作
            return promotionPublishGrantService.refundGrantSave(param);
        } catch (Exception e) {
            log.info("退款失败:{}", JSON.toJSON(e.getStackTrace()));
            return paramError(e.getMessage());
        } finally {
            redisLockUtil.releaseBatchLock(lockObject);
        }
    }
}

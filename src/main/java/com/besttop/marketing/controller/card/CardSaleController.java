package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardPrintInfo;
import com.besttop.marketing.model.card.CardSale;
import com.besttop.marketing.model.card.CardSaleDetail;
import com.besttop.marketing.model.thirdparty.param.RefundmentParams;
import com.besttop.marketing.service.card.CardSaleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 卡销售 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@RestController
@RequestMapping("/cardSale")
@Slf4j
public class CardSaleController extends BaseController {
    @Autowired
    private CardSaleService service;


    /**
     * 列表查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 列表查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<CardSale> findAll(@RequestBody CardSale param) {
        return success(service.findAll(param));
    }


    /**
     * 明细查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 明细查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity<CardSale> findDetail(@RequestBody CardSale param) {
        return success(service.findDetail(param));
    }

    /**
     * 根据卡号查卡信息
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCard
     * @description 根据卡号查卡信息
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findCard")
    @RedisCacheConvertEnable
    public ResultEntity<CardDetail> findCard(@RequestBody CardDetail param) {
        return success(service.findCard(param));
    }


    /**
     * 储值卡充值
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 储值卡充值
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/recharge")
    @RedisCacheConvertEnable
    @OperationLog(opName = "储值卡充值")
    public ResultEntity addRecharge(@RequestBody @Validated(GroupAdd.class) CardSale param) {
        try {
            String str = service.addRecharge(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 退回
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 退回
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/back")
    @RedisCacheConvertEnable
    @OperationLog(opName = "储值卡退回")
    public ResultEntity back(@RequestBody CardSale param) {
        try {
            String str = service.back(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 储值卡删除
     *
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * @methodName delete
     * @description 删除
     * <AUTHOR>
     * @date 2020/4/6 16:06
     */
    @PostMapping(value = "/delete")
    @OperationLog(opName = "储值卡删除")
    public ResultEntity delete(@RequestBody CardSaleDetail entity) {

        try {
            String str = service.delete(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }

    }

    /**
     * 保存收款
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName pay
     * @description 保存收款
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/paySave")
    @RedisCacheConvertEnable
    @OperationLog(opName = "储值卡保存收款")
    @Deprecated
    public ResultEntity paySave(@RequestBody CardSale param) {
        try {
            return success(service.paySave(param));
        } catch (Exception e) {
            log.error("支付失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * 支付订单
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName pay
     * @description 支付订单
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/payOrder")
    @RedisCacheConvertEnable
    @OperationLog(opName = "储值卡支付订单")
    public ResultEntity payOrder(@RequestBody CardSale param) {
        try {
            return success(service.payOrder(param));
        } catch (Exception e) {
            log.error("支付失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * 查询销售记录
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findPayHistory
     * @description 查询销售记录
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findPayHistory")
    @RedisCacheConvertEnable
    public ResultEntity<CardSale> findPayHistory(@RequestBody CardSale param) {
        try {
            return success(service.findPayHistory(param));
        } catch (Exception e) {
            log.error("支付失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payRefund
     * @description 支付中的单据退款
     * <AUTHOR>
     * @date 2020/5/3 11:12
     */
    @PostMapping("/payRefund")
    @OperationLog(opName = "储值卡支付中的单据退款")
    @Deprecated
    public ResultEntity payRefund(@RequestBody RefundmentParams param) {
        try {
            String msg = service.checkRefundParam(param, "payRefund");
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            service.payRefund(param);
        } catch (Exception e) {
            log.error("退款失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundOrder
     * @description 退款
     * <AUTHOR>
     * @date 2020/5/4 10:55
     */
    @PostMapping("/refundOrder")
    @OperationLog(opName = "储值卡退款")
    @Deprecated
    public ResultEntity refundOrder(@RequestBody @Validated(GroupAdd.class) RefundmentParams param) {
        try {
            String msg = service.checkRefundParam(param, "refund");
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            service.refundOrder(param);
        } catch (Exception e) {
            log.error("退款失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return success();
    }
    /**
     * 保存退款
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundSave
     * @description 保存退款
     * <AUTHOR>
     * @date 2020/3/16 15:30
     */
    @PostMapping("/refundSave")
    @OperationLog(opName = "储值卡保存退款")
    @Deprecated
    public ResultEntity refundSave(@RequestBody RefundmentParams param) {
        try {
            String msg = service.checkRefundSaveParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            service.refundSave(param);
        } catch (Exception e) {
            log.error("保存退款失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "保存退款失败");
        }
        return success();
    }

    /**
     * 打印小票
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findPayHistory
     * @description 打印小票
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/queryPrintInfo")
    @RedisCacheConvertEnable
    @Deprecated
    public ResultEntity<CardPrintInfo> queryPrintInfo(@RequestBody CardSale param) {
        try {
            return success(service.queryPrintInfo(param));
        } catch (Exception e) {
            log.error("支付失败", e);
            return error(ResultEnum.ERP_USER_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }
}

package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.card.CardDefine;
import com.besttop.marketing.service.card.CardDefineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 卡制作(入库)
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-02
 */
@RestController
@RequestMapping("/cardDefine")
@Deprecated
public class CardDefineController extends BaseController {
    @Autowired
    private CardDefineService service;


    /**
     * 卡制作&入库列表查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 卡制作&入库列表查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<CardDefine> findAll(@RequestBody CardDefine param) {
        return success(service.findAll(param));
    }


    /**
     * 查询号段卡号是否已制卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 查询号段卡号是否已制卡
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findMadeIn")
    @RedisCacheConvertEnable
    public ResultEntity<String> findMadeIn(@RequestBody CardDefine param) {
        try {
            return success(service.findMadeIn(param));
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }
    /**
     * 明细查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 明细查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity<CardDefine> findDetail(@RequestBody CardDefine param) {
        return success(service.findDetail(param));
    }


    /**
     * 卡入库操作
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 卡入库操作
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/add")
    @RedisCacheConvertEnable
    @OperationLog(opName = "卡入库操作")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) List<CardDefine> param) {
        try {
            String str = service.add(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }



    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }
}

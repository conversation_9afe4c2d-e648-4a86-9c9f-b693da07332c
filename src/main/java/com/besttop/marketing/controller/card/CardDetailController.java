package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.mapper.card.CardDetailMapper;
import com.besttop.marketing.model.card.CardDefine;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardTopup;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.pay.PayParam;
import com.besttop.marketing.service.card.CardDetailService;
import com.besttop.marketing.util.RedisGetInfoUtil;
import com.besttop.redis.config.prefix.RedisSystemPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 储值卡维护
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@RestController
@RequestMapping("/cardDetail")
@Slf4j
public class CardDetailController extends BaseController {


    @Autowired
    private CardDetailMapper mapper;
    @Autowired
    private CardDetailService service;
    @Autowired
    private RedisGetInfoUtil redisGetInfoUtil;

    @GetMapping("/init")
    public ResultEntity<CardDetail> init() {
        return success(service.init());
    }
    /**
     * 列表查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 列表查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<CardDetail> findAll(@RequestBody CardDetail param) {
        return success(service.findAll(param));
    }

    /**
     * 查找未领用的储值卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findNotUseCard
     * @description 查找未领用的储值卡
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findNotUseCard")
    @RedisCacheConvertEnable
    public ResultEntity<CardDetail> findNotUseCard(@RequestBody CardDetail param) {
        return success(service.findNotUseCard(param));
    }
    /**
     * 下拉框
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOption
     * @description 下拉框
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findOption")
    @RedisCacheConvertEnable
    public ResultEntity<CardDetail> findOption(@RequestBody CardDetail param) {
        return success(mapper.findOption(param));
    }

    /**
     * 卡入库操作
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 卡入库操作
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/add")
    @RedisCacheConvertEnable
    @OperationLog(opName = "卡入库操作")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) List<CardDetail> param) {
        try {
            String str = service.add(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 制卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 卡制作
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/makingCard")
    @RedisCacheConvertEnable
    @OperationLog(opName = "制卡")
    public ResultEntity makingCard(@RequestBody @Validated(GroupUpdate.class) CardDetail param) {
        try {
            String str = service.makingCard(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }


    /**
     * 查询号段卡号是否已制卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 查询号段卡号是否已制卡
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findMadeIn")
    public ResultEntity<String> findMadeIn(@RequestBody CardDefine param) {
        try {
            return success(service.findMadeIn(param));
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }


    /**
     * 查询号段卡号是否已制卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findCardLimt
     * @description 查询号段卡号是否已制卡
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findCardLimt")
    public ResultEntity<String> findCardLimt() {
        try {
            Integer sum =  Integer.parseInt(redisGetInfoUtil.getSystemName(RedisSystemPrefix.CONFIG_PREFIX,CommonEnums.CARD_LIMIT_NUM.getCode()));
            return success(null == sum ? 200 : sum);
        } catch (Exception e) {
            log.info(e.getMessage());
            return success(200);
        }
    }

    /**
     * 批量启动停用
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOption
     * @description 下拉框
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/startOrStop")
    @RedisCacheConvertEnable
    public ResultEntity startOrStop(@RequestBody CardDetail param) {
        try {
            String str = service.startOrStop(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }


    /**
     * 重新制卡
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOption
     * @description 重新制卡
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/renewCard")
    @RedisCacheConvertEnable
    @OperationLog(opName = "重新制卡")
    public ResultEntity renewCard(@RequestBody @Validated(GroupAdd.class)CardDetail param) {
        try {
            String str = service.renewCard(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 读卡(根据磁道号读卡)
     *
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 读卡
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/readCard")
    @RedisCacheConvertEnable
    public ResultEntity readCard(@RequestBody CardDetail entity) {
        return success(service.readCard(entity));
    }

    /**
     * 储值卡充值
     * @methodName
     * @description TODO
     * @param
     * @return
     * <AUTHOR>
     * @date 2020/6/12 16:05
     */
    @PostMapping("/topup")
    @RedisCacheConvertEnable
    public ResultEntity topup(@RequestBody CardTopup entity) {
        try {
            String str = service.topup(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }
    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }

    /**
     * @methodName queryValueCard
     * @description 根据磁道号查询储值卡
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/3 18:02
     */
    @PostMapping("/queryValueCard")
    public ResultEntity queryValueCard(@RequestBody CardDetail entity) {
        if (StringUtils.isBlank(entity.getMagneticStripe())){
            return paramError("请刷卡");
        }
        return success(service.queryValueCard(entity));
    }
    @PostMapping("/queryValueCardXB")
    public ResultEntity queryValueCardXB(@RequestBody PayParam payParam) {
        if (StringUtils.isBlank(payParam.getMagneticStripe())){
            return paramError("请刷卡");
        }
        return success(service.queryValueCardXB(payParam));
    }
    
    @PostMapping("/queryValueCardDmall")
    public ResultEntity queryValueCardDmall(@RequestBody PayParam payParam) {
        if (StringUtils.isBlank(payParam.getPayCode())){
            return paramError("请扫码");
        }
        
        if (StringUtils.isBlank(payParam.getPayConfigCode())){
            return paramError("请传入支付配置编码");
        }
        
        return success(service.queryValueCardDmall(payParam));
    }
    
    @PostMapping("/queryValueCardXBList")
    public ResultEntity queryValueCardXBList(@RequestBody PayParam payParam) {
        return success(service.queryValueCardXBList(payParam));
    }
    /***
     *退款获取卡信息
     * @methodName readCard
     * @description
     * @params [entity]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/4 17:01
     */
    @PostMapping("/getCardCode")
    @RedisCacheConvertEnable
    public ResultEntity getCardCode(@RequestBody CardDetail entity) {
        return service.getCardCode(entity);
    }


    /***
     * 卡有效期延期
     * @methodName extensionCard
     * @description
     * @params [entity]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/24 17:01
     */
    @PostMapping("/extensionCard")
    @RedisCacheConvertEnable
    public ResultEntity extensionCard(@RequestBody CardDetail entity) {
        try {
            String str = service.extensionCard(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }
}

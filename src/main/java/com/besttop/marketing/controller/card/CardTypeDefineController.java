package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.card.CardTypeDefine;
import com.besttop.marketing.service.card.CardTypeDefineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 卡类型定义
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-02
 */
@RestController
@RequestMapping("/cardTypeDefine")
public class CardTypeDefineController extends BaseController {

    @Autowired
    private CardTypeDefineService service;

    /**
     * 列表查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 列表查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<CardTypeDefine> findAll(@RequestBody CardTypeDefine param) {
        return success(service.findAll(param));
    }

    /**
     * 下拉框
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOption
     * @description 下拉框
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findOption")
    @RedisCacheConvertEnable
    public ResultEntity<CardTypeDefine> findOption(@RequestBody CardTypeDefine param) {
        return success(service.findOption(param));
    }


    /**
     * 明细查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 明细查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findById")
    @RedisCacheConvertEnable
    public ResultEntity<CardTypeDefine> findById(@RequestBody CardTypeDefine param) {
        return success(service.getById(param));
    }


    /**
     * 新增
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 新增
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/add")
    @RedisCacheConvertEnable
    @OperationLog(opName = "新增卡类型定义")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) CardTypeDefine param) {
        try {
            String str = service.add(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 编辑
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/update")
    @RedisCacheConvertEnable
    @OperationLog(opName = "编辑卡类型定义")
    public ResultEntity updateAll(@RequestBody @Validated(GroupUpdate.class) CardTypeDefine entity) {
        try {
            String str = service.updateAll(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 删除
     *
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * @methodName delete
     * @description 删除
     * <AUTHOR>
     * @date 2020/4/6 16:06
     */
    @PostMapping(value = "/delete")
    @OperationLog(opName = "删除卡类型定义")
    public ResultEntity delete(@RequestBody CardTypeDefine entity) {
        if (null == entity.getId()) {
            return paramError("请选择要删除的数据");
        }
        try {
            String str = service.delete(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }

    }

    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }

}

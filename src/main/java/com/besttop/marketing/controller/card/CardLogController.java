package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardLog;
import com.besttop.marketing.service.card.CardLogService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 卡操作日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@RestController
@RequestMapping("/card/cardLog")
public class CardLogController extends BaseController {

    @Autowired
    private CardLogService service;

    /**
     * 充值记录列表查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 充值记录列表查询
     * <AUTHOR>
     * @date 2020/10/31 10:57
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<CardDetail> findAll(@RequestBody CardLog param) {
        return success(service.findAll(param));
    }

    /**
     * Description:储值卡收款记录
     *
     * <AUTHOR>
     * @date: 2020-12-28 10:46
     * @param:[param]
     * @return:com.besttop.common.model.ResultEntity<com.besttop.marketing.model.card.CardDetail>
     */
    @PostMapping("/list")
    @RedisCacheConvertEnable
    public ResultEntity list(@RequestBody CardLog param) {
        return success(new PageInfo<>(service.list(param)));
    }


}

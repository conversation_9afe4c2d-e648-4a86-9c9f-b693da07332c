package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.card.CardRule;
import com.besttop.marketing.service.card.CardRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 储值规则定义
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@RestController
@RequestMapping("/cardRule")
@Deprecated
public class CardRuleController extends BaseController {
    @Autowired
    private CardRuleService service;

    /**
     * 列表查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 列表查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<CardRule> findAll(@RequestBody CardRule param) {
        return success(service.findAll(param));
    }


    /**
     * 明细查询
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findAll
     * @description 明细查询
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/findById")
    @RedisCacheConvertEnable
    public ResultEntity<CardRule> findById(@RequestBody CardRule param) {
        return success(service.getById(param));
    }


    /**
     * 新增
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 新增
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/add")
    @RedisCacheConvertEnable
    @OperationLog(opName = "新增储值规则定义")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) CardRule param) {
        try {
            String str = service.add(param);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 编辑
     * <AUTHOR>
     * @date 2020/4/6 16:47
     */
    @PostMapping("/update")
    @RedisCacheConvertEnable
    @OperationLog(opName = "编辑储值规则定义")
    public ResultEntity updateAll(@RequestBody @Validated(GroupUpdate.class) CardRule entity) {
        try {
            String str = service.updateAll(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 删除
     *
     * @param entity
     * @return com.besttop.common.model.ResultEntity
     * @methodName delete
     * @description 删除
     * <AUTHOR>
     * @date 2020/4/6 16:06
     */
    @PostMapping(value = "/delete")
    @OperationLog(opName = "删除储值规则定义")
    public ResultEntity delete(@RequestBody CardRule entity) {
        if (null == entity.getId()) {
            return paramError("请选择要删除的数据");
        }
        try {
            String str = service.delete(entity);
            return getResultEntity(str);
        } catch (RuntimeException e) {
            return paramError(e.getMessage());
        }

    }

    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }
}

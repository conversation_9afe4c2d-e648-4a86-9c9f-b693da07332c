package com.besttop.marketing.controller.card;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardPayParam;
import com.besttop.marketing.model.card.CardRelation;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.service.card.CardRelationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 卡制作(入库)
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-02
 */
@RestController
@RequestMapping("/cardRelation")
public class CardRelationController extends BaseController {
    @Autowired
    private CardRelationService service;



    /**
     * 生成二维码 可刷新
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName initCode
     * @description 生成二维码 可刷新
     * <AUTHOR>
     * @date 2020/12/25 16:05
     */
    @PostMapping("/initCode")
    @RedisCacheConvertEnable
    public ResultEntity initCode(@RequestBody CardRelation param) {
        return new ResultEntity(ResultEnum.SUCCESS,service.initCode(param),"成功");
    }



    /**
     * 支付核验
     */
    @RequestMapping(value = "/verification")
    @OperationLog(opName = "支付核验")
    public ResultEntity verification(@RequestBody @Validated(GroupAdd.class) CardPayParam param) {
        try {
            return service.verification(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }
    /**
     * 支付核验
     */
    @RequestMapping(value = "/payApplet")
    @OperationLog(opName = "储值卡拉起支付")
    public ResultEntity payApplet(@RequestBody @Validated(GroupAdd.class) CardPayParam param) {
        try {
            if (StringUtils.isEmpty(param.getOpenId())) {
                return paramError("微信小程序用户openId不能为空!");
            }
            if (StringUtils.isEmpty(param.getPayConfigCode())) {
                return paramError("支付配置编码不能为空!");
            }
            if (StringUtils.isEmpty(param.getStoreCode())) {
                return paramError("制单机构不能为空!");
            }
            return service.payApplet(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }
    /**
     * result
     */
    private ResultEntity getResultEntity(String str) {
        if (str.equals(HttpStatus.OK.getReasonPhrase())) {
            return success();
        } else {
            return paramError(str);
        }
    }

    /**
     * @methodName queryCardByCode
     * @description 通过二维码查询储值卡信息
     * @param relation
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/12/28 10:42
     */
    @PostMapping("/queryCardByCode")
    public ResultEntity queryCardByCode(@RequestBody CardDetail param){
        if (StringUtils.isBlank(param.getOpenId())){
            return paramError("请输入二维码");
        }
        return service.queryCardByCode(param);
    }
}

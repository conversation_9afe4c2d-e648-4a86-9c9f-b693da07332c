package com.besttop.marketing.controller.gift;


import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.gift.GiftSaleNoteSet;
import com.besttop.marketing.service.gift.GiftSaleNoteSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 礼品销售备注设置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@RestController
@RequestMapping("/giftSaleNoteSet")
public class GiftSaleNoteSetController extends BaseController {
    @Autowired
    private GiftSaleNoteSetService giftSaleNoteSetService;

    @PostMapping("/findOption")
    public ResultEntity findOption(@RequestBody GiftSaleNoteSet giftSaleNoteSet) {
        return success(giftSaleNoteSetService.findOption(giftSaleNoteSet));
    }
}

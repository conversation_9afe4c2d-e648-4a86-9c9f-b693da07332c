package com.besttop.marketing.controller.mall;

import com.baomidou.mybatisplus.extension.api.R;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.mall.OrderMallParam;
import com.besttop.marketing.service.mall.ShoppingOrderMallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: ShoppingOrderAppController</p>
 * <p>Description: ShoppingOrderAppController 小商城下单</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/7/28 14:37 Create by Sissi
 * </pre>
 */
@Slf4j
@RestController
@RequestMapping("/shoppingOrderMall")
public class ShoppingOrderMallController extends BaseController {

    @Autowired
    private ShoppingOrderMallService shoppingOrderMallService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addOrderApp
     * @description 小商城销售开票
     * <AUTHOR>
     * @date 2020/7/28 14:44
     */
    @PostMapping("/addOrderMall")
    @OperationLog(opName = "小商城销售开票")
    public ResultEntity addOrderMall(@RequestBody @Validated(GroupAdd.class) OrderMallParam param) {
        try {
            ResultEntity resultEntity = shoppingOrderMallService.checkParam(param);
            if (resultEntity.getFlag() != 1) {
                return paramError(resultEntity.getMessage());
            }
            List<String> codes = shoppingOrderMallService.addOrderMall(param);
            return success(codes);
        } catch (Exception e) {
            log.error("小商城销售开票失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }finally {
            if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
            }
        }
    }

}

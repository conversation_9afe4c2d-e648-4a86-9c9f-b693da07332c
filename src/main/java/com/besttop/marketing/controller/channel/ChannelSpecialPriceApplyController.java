package com.besttop.marketing.controller.channel;


import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.channel.param.ChannelOrderParam;
import com.besttop.marketing.model.channel.param.ChannelOrderUpdateParam;
import com.besttop.marketing.model.channel.param.ChannelSpecialPriceParam;
import com.besttop.marketing.model.channel.param.ChannelSpecialPriceUpdateParam;
import com.besttop.marketing.service.channel.ChannelOrderService;
import com.besttop.marketing.service.channel.ChannelSpecialPriceApplyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 渠道特价申请单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@RestController
@RequestMapping("/channelSpecialPriceApply")
public class ChannelSpecialPriceApplyController extends BaseController {

    @Autowired
    private ChannelSpecialPriceApplyService channelSpecialPriceApplyService;
    /**
     * 新增特价申请单
     *
     * @param channelSpecialPriceUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 新增/审核特价申请单
     * <AUTHOR>
     * @date 2021/5/06 16:27
     */
    @OperationLog(opName = "特价申请单添加")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody ChannelSpecialPriceUpdateParam channelSpecialPriceUpdateParam) {
        if (StringUtils.isEmpty(channelSpecialPriceUpdateParam.getChannelSpecialPriceApply().getStatus())) {
            return paramError("请选择单据状态");
        }
        try {
            channelSpecialPriceApplyService.add(channelSpecialPriceUpdateParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 修改特价申请单
     *
     * @param channelSpecialPriceUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 修改/审核特价申请单
     * <AUTHOR>
     * @date 2021/4/23 16:50
     */
    @OperationLog(opName = "修改/审核特价申请单")
    @PostMapping("/update")
    public ResultEntity<String> update(@RequestBody ChannelSpecialPriceUpdateParam channelSpecialPriceUpdateParam) {
        if (StringUtils.isEmpty(channelSpecialPriceUpdateParam.getChannelSpecialPriceApply().getId())) {
            return paramError("修改特价申请单传入id为空");
        }
        if (StringUtils.isEmpty(channelSpecialPriceUpdateParam.getChannelSpecialPriceApply().getStatus())) {
            return paramError("请选择单据状态");
        }
        try {
            channelSpecialPriceApplyService.update(channelSpecialPriceUpdateParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 删除特价申请单
     *
     * @param channelSpecialPriceParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName delete
     * @description 删除渠道订单
     * <AUTHOR>
     * @date 2021/4/23 16:48
     */
    @OperationLog(opName = "删除特价申请单")
    @PostMapping("/delete")
    public ResultEntity delete(@RequestBody ChannelSpecialPriceParam channelSpecialPriceParam) {
        if (CollectionUtils.isEmpty(channelSpecialPriceParam.getIds())) {
            return paramError("请选择未审核单据删除");
        }
        try {
            if(!channelSpecialPriceApplyService.delete(channelSpecialPriceParam)){
                return error(ResultEnum.ERP_MARK_DEL_ERROR, "");
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_DEL_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 条件查询
     *
     * @param channelSpecialPriceParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName find
     * @description 条件查询
     * <AUTHOR>
     * @date 2021/4/23 16:52
     */
    @OperationLog(opName = "查询特价申请单")
    @PostMapping("/find")
    @RedisCacheConvertEnable
    public ResultEntity find(@RequestBody ChannelSpecialPriceParam channelSpecialPriceParam) {
        try {
            return success(channelSpecialPriceApplyService.find(channelSpecialPriceParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * 条件查询
     *
     * @param channelSpecialPriceParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName find
     * @description 条件查询
     * <AUTHOR>
     * @date 2021/4/23 16:52
     */
    @OperationLog(opName = "查询特价申请单带SKU/品牌/品类的查询")
    @PostMapping("/findTodo")
    @RedisCacheConvertEnable
    public ResultEntity findTodo(@RequestBody ChannelSpecialPriceParam channelSpecialPriceParam) {
        try {
            return success(channelSpecialPriceApplyService.findTodo(channelSpecialPriceParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }
    /**
     * Description: 特价申请审批流查询
     *
     * <AUTHOR>
     * @date: 2020-11-12 14:29
     * @param:[billBudgetSales]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/getApprove")
    @RedisCacheConvertEnable
    public ResultEntity getApprove(@RequestBody ChannelSpecialPriceParam channelSpecialPriceParam) {
        try {
            return success(channelSpecialPriceApplyService.getApprove(channelSpecialPriceParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }
}

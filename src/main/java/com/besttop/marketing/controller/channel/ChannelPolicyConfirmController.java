package com.besttop.marketing.controller.channel;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.channel.ChannelPolicyConfirm;
import com.besttop.marketing.model.channel.param.ChannelPolicyConfirmInfoParam;
import com.besttop.marketing.model.channel.param.ChannelPolicyConfirmParam;
import com.besttop.marketing.model.channel.param.ChannelPolicyInfoParam;
import com.besttop.marketing.service.channel.ChannelPolicyConfirmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 渠道政策确认主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@RestController
@RequestMapping("/channelPolicyConfirm")
public class ChannelPolicyConfirmController extends BaseController {
    @Autowired
    ChannelPolicyConfirmService channelPolicyConfirmService;

    /**
     * <AUTHOR>
     * @create 2021/5/7 14:12
     * @desc 新增渠道政策折让单
     **/
    @OperationLog(opName = "新增渠道政策确认单")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) ChannelPolicyConfirmInfoParam channelPolicyConfirmInfoParam) {
        return channelPolicyConfirmService.add(channelPolicyConfirmInfoParam);
    }

    /**
     * <AUTHOR>
     * @create 2021/5/8 11:16
     * @desc 渠道政策筛选查询
     **/

    @OperationLog(opName = "渠道政策确认单筛选查询")
    @PostMapping("/queryChannelPolicyConfirm")
    @RedisCacheConvertEnable
    public ResultEntity<List<ChannelPolicyConfirm>> queryChannelPolicyConfirm(@RequestBody ChannelPolicyConfirmParam channelPolicyConfirmParam) {
        return channelPolicyConfirmService.queryChannelPolicyConfirm(channelPolicyConfirmParam);
    }

    /**
     * <AUTHOR>
     * @create 2021/5/8 11:16
     * @desc 更新渠道确认单
     **/
    @OperationLog(opName = "更新渠道确认单")
    @PostMapping("/updateChannelPolicyConfirm")
    public ResultEntity updateChannelPolicyConfirm(@RequestBody @Validated(GroupUpdate.class) ChannelPolicyConfirmInfoParam channelPolicyConfirmInfoParam) {
        return channelPolicyConfirmService.updateChannelPolicyConfirm(channelPolicyConfirmInfoParam);
    }

    /**
     * <AUTHOR>
     * @create 2021/5/8 11:16
     * @desc 删除渠道确认单
     **/
    @OperationLog(opName = "删除渠道确认单")
    @PostMapping("/deleteChannelPolicyConfirmByCode")
    public ResultEntity deleteChannelPolicyConfirmByCode(@RequestBody ChannelPolicyConfirmParam channelPolicyConfirmParam) {
        return channelPolicyConfirmService.deleteChannelPolicyConfirmByCode(channelPolicyConfirmParam);
    }

    /**
     * <AUTHOR>
     * @create 2021/5/8 11:16
     * @desc 渠道确认单详情查询
     **/
    @OperationLog(opName = "通过code查询渠道政策详情")
    @PostMapping("/queryChannelPolicyByCode")
    @RedisCacheConvertEnable
    public ResultEntity<ChannelPolicyConfirmInfoParam> queryChannelPolicyByCode(@RequestBody ChannelPolicyConfirmParam channelPolicyConfirmParam) {
        return channelPolicyConfirmService.queryChannelPolicyConfirmByCode(channelPolicyConfirmParam);
    }
}

package com.besttop.marketing.controller.channel;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.channel.ChannelPolicy;
import com.besttop.marketing.model.channel.param.ChannelPolicyInfoParam;
import com.besttop.marketing.model.channel.param.ChannelPolicyParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.channel.ChannelPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 渠道政策单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@RestController
@RequestMapping("/channelPolicy")
public class ChannelPolicyController extends BaseController {

    @Autowired
    private ChannelPolicyService channelPolicyService;

    /**
     * <AUTHOR>
     * @create 2021/4/26 10:17
     * @desc 渠道政策筛选查询
     **/
    @OperationLog(opName = "渠道政策筛选查询")
    @PostMapping("/queryChannelPolicy")
    @RedisCacheConvertEnable
    public ResultEntity<List<ChannelPolicy>> queryChannelContract(@RequestBody ChannelPolicyParam channelpolicyParam) {
        return channelPolicyService.queryChannelPolicy(channelpolicyParam);
    }

    /**
     * <AUTHOR>
     * @create  2021/4/26 11:11
     * @desc    新增渠道政策
     **/
    @OperationLog(opName = "新增渠道政策")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) ChannelPolicyInfoParam channelpolicyInfoParam) {
        return channelPolicyService.add(channelpolicyInfoParam);
    }

    /**
     * <AUTHOR>
     * @create  2021/4/28 10:03
     * @desc    修改渠道政策
     **/
    @OperationLog(opName = "修改政策（同变更）")
    @PostMapping("/updateChannelPolicy")
    public ResultEntity updateChannelPolicy(@RequestBody @Validated(GroupUpdate.class) ChannelPolicyInfoParam channelpolicyInfoParam) {
        return channelPolicyService.updateChannelPolicy(channelpolicyInfoParam);
    }

    /**
     * <AUTHOR>
     * @create  2021/4/28 11:14
     * @desc    updateAudit
     **/
    @OperationLog(opName = "变更审核政策单")
    @PostMapping("/updateAudit")
    public ResultEntity updateAudit(@RequestBody @Validated(GroupUpdate.class) ChannelPolicyInfoParam channelpolicyInfoParam) {
        return channelPolicyService.updateAudit(channelpolicyInfoParam);
    }

    /**
     * <AUTHOR>
     * @create  2021/4/28 15:01
     * @desc    查询渠道信息详情
     **/
    @OperationLog(opName = "通过code查询渠道政策详情")
    @PostMapping("/queryChannelPolicyByCode")
    @RedisCacheConvertEnable
    public ResultEntity<ChannelPolicyInfoParam> queryChannelPolicyByCode(@RequestBody ChannelPolicyParam channelPolicyParam) {
        return channelPolicyService.queryChannelPolicyByCode(channelPolicyParam);
    }
    /**
     * <AUTHOR>
     * @create  2021/4/29 9:32
     * @desc    删除政策信息
     **/
    @OperationLog(opName = "删除政策信息")
    @PostMapping("/deleteChannelPolicyByCode")
    @RedisCacheConvertEnable
    public ResultEntity<ChannelPolicyInfoParam> deleteChannelPolicyByCode(@RequestBody ChannelPolicyParam channelPolicyParam) {
        return channelPolicyService.deleteChannelPolicyByCode(channelPolicyParam);
    }
    @OperationLog(opName = "作废渠道信息")
    @PostMapping("/invalidChannelPolicyByCode")
    @RedisCacheConvertEnable
    public ResultEntity<ChannelPolicyInfoParam> invalidChannelPolicyByCode(@RequestBody ChannelPolicyParam channelPolicyParam) {
        return channelPolicyService.invalidChannelPolicyByCode(channelPolicyParam);
    }

    @OperationLog(opName = "试算")
    @PostMapping("/calculateChannelPolicy")
    @RedisCacheConvertEnable
    public ResultEntity<ChannelPolicyInfoParam> calculateChannelPolicy(@RequestBody ChannelPolicyParam channelpolicyParam) {
        return channelPolicyService.calculateChannelPolicy(channelpolicyParam);
    }
}

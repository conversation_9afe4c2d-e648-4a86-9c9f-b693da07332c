package com.besttop.marketing.controller.channel;


import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.channel.param.ChannelOrderParam;
import com.besttop.marketing.model.channel.param.ChannelOrderUpdateParam;
import com.besttop.marketing.service.channel.ChannelOrderService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 渠道订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@RestController
@RequestMapping("/channelOrder")
public class ChannelOrderController extends BaseController {

    @Autowired
    private ChannelOrderService channelOrderService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    /**
     * 新增渠道订单
     *
     * @param channelOrderUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 新增/审核渠道订单
     * <AUTHOR>
     * @date 2021/4/23 16:27
     */
    @OperationLog(opName = "新增/审核渠道订单")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody ChannelOrderUpdateParam channelOrderUpdateParam) {

        if (StringUtils.isNotEmpty(channelOrderUpdateParam.getChannelOrder().getId())) {
            return paramError("新增渠道订单传入id,不能新增");
        }
        if (StringUtils.isEmpty(channelOrderUpdateParam.getChannelOrder().getStatus())) {
            return paramError("请选择单据状态");
        }
        try {
            channelOrderService.add(channelOrderUpdateParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 删除渠道订单
     *
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName delete
     * @description 删除渠道订单
     * <AUTHOR>
     * @date 2021/4/23 16:48
     */
    @OperationLog(opName = "删除渠道订单")
    @PostMapping("/delete")
    public ResultEntity delete(@RequestBody ChannelOrderParam channelOrderParam) {
        if (null == channelOrderParam.getIds() || channelOrderParam.getIds().isEmpty()) {
            return paramError("请选择未审核单据删除");
        }
        try {
            channelOrderService.delete(channelOrderParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_DEL_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 修改渠道订单
     *
     * @param channelOrderUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 修改/审核渠道订单
     * <AUTHOR>
     * @date 2021/4/23 16:50
     */
    @OperationLog(opName = "修改/审核渠道订单")
    @PostMapping("/update")
    public ResultEntity<String> update(@RequestBody ChannelOrderUpdateParam channelOrderUpdateParam) {
        if (StringUtils.isEmpty(channelOrderUpdateParam.getChannelOrder().getId())) {
            return paramError("修改渠道订单传入id为空");
        }
        if (StringUtils.isEmpty(channelOrderUpdateParam.getChannelOrder().getStatus())) {
            return paramError("请选择单据状态");
        }
        try {
            channelOrderService.update(channelOrderUpdateParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 条件查询
     *
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName find
     * @description 条件查询
     * <AUTHOR>
     * @date 2021/4/23 16:52
     */
    @PostMapping("/find")
    @RedisCacheConvertEnable
    public ResultEntity find(@RequestBody ChannelOrderParam channelOrderParam) {
        return success(channelOrderService.find(channelOrderParam));
    }

    /**
     * 渠道订单明细表查询
     *
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 渠道订单明细表查询
     * <AUTHOR>
     * @date 2021/4/23 16:59
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody ChannelOrderParam channelOrderParam) {
        if (StringUtils.isEmpty(channelOrderParam.getOrderCode())) {
            return paramError("渠道订单orderCode传参为空，不能查询");
        }
        return success(channelOrderService.findDetail(channelOrderParam));
    }

    /**
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateCancel
     * @description 终止渠道订单
     * <AUTHOR>
     * @date 2021/4/23 15:09
     */
    @OperationLog(opName = "终止渠道订单")
    @PostMapping("/updateCancel")
    public ResultEntity updateCancel(@RequestBody ChannelOrderParam channelOrderParam) {
        if (null == channelOrderParam.getIds() || channelOrderParam.getIds().isEmpty()) {
            return paramError("请选择终止单据");
        }
        try {
            channelOrderService.updateCancel(channelOrderParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findSkuByChannel
     * @description 查询渠道客户Sku
     * <AUTHOR>
     * @date 2021/4/23 15:42
     */
    @PostMapping("/findSkuByChannel")
    @RedisCacheConvertEnable
    public ResultEntity findSkuByChannel(@RequestBody ChannelOrderParam channelOrderParam) {
        try {
            return success(channelOrderService.findSkuByChannel(channelOrderParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findSkuByPolicy
     * @description 根据政策查询价格
     * <AUTHOR>
     * @date 2021/4/25 16:10
     */
    @PostMapping("/findSkuByPolicy")
    @RedisCacheConvertEnable
    public ResultEntity findSkuByPolicy(@RequestBody ChannelOrderParam channelOrderParam) {
        if (StringUtils.isEmpty(channelOrderParam.getChannelCode())) {
            return paramError("请传入渠道客户编码");
        }
        if (StringUtils.isEmpty(channelOrderParam.getSkuCode())) {
            return paramError("请传入SKU编码");
        }
        try {
            return success(channelOrderService.findSkuByPolicy(channelOrderParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param channelOrderParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findTodo
     * @description 查询待处理渠道订单
     * <AUTHOR>
     * @date 2021/5/7 9:30
     */
    @PostMapping("/findTodo")
    @RedisCacheConvertEnable
    public ResultEntity findTodo(@RequestBody ChannelOrderParam channelOrderParam) {
        return success(channelOrderService.findTodo(channelOrderParam));
    }


    /**
     * 渠道付款
     *
     * @param channelOrderUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName channnelPay
     * @description 渠道付款
     * <AUTHOR>
     * @date 2021/4/23 16:50
     */
    @OperationLog(opName = "渠道付款")
    @PostMapping("/channnelPay")
    public ResultEntity<String> channnelPay(@RequestBody ChannelOrderParam channelOrderParam) {
        if (StringUtils.isEmpty(channelOrderParam.getId())) {
            return paramError("渠道订单传入id为空");
        }
        try {
            channelOrderService.channnelPay(channelOrderParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }finally {
            if (ObjectUtils.isNotEmpty(channelOrderParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(channelOrderParam.getStoreSkuInventoryListKey());
            }
        }
        return success();
    }


    /**
     * 查找渠道代付款订单
     *
     * @param channelOrderUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryNoPayment
     * @description 查找渠道代付款订单
     * <AUTHOR>
     * @date 2021/4/23 16:50
     */
    @OperationLog(opName = "查找渠道代付款订单")
    @PostMapping("/queryNoPayment")
    public ResultEntity queryNoPayment(@RequestBody ChannelOrderParam channelOrderParam) {
        if (StringUtils.isEmpty(channelOrderParam.getId())) {
            return paramError("渠道订单传入id为空");
        }
        try {
            return success(channelOrderService.queryNoPayment(channelOrderParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
    }
}

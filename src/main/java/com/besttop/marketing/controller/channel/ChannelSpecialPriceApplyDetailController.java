package com.besttop.marketing.controller.channel;


import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.channel.param.ChannelSpecialPriceDetailParam;
import com.besttop.marketing.model.channel.param.ChannelSpecialPriceParam;
import com.besttop.marketing.service.channel.ChannelSpecialPriceApplyDetailService;
import com.besttop.marketing.service.channel.ChannelSpecialPriceApplyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 渠道特价申请明细单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
@RestController
@RequestMapping("/channelSpecialPriceApplyDetail")
public class ChannelSpecialPriceApplyDetailController extends BaseController {

    @Autowired
    private ChannelSpecialPriceApplyDetailService channelSpecialPriceApplyDetailService;

    /**
     * 特价申请单明细表查询
     *
     * @param channelSpecialPriceParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 渠道订单明细表查询
     * <AUTHOR>
     * @date 2021/4/23 16:59
     */
    @OperationLog(opName = "查询特价申请单详情")
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody ChannelSpecialPriceParam channelSpecialPriceParam) {
        if (StringUtils.isEmpty(channelSpecialPriceParam.getApplyCode())) {
            return paramError("特价申请明细申请单applyCode传参为空，不能查询");
        }
        return success(channelSpecialPriceApplyDetailService.findDetail(channelSpecialPriceParam));
    }

    /**
     * 特价申请单明细表删除
     *
     * @param channelSpecialPriceDetailParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 渠道订单明细表查询
     * <AUTHOR>
     * @date 2021/4/23 16:59
     */
    @OperationLog(opName = "删除特价申请单详情")
    @PostMapping("/delete")
    @RedisCacheConvertEnable
    public ResultEntity delete(@RequestBody ChannelSpecialPriceDetailParam channelSpecialPriceDetailParam) {
        if (StringUtils.isEmpty(channelSpecialPriceDetailParam.getApplyCode())) {
            return paramError("特价申请明细code传参为空，不能查询");
        }
        if (CollectionUtils.isEmpty(channelSpecialPriceDetailParam.getIds())) {
            return paramError("特价申请明细ids传参为空，不能查询");
        }
        return success(channelSpecialPriceApplyDetailService.delete(channelSpecialPriceDetailParam));
    }
}

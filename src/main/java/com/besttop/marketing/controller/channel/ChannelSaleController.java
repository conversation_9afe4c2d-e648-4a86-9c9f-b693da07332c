package com.besttop.marketing.controller.channel;


import com.alibaba.fastjson.JSON;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.channel.param.ChannelSaleParam;
import com.besttop.marketing.model.channel.param.ChannelSaleUpdateParam;
import com.besttop.marketing.service.channel.ChannelSaleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.besttop.common.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 渠道开单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Slf4j
@RestController
@RequestMapping("/channelSale")
public class ChannelSaleController extends BaseController {

    @Autowired
    private ChannelSaleService channelSaleService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    /**
     * 新增渠道开单
     *
     * @param channelSaleUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 新增/审核渠道开单
     * <AUTHOR>
     * @date 2021/4/26 16:27
     */
    @OperationLog(opName = "新增/审核渠道开单")
    @PostMapping("/add")
    public ResultEntity add(@RequestBody ChannelSaleUpdateParam channelSaleUpdateParam) {

        if (StringUtils.isNotEmpty(channelSaleUpdateParam.getChannelSale().getId())) {
            return paramError("新增渠道开单传入id,不能新增");
        }
        if (StringUtils.isEmpty(channelSaleUpdateParam.getChannelSale().getStatus())) {
            return paramError("请选择单据状态");
        }
        String lockKey = null;
        try {
            lockKey = redisLockUtil.getStoreSkuLock(channelSaleUpdateParam.getChannelSale().getSaleStore(), channelSaleUpdateParam.getChannelSale().getChannelCode());

            channelSaleService.add(channelSaleUpdateParam);
        } catch (Exception e) {

            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }finally {
            redisLockUtil.releaseLock(lockKey);
            if (ObjectUtils.isNotEmpty(channelSaleUpdateParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(channelSaleUpdateParam.getStoreSkuInventoryListKey());
            }
        }
        return success();
    }

    /**
     * 删除渠道开单
     *
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName delete
     * @description 删除渠道开单
     * <AUTHOR>
     * @date 2021/4/26 16:48
     */
    @OperationLog(opName = "删除渠道开单")
    @PostMapping("/delete")
    public ResultEntity delete(@RequestBody ChannelSaleParam channelSaleParam) {
        if (null == channelSaleParam.getIds() || channelSaleParam.getIds().isEmpty()) {
            return paramError("请选择未审核单据删除");
        }
        try {
            if (!channelSaleService.delete(channelSaleParam)) {
                return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除失败");
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_DEL_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * 修改渠道开单
     *
     * @param channelSaleUpdateParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 修改/审核渠道开单
     * <AUTHOR>
     * @date 2021/4/26 16:50
     */
    @OperationLog(opName = "修改/审核渠道开单")
    @PostMapping("/update")
    public ResultEntity<String> update(@RequestBody ChannelSaleUpdateParam channelSaleUpdateParam) {
        if (StringUtils.isEmpty(channelSaleUpdateParam.getChannelSale().getId())) {
            return paramError("修改渠道开单传入id为空");
        }
        if (StringUtils.isEmpty(channelSaleUpdateParam.getChannelSale().getStatus())) {
            return paramError("请选择单据状态");
        }
        String lockKey = null;
        try {
            lockKey = redisLockUtil.getStoreSkuLock(channelSaleUpdateParam.getChannelSale().getSaleStore(), channelSaleUpdateParam.getChannelSale().getChannelCode());
            channelSaleService.update(channelSaleUpdateParam);
        } catch (Exception e) {
            log.info("异常:{}",JSON.toJSONString(e.getStackTrace()));
            log.info("异常:{}" ,JSON.toJSONString(e.getMessage()));
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }finally {
            redisLockUtil.releaseLock(lockKey);
            if (ObjectUtils.isNotEmpty(channelSaleUpdateParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(channelSaleUpdateParam.getStoreSkuInventoryListKey());
            }
        }
        return success();
    }

    /**
     * 条件查询
     *
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName find
     * @description 条件查询
     * <AUTHOR>
     * @date 2021/4/26 16:52
     */
    @PostMapping("/find")
    @RedisCacheConvertEnable
    public ResultEntity find(@RequestBody ChannelSaleParam channelSaleParam) {
        return success(channelSaleService.find(channelSaleParam));
    }

    /**
     * 渠道开单明细表查询
     *
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 渠道开单明细表查询
     * <AUTHOR>
     * @date 2021/4/26 16:59
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody ChannelSaleParam channelSaleParam) {
        if (StringUtils.isEmpty(channelSaleParam.getSaleCode())) {
            return paramError("渠道开单明细saleCode传参为空，不能查询");
        }
        return success(channelSaleService.findDetail(channelSaleParam));
    }


    /**
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findSkuByChannel
     * @description 查询渠道客户Sku
     * <AUTHOR>
     * @date 2021/4/26 15:42
     */
    @PostMapping("/findSkuByChannel")
    @RedisCacheConvertEnable
    public ResultEntity findSkuByChannel(@RequestBody ChannelSaleParam channelSaleParam) {
        try {
            return success(channelSaleService.findSkuByChannel(channelSaleParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findSkuByPolicy
     * @description 根据政策查询价格
     * <AUTHOR>
     * @date 2021/4/26 16:10
     */
    @PostMapping("/findSkuByPolicy")
    @RedisCacheConvertEnable
    public ResultEntity findSkuByPolicy(@RequestBody ChannelSaleParam channelSaleParam) {
        if (StringUtils.isEmpty(channelSaleParam.getChannelCode())) {
            return paramError("请传入渠道客户编码");
        }
        if (StringUtils.isEmpty(channelSaleParam.getSkuCode())) {
            return paramError("请传入SKU编码");
        }
        try {
            return success(channelSaleService.findSkuByPolicy(channelSaleParam));
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOrderTodo
     * @description 查询待处理渠道订单
     * <AUTHOR>
     * @date 2021/5/7 9:30
     */
    @PostMapping("/findOrderTodo")
    @RedisCacheConvertEnable
    public ResultEntity findOrderTodo(@RequestBody ChannelSaleParam channelSaleParam) {
        if (StringUtils.isBlank(channelSaleParam.getSourceCode()) && StringUtils.isBlank(channelSaleParam.getId())) {
            return paramError("请传入id或sourceCode编码");
        }
        return success(channelSaleService.findOrderTodo(channelSaleParam));
    }

    /**
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findSpecialPriceTodo
     * @description 查询待处理特价申请单详情
     * <AUTHOR>
     * @date 2021/5/11 16:04
     */
    @PostMapping("/findSpecialPriceTodo")
    @RedisCacheConvertEnable
    public ResultEntity findSpecialPriceTodo(@RequestBody ChannelSaleParam channelSaleParam) {
        if (StringUtils.isBlank(channelSaleParam.getSourceCode()) && StringUtils.isBlank(channelSaleParam.getId())) {
            return paramError("请传入id或sourceCode编码");
        }
        return success(channelSaleService.findSpecialPriceTodo(channelSaleParam));
    }


    @PostMapping("/findChannelCodeSO")
    @RedisCacheConvertEnable
    public ResultEntity findChannelCodeSO(@RequestBody String code) {
        if (StringUtils.isBlank(code)) {
            return paramError("渠道客户code");
        }

        return success(channelSaleService.findChannelCodeSO(code));
    }

    /**
     * @param channelSaleParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findShouldUseAmount
     * @description 查询渠道客户可用额度
     * <AUTHOR>
     * @date 2021/9/17 16:01
     */
    @PostMapping("/findShouldUseAmount")
    @RedisCacheConvertEnable
    public ResultEntity findShouldUseAmount(@RequestBody ChannelSaleParam channelSaleParam) {
        if (StringUtils.isEmpty(channelSaleParam.getChannelCode())) {
            return paramError("请传入渠道客户编码");
        }
        if (StringUtils.isEmpty(channelSaleParam.getSaleStore())) {
            return paramError("请传入销售机构编码");
        }
        return success(channelSaleService.findShouldUseAmount(channelSaleParam));
    }
    /**
     * @methodName addImport
     * @description 导入
     * @param file
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2022/2/17 11:27
     */
    @OperationLog(opName = "导入渠道开单")
    @PostMapping("/addImport")
    public ResultEntity addImport(@RequestParam("file") MultipartFile file) {
        if (null == file) {
            return paramError("请选择导入文件");
        }
        try {
            return success(channelSaleService.addImport(file));
        } catch (Exception e) {
            return error(ResultEnum.ERP_SETT_ADD_ERROR, e.getMessage());
        }

    }

    @OperationLog(opName = "渠道开单增加串号")
    @PostMapping("/addBarcodeChannelSale")
    public ResultEntity<String> addBarcodeChannelSale(@RequestBody ChannelSaleParam channelSaleParam) {
        if (StringUtils.isEmpty(channelSaleParam.getId())) {
            return paramError("渠道开单增加串号传入id为空");
        }
        if (CollectionUtils.isEmpty(channelSaleParam.getSkuTandemRecords())) {
            return paramError("请选择串号");
        }
        try {
            channelSaleService.addBarcodeChannelSale(channelSaleParam);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }


    @OperationLog(opName = "导入串号")
    @PostMapping("/addImportBarcode")
    public ResultEntity<String> addImportBarcode(@RequestParam("file") MultipartFile file, String skuTandemRecord) {
        if (null == file) {
            return paramError("请选择导入文件");
        }
        try {
            String s = channelSaleService.addImportBarcode(file, skuTandemRecord);
            if (s.equals("成功")) {
                return success();
            } else {
                return error(ResultEnum.ERP_LEDGER_ADD_ERROR, s);
            }
        } catch (Exception e) {
            return error(ResultEnum.ERP_LEDGER_ADD_ERROR, e.getMessage());
        }
    }
}

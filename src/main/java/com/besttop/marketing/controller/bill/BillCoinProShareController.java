package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillCoinProShareAndDetailParam;
import com.besttop.marketing.model.bill.param.BillCoinProShareParam;
import com.besttop.marketing.service.bill.BillCoinProShareService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 电子币促销券分摊规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@RestController
@RequestMapping("/billCoinProShare")
@Slf4j
public class BillCoinProShareController extends BaseController {

    @Autowired
    private BillCoinProShareService shareService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addShare
     * @description 添加分摊规则
     * <AUTHOR>
     * @date 2020/2/25 10:20
     */
    @PostMapping("/addShare")
    @OperationLog(opName = "添加分摊规则")
    public ResultEntity addShare(@RequestBody @Validated(GroupAdd.class) BillCoinProShareAndDetailParam param) {
        try {
            String msg = shareService.checkParam(param.getCoinProShare(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (shareService.addShare(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增分摊规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增分摊规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增分摊规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateShare
     * @description 编辑分摊规则
     * <AUTHOR>
     * @date 2020/2/25 10:20
     */
    @PostMapping("/updateShare")
    @OperationLog(opName = "编辑分摊规则")
    public ResultEntity updateShare(@RequestBody @Validated(GroupUpdate.class) BillCoinProShareAndDetailParam param) {
        try {
            String msg = shareService.checkParam(param.getCoinProShare(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (shareService.updateShare(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑分摊规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑分摊规则单失败");
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑分摊规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryShare
     * @description 查询分摊规则单
     * <AUTHOR>
     * @date 2020/2/25 14:18
     */
    @PostMapping("/queryShare")
    @RedisCacheConvertEnable
    public ResultEntity queryShare(@RequestBody BillCoinProShareParam param) {
        try {
            return success(shareService.queryShare(param));
        } catch (Exception e) {
            log.error("查询分摊规则单失败", e);
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @methodName deleteShare
     * @description 删除分摊规则
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/25 14:58
     */
    @PostMapping("/deleteShare")
    @OperationLog(opName = "删除分摊规则")
    public ResultEntity deleteShare(@RequestBody BillCoinProShareParam param){
        try {
            if (CollectionUtils.isEmpty(param.getCodes())){
                return paramError("请选择要删除的数据");
            }
            if (shareService.deleteShare(param)){
                return success();
            }
        }catch (Exception e){
            log.error("删除分摊规则单失败",e);
            return error(ResultEnum.ERP_MARK_DEL_ERROR,"删除分摊规则单失败");
        }
        return error(ResultEnum.ERP_MARK_DEL_ERROR,"删除分摊规则单失败");
    }

    /**
     * @methodName auditShare
     * @description 审核分摊规则
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 9:26
     */
    @PostMapping("/auditShare")
    @OperationLog(opName = "审核分摊规则")
    public ResultEntity auditShare(@RequestBody BillCoinProShareParam param){
        try {
            if (StringUtils.isBlank(param.getId())){
                return paramError("请选择要审核的数据");
            }
            shareService.auditShare(param);
        }catch (Exception e){
            log.error("审核分摊规则失败",e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR,e.getMessage());
        }
        return success();
    }

    /**
     * @methodName stopShare
     * @description 终止分摊规则
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 10:38
     */
    @PostMapping("/stopShare")
    @OperationLog(opName = "终止分摊规则")
    public ResultEntity stopShare(@RequestBody BillCoinProShareParam param){
        try {
            if (StringUtils.isBlank(param.getId())){
                return paramError("请选择要终止的数据");
            }
            shareService.stopShare(param);
        }catch (Exception e){
            log.error("终止分摊规则失败",e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR,"终止分摊规则失败");
        }
        return success();
    }

    /**
     * 根据code查询单据详情
     * @param param
     * @return
     */
    @RequestMapping(value = "/findByCode")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody BillCoinProShareParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("请选择需要查询的数据");
        }
        return success(shareService.findByCode(param));
    }

}

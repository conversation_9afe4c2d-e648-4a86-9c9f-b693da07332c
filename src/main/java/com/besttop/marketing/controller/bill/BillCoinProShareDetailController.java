package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.BillCoinProShareDetail;
import com.besttop.marketing.service.bill.BillCoinProShareDetailService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 电子币促销券分摊规则明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@RestController
@RequestMapping("/billCoinProShareDetail")
public class BillCoinProShareDetailController extends BaseController {

    @Autowired
    private BillCoinProShareDetailService detailService;

    /**
     * @methodName findDetailList
     * @description 根据单据号查询明细
     * @param detail
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/26 10:53
     */
    @PostMapping("findDetailList")
    @RedisCacheConvertEnable
    public ResultEntity findDetailList(@RequestBody BillCoinProShareDetail detail){
        if (StringUtils.isBlank(detail.getShareCode())){
            return paramError("请选择单据");
        }
        return success(new PageInfo<>(detailService.findDetailList(detail)));
    }

}

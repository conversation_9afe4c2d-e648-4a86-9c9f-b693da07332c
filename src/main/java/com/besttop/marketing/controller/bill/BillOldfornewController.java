package com.besttop.marketing.controller.bill;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.*;
import com.besttop.marketing.service.bill.BillOldfornewService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * BillOldfornew的路由接口服务
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/billOldfornew")
public class BillOldfornewController extends BaseController {

	/** BillOldfornewService服务 */
	@Autowired
	private BillOldfornewService billOldfornewService;

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName addService
	 * @description 新增服务规则单
	 * <AUTHOR>
	 * @date 2020/3/18 14:10
	 */
	@PostMapping("/addOldfornew")
	@OperationLog(opName = "新增以旧换新规则单")
	public ResultEntity addService(@RequestBody @Validated(GroupAdd.class) BillOldfornewServiceAndDetailParam param) {
		try {
			String msg = billOldfornewService.checkParam(param.getService(), param.getRuleList(), param.getDetailList());
			if (StringUtils.isNotBlank(msg)) {
				return paramError(msg);
			}
			if (billOldfornewService.addService(param)) {
				return success();
			}
		} catch (Exception e) {
			log.error("新增以旧换新规则单失败", e);
			return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增以旧换新规则单失败");
		}
		return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "新增以旧换新规则单失败");
	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName updateService
	 * @description 更新以旧换新规则单
	 * <AUTHOR>
	 * @date 2020/3/18 14:10
	 */
	@PostMapping("/updateOldfornew")
	@OperationLog(opName = "更新以旧换新规则单")
	public ResultEntity updateService(@RequestBody @Validated(GroupUpdate.class) BillOldfornewServiceAndDetailParam param) {
		try {
			String msg = billOldfornewService.checkParam(param.getService(), param.getRuleList(), param.getDetailList());
			if (StringUtils.isNotBlank(msg)) {
				return paramError(msg);
			}
			if (billOldfornewService.updateService(param)) {
				return success();
			}
		} catch (Exception e) {
			log.error("更新以旧换新规则单失败", e);
			return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "更新以旧换新规则单失败");
		}
		return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "更新以旧换新规则单失败");
	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName queryService
	 * @description 查询以旧换新配置
	 * <AUTHOR>
	 * @date 2020/3/18 15:47
	 */
	@PostMapping("/queryOldfornew")
	@RedisCacheConvertEnable
	public ResultEntity queryService(@RequestBody BillOldfornewParam param) {
		return success(new PageInfo<>(billOldfornewService.queryService(param)));
	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName deleteService
	 * @description 删除以旧换新规则单
	 * <AUTHOR>
	 * @date 2020/3/18 16:27
	 */
	@PostMapping("/deleteOldfornew")
	@OperationLog(opName = "删除以旧换新规则单")
	public ResultEntity deleteService(@RequestBody BillOldfornewParam param) {
		try {
			if (CollectionUtils.isEmpty(param.getCodes())) {
				return paramError("请选择要删除的数据");
			}
			if (billOldfornewService.deleteService(param)) {
				return success();
			}
		} catch (Exception e) {
			log.error("删除以旧换新规则单失败", e);
			return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除以旧换新规则单失败");
		}
		return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除以旧换新规则单失败");
	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName deleteService
	 * @description 审核以旧换新规则单
	 * <AUTHOR>
	 * @date 2020/3/18 16:27
	 */
	@PostMapping("/auditOldfornew")
	@OperationLog(opName = "审核以旧换新规则单")
	public ResultEntity auditService(@RequestBody BillOldfornewParam param) {
		try {
			if (StringUtils.isBlank(param.getId())) {
				return paramError("请选择要审核的数据");
			}
			if (billOldfornewService.auditService(param)) {
				return success();
			}
		} catch (Exception e) {
			log.error("审核以旧换新规则单失败", e);
			return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
		}
		return error(ResultEnum.ERP_MARK_AUDIT_ERROR, "审核以旧换新规则单失败");
	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName deleteService
	 * @description 终止以旧换新规则单
	 * <AUTHOR>
	 * @date 2020/3/18 16:27
	 */
	@PostMapping("/stopOldfornew")
	@OperationLog(opName = "终止以旧换新规则单")
	public ResultEntity stopService(@RequestBody BillOldfornewParam param) {
		try {
			if (StringUtils.isBlank(param.getId())) {
				return paramError("请选择要终止的数据");
			}
			if (billOldfornewService.stopService(param)) {
				return success();
			}
		} catch (Exception e) {
			log.error("终止以旧换新规则单失败", e);
			return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
		}
		return error(ResultEnum.ERP_MARK_AUDIT_ERROR, "终止以旧换新规则单失败");
	}

	/**
	 * @methodName query
	 * @description 查询导购开票中的以旧换新
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/19 17:34
	 */
	@PostMapping("/query")
	@RedisCacheConvertEnable
	public ResultEntity query(@RequestBody @Validated(GroupQuery.class) BillScheduleParam param) {
		return success(billOldfornewService.query(param));
	}


	/**
	 * @methodName query
	 * @description 查询导购开票中的以旧换新
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/19 17:34
	 */
	@PostMapping("/findDetail")
	@RedisCacheConvertEnable
	public ResultEntity findDetail(@RequestBody @Validated(GroupQuery.class) BillOldfornewParam param) {
		return success(billOldfornewService.findDetail(param));
	}

	/**
	 * @methodName queryOldfornew
	 * @description 导购开票查询以旧换新
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2021/2/24 13:57
	 */
    @PostMapping("/selectOldfornew")
    @RedisCacheConvertEnable
	public ResultEntity selectOldfornew(@RequestBody BillOldfornewParam param){
        return success(billOldfornewService.queryOldfornew(param));
    }
}

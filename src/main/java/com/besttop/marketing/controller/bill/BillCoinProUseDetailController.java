package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.param.BillCoinProUseDetailParam;
import com.besttop.marketing.service.bill.BillCoinProUseDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 电子币促销券使用规则明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@RestController
@RequestMapping("/billCoinProUseDetail")
public class BillCoinProUseDetailController extends BaseController {

    @Autowired
    private BillCoinProUseDetailService detailService;

    @RequestMapping(value = "/findList")
    @RedisCacheConvertEnable
    public ResultEntity findList(@RequestBody BillCoinProUseDetailParam param) {
        if(StringUtils.isBlank(param.getType())){
            return paramError("请选择查询的类型");
        }
        return success(detailService.findList(param));
    }

}

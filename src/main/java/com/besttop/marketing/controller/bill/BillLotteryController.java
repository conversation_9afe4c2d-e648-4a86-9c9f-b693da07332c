package com.besttop.marketing.controller.bill;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillLotteryParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillLotteryService;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;

/**
 * BillLottery的路由接口服务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/billLottery")
public class BillLotteryController extends BaseController {

	/** BillLotteryService服务 */
	@Autowired
	private BillLotteryService billLotteryService;
	@Autowired
	private LoginCacheUtil loginCacheUtil;

	/**
	 * 新增
	 *
	 * @methodName add
	 * @description 新增
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/17 10:21
	 */
	@RequestMapping(value = "/add")
	@OperationLog(opName = "新增")
	public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillLotteryParam param) {
		if (param.getStartTime().after(DateUtils.addDays(param.getEndTime(), 0))) {
			return paramError("传入开始时间在结束时间之后，请重新选择");
		}

		Integer count = this.billLotteryService.queryEeffectRule(param);
		if (count > 0) {
			return paramError("开始时间或结束时间, 不允许与已存在生效抽奖规则有重叠 !");
		}

		String message = billLotteryService.add(param);
		if (StringUtils.isBlank(message)) {
			return error(ResultEnum.ERP_MARK_ADD_ERROR);
		} else if ("成功".equalsIgnoreCase(message)) {
			return success();
		} else {
			return paramError(message);
		}
	}

	/**
	 * 编辑
	 *
	 * @methodName update
	 * @description 编辑
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/16 16:01
	 */
	@RequestMapping(value = "/update")
	@OperationLog(opName = "编辑")
	public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) BillLotteryParam param) {
		if (StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())) {
			return paramError("请选择需要修改的数据");
		}
		if (param.getStartTime().after(DateUtils.addDays(param.getEndTime(), 0))) {
			return paramError("传入开始时间在结束时间之后，请重新选择");
		}
		
		Integer count = this.billLotteryService.queryEeffectRule(param);
		if (count > 0) {
			return paramError("开始时间或结束时间, 不允许与已存在生效抽奖规则有重叠 !");
		}
		
		String message = billLotteryService.update(param);
		if (StringUtils.isBlank(message)) {
			return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
		} else if ("成功".equalsIgnoreCase(message)) {
			return success();
		} else {
			return paramError(message);
		}
	}

	/**
	 * 删除
	 *
	 * @methodName del
	 * @description 删除
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/16 16:11
	 */
	@RequestMapping(value = "/del")
	@OperationLog(opName = "删除")
	public ResultEntity del(@RequestBody BillLotteryParam param) {
		if (StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())) {
			return paramError("请选择需要删除的数据");
		}
		String message = billLotteryService.del(param);
		if (StringUtils.isBlank(message)) {
			return error(ResultEnum.ERP_MARK_DEL_ERROR);
		} else if ("成功".equalsIgnoreCase(message)) {
			return success();
		} else {
			return paramError(message);
		}
	}

	/**
	 * 终止
	 *
	 * @methodName terminal
	 * @description 终止
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/16 16:18
	 */
	@RequestMapping(value = "/terminal")
	@OperationLog(opName = "终止")
	public ResultEntity terminal(@RequestBody BillLotteryParam param) {
		if (StringUtils.isBlank(param.getId())) {
			return paramError("请选择需要终止的数据");
		}
		param.setStatus(CommonEnums.BILL_LOTTERY_STATUS_C.getCode());
		param.setCancelBy(loginCacheUtil.getUserCode());
		param.setCancelTime(new Date());
		if (billLotteryService.updateById(param)) {
			return success();
		}
		return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
	}

	/**
	 * 模糊查询列表
	 *
	 * @methodName findBySelected
	 * @description 模糊查询列表
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/16 16:19
	 */
	@RequestMapping(value = "/findBySelected")
	@RedisCacheConvertEnable
	public ResultEntity findBySelected(@RequestBody BillLotteryParam param) {
		return success(new PageInfo<>(billLotteryService.findBySelected(param)));
	}

	/**
	 * 根据编码查询详情
	 *
	 * @methodName findByCode
	 * @description 根据编码查询详情
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2020/3/16 17:55
	 */
	@RequestMapping(value = "/findByCode")
	@RedisCacheConvertEnable
	public ResultEntity findByCode(@RequestBody BillLotteryParam param) {
		if (StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())) {
			return paramError("请选择需要查看的数据");
		}
		return success(billLotteryService.findByCode(param));
	}

}

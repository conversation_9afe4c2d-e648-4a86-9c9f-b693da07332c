package com.besttop.marketing.controller.bill;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.BillCouponDefine;
import com.besttop.marketing.model.bill.param.BillCouponDefineBParam;
import com.besttop.marketing.model.bill.param.BillCouponDefineParam;
import com.besttop.marketing.model.coupon.param.CouponBalanceParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillCouponDefineService;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 优惠券定义主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
@RestController
@RequestMapping("/billCouponDefine")
public class BillCouponDefineController extends BaseController {

    @Autowired
    private BillCouponDefineService billCouponDefineService;
    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * 新增优惠券定义
     *
     * @methodName add
     * @description 新增优惠券定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/10 16:36
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增优惠券定义")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillCouponDefineParam param) {
        String message=billCouponDefineService.add(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }
    
    /**
     * 新增b账户商品设置
     *
     * @methodName addB
     * @description 新增b账户商品设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/11 17:40
     */
    @RequestMapping(value = "/addB")
    @OperationLog(opName = "新增b账户商品设置")
    public ResultEntity addB(@RequestBody @Validated(GroupAdd.class) BillCouponDefineBParam param) {
        String message=billCouponDefineService.addB(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 修改用户券定义
     *
     * @methodName update
     * @description 修改用户券定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/10 16:37
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "修改用户券定义")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) BillCouponDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要修改的数据");
        }
        String message=billCouponDefineService.update(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 编辑B账户的商品设置
     *
     * @methodName updateB
     * @description 编辑B账户的商品设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/11 18:08
     */
    @RequestMapping(value = "/updateB")
    @OperationLog(opName = "编辑B账户的商品设置")
    public ResultEntity updateB(@RequestBody @Validated(GroupUpdate.class) BillCouponDefineBParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要修改的数据");
        }
        String message=billCouponDefineService.updateB(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 删除优惠券定义
     *
     * @methodName del
     * @description 删除优惠券定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/10 16:52
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除优惠券定义")
    public ResultEntity del(@RequestBody BillCouponDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要删除的数据");
        }
        String message=billCouponDefineService.del(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 删除b账户设置
     *
     * @methodName delB
     * @description 删除b账户设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/12 14:01
     */
    @RequestMapping(value = "/delB")
    @OperationLog(opName = "删除b账户设置")
    public ResultEntity delB(@RequestBody BillCouponDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要删除的数据");
        }
        String message=billCouponDefineService.delB(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 模糊查询列表
     *
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/10 17:27
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody BillCouponDefineParam param) {
        return success(new PageInfo<>(billCouponDefineService.findBySelected(param)));
    }

    /**
     * 查询详情
     *
     * @methodName findByCode
     * @description 查询详情
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/10 17:55
     */
    @RequestMapping(value = "/findByCode")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody BillCouponDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要查看的数据");
        }
        return success(billCouponDefineService.findByCode(param));
    }

    /**
     * 查询B账户商品设置详情
     *
     * @methodName findByCode
     * @description 查询B账户商品设置详情
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/12 14:03
     */
    @RequestMapping(value = "/findById")
    @RedisCacheConvertEnable
    public ResultEntity findById(@RequestBody BillCouponDefineBParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要查看的数据");
        }
        return success(billCouponDefineService.findBByCode(param));
    }


    /**
     * @methodName terminal
     * @description 终止
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/11 16:13
     */
    @RequestMapping(value = "/terminal")
    @OperationLog(opName = "终止优惠券")
    public ResultEntity terminal(@RequestBody BillCouponDefineParam param) {
        if(StringUtils.isBlank(param.getId())){
            return paramError("请选择需要终止的数据");
        }
        BillCouponDefine define=billCouponDefineService.getById(param.getId());
        if (null == define) {
            return  paramError("单据已经被删除");
        }
        if(!CommonEnums.COUPON_DEFINE_AUDIT.getCode().equals(define.getStatus())){
            return paramError("单据状态已经变更，请刷新重试");
        }
        param.setStatus(CommonEnums.COUPON_DEFINE_STOP.getCode());
        param.setCancelBy(loginCacheUtil.getUserCode());
        param.setCancelTime(new Date());
        if(billCouponDefineService.updateById(param)){
            return success();
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
    }

    /**
     * @methodName query
     * @description 导购开票查询厂家券/商场券
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/18 11:28
     */
    @RequestMapping(value = "/query")
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class) CouponBalanceParam param) {
        CouponBalanceParam result=billCouponDefineService.query(param);
        if(null==result){
            return success("没有可用余额");
        }
        String message=result.getMessage();
        if(StringUtils.isNotBlank(message)){
            return paramError(message);
        }
        return success(result);
    }
}

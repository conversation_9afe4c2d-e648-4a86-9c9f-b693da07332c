package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.BillScheduleService;
import com.besttop.marketing.service.bill.BillScheduleServiceRuleService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 商品服务规则单规则设置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@RestController
@RequestMapping("/billScheduleServiceRule")
public class BillScheduleServiceRuleController extends BaseController {

    @Autowired
    private BillScheduleServiceRuleService ruleService;

    /**
     * @methodName findDetail
     * @description 查询明细
     * @param service
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/19 15:11
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody BillScheduleService service){
        return success(new PageInfo<>(ruleService.findDetail(service)));
    }
}

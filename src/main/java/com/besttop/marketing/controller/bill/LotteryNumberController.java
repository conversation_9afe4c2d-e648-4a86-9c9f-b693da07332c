package com.besttop.marketing.controller.bill;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.LotteryNumber;
import com.besttop.marketing.model.bill.param.BillLotteryParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.LotteryNumberService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;


/**
 * LotteryNumber的路由接口服务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lotteryNumber")
public class LotteryNumberController extends BaseController {

	/** LotteryNumberService服务 */
	@Autowired
	private LotteryNumberService lotteryNumberService;

	/**
     * @methodName terminal
     * @description TODO
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2021/2/27 14:27
	 */
    @RequestMapping(value = "/updateNoLottery")
    @OperationLog(opName = "未中奖")
    public ResultEntity updateNoLottery(@RequestBody LotteryNumber param) {
        if (param.getIds().isEmpty()) {
            return paramError("请选择需要修改的数据");
        }
        if (lotteryNumberService.updateNoLottery(param)) {
            return success();
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
    }

    @RequestMapping(value = "/updateLottery")
    @OperationLog(opName = "已中奖")
    public ResultEntity updateLottery(@RequestBody LotteryNumber param) {
        if (param.getIds().isEmpty()) {
            return paramError("请选择需要修改的数据");
        }
        if (lotteryNumberService.updateLottery(param)) {
            return success();
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
    }

	/**
     * 模糊查询列表
     *
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:19
	 */
    @RequestMapping(value = "/find")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody LotteryNumber param) {
        return success(new PageInfo<>(lotteryNumberService.find(param)));
	}

}

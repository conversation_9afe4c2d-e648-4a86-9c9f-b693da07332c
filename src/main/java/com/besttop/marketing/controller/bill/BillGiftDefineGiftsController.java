package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.BillGiftDefine;
import com.besttop.marketing.service.bill.BillGiftDefineGiftsService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 礼品规则单礼品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@RestController
@RequestMapping("/billGiftDefineGifts")
public class BillGiftDefineGiftsController extends BaseController {

    @Autowired
    private BillGiftDefineGiftsService detailService;

    /**
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 查询明细
     * <AUTHOR>
     * @date 2020/3/17 18:12
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody BillGiftDefine define) {
        return success(new PageInfo<>(detailService.findDetail(define)));
    }

}

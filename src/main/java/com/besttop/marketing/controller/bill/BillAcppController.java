package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillAcppParam;
import com.besttop.marketing.model.bill.result.BillAcppResult;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillAcppService;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 延保服务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@RestController
@RequestMapping("/billAcpp")
public class BillAcppController extends BaseController {

    @Autowired
    private BillAcppService billAcppService;
    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * 新增延保服务
     *
     * @methodName add
     * @description 新增延保服务
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/17 10:21
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增延保服务")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillAcppParam param) {
        String message=billAcppService.add(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }
    /**
     * 编辑延保服务
     *
     * @methodName update
     * @description 编辑延保服务
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:01
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑延保服务")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) BillAcppParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要修改的数据");
        }
        String message=billAcppService.update(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 删除延保服务
     *
     * @methodName del
     * @description 删除延保服务
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:11
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除延保服务")
    public ResultEntity del(@RequestBody BillAcppParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要删除的数据");
        }
        String message=billAcppService.del(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 终止延保服务
     *
     * @methodName terminal
     * @description 终止延保服务
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:18
     */
    @RequestMapping(value = "/terminal")
    @OperationLog(opName = "终止延保服务")
    public ResultEntity terminal(@RequestBody BillAcppParam param) {
        if(StringUtils.isBlank(param.getId())){
            return paramError("请选择需要终止的数据");
        }
        param.setStatus(CommonEnums.ACPP_STOP.getCode());
        param.setCancelBy(loginCacheUtil.getUserCode());
        param.setCancelTime(new Date());
        if(billAcppService.updateById(param)){
            return success();
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
    }

    /**
     * 模糊查询列表
     *
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:19
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody BillAcppParam param) {
        return success(new PageInfo<>(billAcppService.findBySelected(param)));
    }

    /**
     * 根据编码查询详情
     *
     * @methodName findByCode
     * @description 根据编码查询详情
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 17:55
     */
    @RequestMapping(value = "/findByCode")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody BillAcppParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要查看的数据");
        }
        return success(billAcppService.findByCode(param));
    }


    /**
     * @methodName query
     * @description 导购开票查询延保服务
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/23 11:19
     */
    @RequestMapping(value = "/query")
    @RedisCacheConvertEnable
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class) BillAcppResult param) {
        return success(billAcppService.query(param));
    }
}

package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillPromotionPayParam;
import com.besttop.marketing.service.bill.BillPromotionPayService;
import com.besttop.marketing.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 券销售记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@RestController
@RequestMapping("/billPromotionPay")
public class BillPromotionPayController extends BaseController {

    @Autowired
    private BillPromotionPayService billPromotionPayService;


    /***
     * 新增券销售
     * @methodName add
     * @description
     * @params [param]
     * @return ResultEntity
     * <AUTHOR>
     * @date 2020/8/31 16:57
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增券销售单")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillPromotionPayParam param) {
        if (CollectionUtil.isEmpty(param.getBillPromotionPayDetailList())) {
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, "参数错误");
        }
        try {
            return billPromotionPayService.add(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * 支付核验
     */
    @RequestMapping(value = "/verification")
    @OperationLog(opName = "支付核验")
    public ResultEntity verification(@RequestBody @Validated(GroupAdd.class) BillPromotionPayParam param) {
        try {
            return billPromotionPayService.verification(param);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultEntity(ResultEnum.ERP_PURC_QUERY_ERROR, e.getMessage());
        }
    }

}

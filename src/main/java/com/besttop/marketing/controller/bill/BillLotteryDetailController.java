package com.besttop.marketing.controller.bill;

import com.besttop.common.controller.BaseController;
import com.besttop.marketing.model.bill.BillLotteryDetail;
import com.besttop.marketing.service.bill.BillLotteryDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * BillLotteryDetail的路由接口服务
 * 
 * <AUTHOR>
 */
@RestController
public class BillLotteryDetailController extends BaseController {

	/** BillLotteryDetailService服务 */
	@Autowired
	private BillLotteryDetailService billLotteryDetailService;
	
	/**
	 * 查询所有BillLotteryDetail数据的方法
	 * @param value
	 * @return
	 */
	@GetMapping(value = "/BillLotteryDetail", produces = {"application/json;charset=UTF-8"})
	public String find(BillLotteryDetail value) {
		return billLotteryDetailService.find(value);
	}
	
	/**
	 * 通过id查询BillLotteryDetail数据的方法
	 * @param id
	 * @return
	 */
	@GetMapping(value = "/BillLotteryDetail/{id}", produces = {"application/json;charset=UTF-8"})
	public String findOne(@PathVariable(name="id") String id) {
		return billLotteryDetailService.findOne(id);
	}
	
	/**
	 * 插入BillLotteryDetail属性不为空的数据方法
	 * @param id
	 * @return
	 */
	@PostMapping(value = "/BillLotteryDetail", produces = {"application/json;charset=UTF-8"})
	public String save(BillLotteryDetail value) {
		return billLotteryDetailService.saveNotNull(value);
	}
	
	/**
	 * 更新BillLotteryDetail属性不为空的数据方法
	 * @param id
	 * @return
	 */
	@PutMapping(value = "/BillLotteryDetail", produces = {"application/json;charset=UTF-8"})
	public String update(BillLotteryDetail value) {
		return billLotteryDetailService.updateNotNullById(value);
	}

	/**
	 * 通过id删除BillLotteryDetail数据方法
	 * @param id
	 * @return
	 */
	@DeleteMapping(value = "/BillLotteryDetail/{id}", produces = {"application/json;charset=UTF-8"})
	public String delete(@PathVariable(name="id") String id) {
		return billLotteryDetailService.deleteById(id);
	}
}

package com.besttop.marketing.controller.bill;


import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.exception.CommonException;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.param.BigMemberVendorCouponParam;
import com.besttop.marketing.model.bill.param.BillOldfornewParam;
import com.besttop.marketing.model.bill.param.BillServiceIssuanceDetailParam;
import com.besttop.marketing.service.bill.BillServiceIssuanceDetailService;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 服务券发放表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@RestController
@RequestMapping("/billServiceIssuanceDetail")
@Slf4j
public class BillServiceIssuanceDetailController extends BaseController {

    @Autowired
    private BillServiceIssuanceDetailService billServiceIssuanceDetailService;

    @PostMapping("/query")
    @RedisCacheConvertEnable
    public ResultEntity queryService(@RequestBody BillServiceIssuanceDetailParam param) {
        return success(new PageInfo<>(billServiceIssuanceDetailService.queryService(param)));
    }

    @PostMapping("/queryDetail")
    @RedisCacheConvertEnable
    public ResultEntity queryDetail(@RequestBody BillServiceIssuanceDetailParam param) {
        return success(new PageInfo<>(billServiceIssuanceDetailService.queryDetail(param)));
    }

    @PostMapping("/giveCard")
    @RedisCacheConvertEnable
    public ResultEntity giveCard(@RequestBody BillServiceIssuanceDetailParam param) {
        return success((billServiceIssuanceDetailService.giveCard(param)));
    }

    @PostMapping("/cancelCard")
    @RedisCacheConvertEnable
    public ResultEntity cancelCard(@RequestBody BillServiceIssuanceDetailParam param) {
        return success((billServiceIssuanceDetailService.cancelCard(param)));
    }

}

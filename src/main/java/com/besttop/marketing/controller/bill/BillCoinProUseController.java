package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.BillCoinProUse;
import com.besttop.marketing.model.bill.param.BillCoinProUseParam;
import com.besttop.marketing.model.bill.param.CoinProUseParam;
import com.besttop.marketing.model.bill.param.CoinProUseQueryParam;
import com.besttop.marketing.model.bill.CouponRequest;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillCoinProUseService;
import com.besttop.marketing.util.CollectionUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 电子币促销券使用规则定义主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@Slf4j
@RestController
@RequestMapping("/billCoinProUse")
public class BillCoinProUseController extends BaseController {

    @Autowired
    private BillCoinProUseService billCoinProUseService;

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName add
     * @description 添加电子币促销券使用规则定义
     * <AUTHOR>
     * @date 2020/2/24 15:36
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "添加电子币促销券使用规则定义")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillCoinProUseParam param) {
        String message = billCoinProUseService.add(param);
        if (StringUtils.isBlank(message)) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        } else if ("成功".equalsIgnoreCase(message)) {
            return success();
        } else {
            return paramError(message);
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName update
     * @description 编辑电子币促销券使用规则定义
     * <AUTHOR>
     * @date 2020/2/24 16:56
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑电子币促销券使用规则定义")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) BillCoinProUseParam param) {
        try {
            if (StringUtils.isBlank(param.getCode()) || StringUtils.isBlank(param.getId())) {
                return paramError("请选择需要修改的数据");
            }
            String message = billCoinProUseService.update(param);
            if (StringUtils.isBlank(message)) {
                return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
            } else if ("成功".equalsIgnoreCase(message)) {
                return success();
            } else {
                return paramError(message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error(ResultEnum.ERP_PURC_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName del
     * @description 删除电子币促销券使用规则定义
     * <AUTHOR>
     * @date 2020/2/24 17:35
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除电子币促销券使用规则定义")
    public ResultEntity del(@RequestBody BillCoinProUseParam param) {
        try {
            if (CollectionUtil.isEmpty(param.getCodes())) {
                return paramError("请选择需要删除的数据");
            }
            if (billCoinProUseService.del(param) > 0) {
                return success();
            }
            return error(ResultEnum.ERP_PURC_DEL_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return error(ResultEnum.ERP_PURC_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName teminal
     * @description 终止
     * <AUTHOR>
     * @date 2020/2/25 10:32
     */
    @RequestMapping(value = "/teminal")
    @OperationLog(opName = "终止电子币促销券使用规则定义")
    public ResultEntity teminal(@RequestBody BillCoinProUseParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择需要终止的数据");
            }
            BillCoinProUse byId = billCoinProUseService.getById(param.getId());
            if (null == byId) {
                return paramError("单据已经被删除");
            }
            if (!byId.getStatus().equals(CommonEnums.BILL_COIN_RULE_DEFINE_AUDIT.getCode())) {
                return paramError("单据状态" + CommonEnums.getValue(byId.getStatus()) + "，请刷新重试");
            }
            //终止
            param.setCancelTime(new Date());
            param.setCancelBy(loginCacheUtil.getUserCode());
            param.setStatus(CommonEnums.BILL_COIN_RULE_DEFINE_STOP.getCode());
            if (billCoinProUseService.updateById(param)) {
                return success();
            }
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            return error(ResultEnum.ERP_PURC_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBySelected
     * @description 模糊查询
     * <AUTHOR>
     * @date 2020/2/25 12:46
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody BillCoinProUseParam param) {
        return success(new PageInfo<>(billCoinProUseService.findBySelected(param)));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findByCode
     * @description 查询单条数据
     * <AUTHOR>
     * @date 2020/2/25 17:01
     */
    @RequestMapping(value = "/findByCode")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody BillCoinProUseParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("请选择需要查询的数据");
        }
        return success(billCoinProUseService.findByCode(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName query
     * @description 查询导购开票中的导购促销券
     * <AUTHOR>
     * @date 2020/3/19 10:42
     */
    @RequestMapping(value = "/query")
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class) CoinProUseParam param) {
        return success(billCoinProUseService.query(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName query
     * @description 查询导购开票中的导顾客促销券
     * <AUTHOR>
     * @date 2020/3/19 10:42
     */
    @RequestMapping(value = "/queryCoupon")
    public ResultEntity queryCoupon(@RequestBody @Validated(GroupQuery.class) CoinProUseParam param) {
        return success(billCoinProUseService.queryCoupon(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName query
     * @description 查询导购开票中的顾客内购券
     * <AUTHOR>
     * @date 2021/10/19 10:42
     */
    @RequestMapping(value = "/queryInHoseCoupon")
    public ResultEntity queryInHoseCoupon(@RequestBody @Validated(GroupQuery.class) CoinProUseQueryParam param) {
        return success(billCoinProUseService.queryInHoseCoupon(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName query
     * @description 查询顾客促销券-多点
     * <AUTHOR>
     * @date 2021/10/19 10:42
     */
    @RequestMapping(value = "/querydmallCoupon")
    public ResultEntity querydmallCoupon(@RequestBody @Validated(GroupQuery.class) CouponRequest param) {
        return success(billCoinProUseService.querydmallCoupon(param));
    }
}

package com.besttop.marketing.controller.bill;

import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillPromotionSaleParam;
import com.besttop.marketing.model.bill.param.BillPromotionSalePayParam;
import com.besttop.marketing.model.bill.result.BillPromotionSaleResult;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.thirdparty.param.RefundmentParams;
import com.besttop.marketing.service.bill.BillPromotionSaleService;
import com.besttop.marketing.util.NumberUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 促销券销售表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@Slf4j
@RestController
@RequestMapping("/billPromotionSale")
public class BillPromotionSaleController extends BaseController {

    @Autowired
    private BillPromotionSaleService saleService;

    /**
     * 促销券销售支付
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName payPromotionSale
     * @description 促销券销售支付
     * <AUTHOR>
     * @date 2020/3/3 17:45
     */
    @PostMapping("/payPromotionSale")
    @OperationLog(opName = "促销券销售支付")
    public ResultEntity payPromotionSale(@RequestBody @Validated(GroupAdd.class) BillPromotionSalePayParam param) {
        try {
            String code = param.getCode();
            if (StringUtils.isEmpty(code)) {
                String payN = System.currentTimeMillis() + NumberUtils.get3Random();
                param.setPayNumber(payN);
            }
            String msg = saleService.checkParam(param, CommonConstant.PAY);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            return success(saleService.payPromotionSale(param));
        } catch (Exception e) {
            log.error("促销券销售支付失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * 添加保存收款接口
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName paySave
     * @description 添加保存收款接口
     * <AUTHOR>
     * @date 2020/3/12 14:58
     */
    @PostMapping("/paySave")
    @OperationLog(opName = "促销券销售保存收款接口")
    public ResultEntity paySave(@RequestBody BillPromotionSalePayParam param) {
        String code = param.getCode();
        if (StringUtils.isEmpty(code)) {
            String payN = System.currentTimeMillis() + NumberUtils.get3Random();
            param.setPayNumber(payN);
        }
        String msgPay = saleService.checkParam(param, CommonConstant.PAY_SAVE);
        if (StringUtils.isNotBlank(msgPay)) {
            return paramError(msgPay);
        }
        return success(saleService.paySave(param));
    }

    /**
     * 查询销售订单
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPromotionOrder
     * @description 查询销售订单
     * <AUTHOR>
     * @date 2020/3/12 17:27
     */
    @PostMapping("/queryPromotionOrder")
    @RedisCacheConvertEnable
    public ResultEntity<List<BillPromotionSaleResult>> queryPromotionOrder(@RequestBody BillPromotionSaleParam param) {
        return success(new PageInfo<>(saleService.queryPromotionOrder(param)));
    }

    /**
     * 促销券销售退款
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundPromotionSale
     * @description 促销券销售退款
     * <AUTHOR>
     * @date 2020/3/13 14:19
     */
    @PostMapping("/refundPromotionSale")
    @OperationLog(opName = "促销券销售退款")
    public ResultEntity refundPromotionSale(@RequestBody RefundmentParams param) {
        try {
            String msg = saleService.checkRefundParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            return success(saleService.refundPromotionSale(param));
        } catch (Exception e) {
            log.error("退款失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "退款失败");
        }
    }

    /**
     * 保存退款
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName refundSave
     * @description 保存退款
     * <AUTHOR>
     * @date 2020/3/16 15:30
     */
    @PostMapping("/refundSave")
    @OperationLog(opName = "促销券销售保存退款")
    public ResultEntity refundSave(@RequestBody RefundmentParams param) {
        try {
            String msg = saleService.checkRefundParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            saleService.refundSave(param);
        } catch (Exception e) {
            log.error("保存退款失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "保存退款失败");
        }
        return success();
    }

    /**
     * @methodName queryPromotionSaleRecord
     * @description 查询退款记录
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/2 17:56
     */
    @PostMapping("/queryPromotionSaleRecord")
    public ResultEntity queryPromotionSaleRecord(@RequestBody BillPromotionSalePayParam param){
        if (StringUtils.isBlank(param.getCode())){
            return paramError("单号不能为空");
        }
        return success(saleService.queryPromotionSaleRecord(param));
    }

}

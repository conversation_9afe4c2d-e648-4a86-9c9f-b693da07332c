package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillScheduleLimitParam;
import com.besttop.marketing.model.bill.param.LimitParam;
import com.besttop.marketing.model.bill.param.ScheduleLimitParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillScheduleLimitService;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 限量限价销售设置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@RestController
@RequestMapping("/billScheduleLimit")
public class BillScheduleLimitController extends BaseController {

    @Autowired
    private BillScheduleLimitService limitService;
    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * 添加限量限价设置
     *
     * @methodName add
     * @description 添加限量限价设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 15:35
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "添加限量限价设置")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillScheduleLimitParam param) {
        String message=limitService.add(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }


    /**
     * 编辑限量限价设置
     *
     * @methodName update
     * @description 编辑限量限价设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:01
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑限量限价设置")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) BillScheduleLimitParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要修改的数据");
        }
        String message=limitService.update(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 删除限量限价设置
     *
     * @methodName del
     * @description 删除限量限价设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:11
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除限量限价设置")
    public ResultEntity del(@RequestBody BillScheduleLimitParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要删除的数据");
        }
        String message=limitService.del(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 删除限量限价设置
     *
     * @methodName terminal
     * @description 终止限量限价设置
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:18
     */
    @RequestMapping(value = "/terminal")
    @OperationLog(opName = "终止限量限价设置")
    public ResultEntity terminal(@RequestBody BillScheduleLimitParam param) {
        if(StringUtils.isBlank(param.getId())){
            return paramError("请选择需要终止的数据");
        }
        param.setStatus(CommonEnums.SCHEDULE_LIMIT_STOP.getCode());
        param.setCancelBy(loginCacheUtil.getUserCode());
        param.setCancelTime(new Date());
        if(limitService.updateById(param)){
            return success();
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
    }

    /**
     * 模糊查询列表
     *
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 16:19
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody ScheduleLimitParam param) {
        return success(new PageInfo<>(limitService.findBySelected(param)));
    }

    /**
     * 根据编码查询详情
     *
     * @methodName findByCode
     * @description 根据编码查询详情
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 17:55
     */
    @RequestMapping(value = "/findByCode")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody BillScheduleLimitParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要查看的数据");
        }
        return success(limitService.findByCode(param));
    }

    /**
     * @methodName query
     * @description 查询导购开票的限时抢购
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/23 10:21
     */
    @RequestMapping(value = "/query")
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class) LimitParam param) {
        return success(limitService.query(param));
    }

}

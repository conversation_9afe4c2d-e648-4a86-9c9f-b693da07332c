package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillScheduleParam;
import com.besttop.marketing.model.bill.param.BillScheduleServiceAndDetailParam;
import com.besttop.marketing.model.bill.param.BillScheduleServiceParam;
import com.besttop.marketing.service.bill.BillScheduleServiceService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 商品服务规则单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Slf4j
@RestController
@RequestMapping("/billScheduleService")
public class BillScheduleServiceController extends BaseController {

    @Autowired
    private BillScheduleServiceService scheduleServiceService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addService
     * @description 新增服务规则单
     * <AUTHOR>
     * @date 2020/3/18 14:10
     */
    @PostMapping("/addService")
    @OperationLog(opName = "新增服务规则单")
    public ResultEntity addService(@RequestBody @Validated(GroupAdd.class) BillScheduleServiceAndDetailParam param) {
        try {
            String msg = scheduleServiceService.checkParam(param.getService(), param.getRuleList(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (scheduleServiceService.addService(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增服务规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增服务规则单失败");
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "新增服务规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateService
     * @description 更新服务规则单
     * <AUTHOR>
     * @date 2020/3/18 14:10
     */
    @PostMapping("/updateService")
    @OperationLog(opName = "更新服务规则单")
    public ResultEntity updateService(@RequestBody @Validated(GroupUpdate.class) BillScheduleServiceAndDetailParam param) {
        try {
            String msg = scheduleServiceService.checkParam(param.getService(), param.getRuleList(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (scheduleServiceService.updateService(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("更新服务规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "更新服务规则单失败");
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "更新服务规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryService
     * @description 查询服务配置
     * <AUTHOR>
     * @date 2020/3/18 15:47
     */
    @PostMapping("/queryService")
    @RedisCacheConvertEnable
    public ResultEntity queryService(@RequestBody BillScheduleServiceParam param) {
        return success(new PageInfo<>(scheduleServiceService.queryService(param)));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteService
     * @description 删除服务规则单
     * <AUTHOR>
     * @date 2020/3/18 16:27
     */
    @PostMapping("/deleteService")
    @OperationLog(opName = "删除服务规则单")
    public ResultEntity deleteService(@RequestBody BillScheduleServiceParam param) {
        try {
            if (CollectionUtils.isEmpty(param.getCodes())) {
                return paramError("请选择要删除的数据");
            }
            if (scheduleServiceService.deleteService(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("删除服务规则单失败", e);
            return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除服务规则单失败");
        }
        return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除服务规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteService
     * @description 审核服务规则单
     * <AUTHOR>
     * @date 2020/3/18 16:27
     */
    @PostMapping("/auditService")
    @OperationLog(opName = "审核服务规则单")
    public ResultEntity auditService(@RequestBody BillScheduleServiceParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择要审核的数据");
            }
            if (scheduleServiceService.auditService(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("审核服务规则单失败", e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_AUDIT_ERROR, "审核服务规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteService
     * @description 终止服务规则单
     * <AUTHOR>
     * @date 2020/3/18 16:27
     */
    @PostMapping("/stopService")
    @OperationLog(opName = "终止服务规则单")
    public ResultEntity stopService(@RequestBody BillScheduleServiceParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择要终止的数据");
            }
            if (scheduleServiceService.stopService(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("终止服务规则单失败", e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_AUDIT_ERROR, "终止服务规则单失败");
    }

    /**
     * @methodName query
     * @description 查询导购开票中的赠服务
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/19 17:34
     */
    @PostMapping("/query")
    @RedisCacheConvertEnable
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class) BillScheduleParam param) {
        return success(scheduleServiceService.query(param));
    }

}

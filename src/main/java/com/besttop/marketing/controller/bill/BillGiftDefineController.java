package com.besttop.marketing.controller.bill;

import cn.hutool.json.JSONObject;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillGiftDefineAndDetailParam;
import com.besttop.marketing.model.bill.param.BillGiftDefineParam;
import com.besttop.marketing.model.bill.result.GiftExclusiveOptionResult;
import com.besttop.marketing.service.bill.BillGiftDefineService;
import com.github.pagehelper.PageInfo;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 礼品规则单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Slf4j
@RestController
@RequestMapping("/billGiftDefine")
public class BillGiftDefineController extends BaseController {

    @Autowired
    private BillGiftDefineService giftDefineService;

    /**
     * @param id
     * @return com.besttop.common.model.ResultEntity
     * @methodName copyGift
     * @description 复制礼品规则单
     * <AUTHOR>
     * @date 2021/6/17 13:59
     */
    @PostMapping("/copyGift")
    @OperationLog(opName = "复制礼品规则单")
    public ResultEntity copyGift(@RequestBody String id) {
        try {
            String temp = giftDefineService.copyGift(new JSONObject(id).get("id").toString());
            if (temp.split("-").length == 1) {
                return error(ResultEnum.ERP_MARK_ADD_ERROR, temp);
            } else {
                return success(temp, "Copy Success");
            }
        } catch (Exception e) {
            log.error("复制礼品规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "复制礼品规则单失败");
        }
    }


    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addGift
     * @description 新增礼品规则单
     * <AUTHOR>
     * @date 2020/3/17 14:05
     */
    @PostMapping("/addGift")
    @OperationLog(opName = "新增礼品规则单")
    public ResultEntity addGift(@RequestBody @Validated(GroupAdd.class) BillGiftDefineAndDetailParam param) {
        try {
            String msg = giftDefineService.checkParam(param.getGiftDefine(), param.getDetailList(), param.getGiftsList(),true);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (giftDefineService.addGift(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增礼品规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增礼品规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增礼品规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateGift
     * @description 更新礼品规则单
     * <AUTHOR>
     * @date 2020/3/17 14:05
     */
    @PostMapping("/updateGift")
    @OperationLog(opName = "更新礼品规则单")
    public ResultEntity updateGift(@RequestBody @Validated(GroupUpdate.class) BillGiftDefineAndDetailParam param) {
        try {
            String msg = giftDefineService.checkParam(param.getGiftDefine(), param.getDetailList(), param.getGiftsList(),false);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (giftDefineService.updateGift(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("更新礼品规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "更新礼品规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryGift
     * @description 查询档期规则
     * <AUTHOR>
     * @date 2020/3/17 17:18
     */
    @PostMapping("/queryGift")
    @RedisCacheConvertEnable
    public ResultEntity queryGift(@RequestBody BillGiftDefineParam param) {
        return success(new PageInfo<>(giftDefineService.queryGift(param)));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteGift
     * @description 删除礼品规则单
     * <AUTHOR>
     * @date 2020/3/17 17:18
     */
    @PostMapping("/deleteGift")
    @OperationLog(opName = "删除礼品规则单")
    public ResultEntity deleteGift(@RequestBody BillGiftDefineParam param) {
        try {
            if (CollectionUtils.isEmpty(param.getCodes())) {
                return paramError("请选择要删除的单据");
            }
            if (giftDefineService.deleteGift(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("删除礼品规则单失败", e);
            return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除礼品规则单失败");
        }
        return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除礼品规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditGift
     * @description 审核礼品规则单
     * <AUTHOR>
     * @date 2020/3/18 10:03
     */
    @PostMapping("/auditGift")
    @OperationLog(opName = "审核礼品规则单")
    public ResultEntity auditGift(@RequestBody BillGiftDefineParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择要审核的数据");
            }
            if (giftDefineService.auditGift(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("审核礼品规则单失败", e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_AUDIT_ERROR, "审核礼品规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName stopGift
     * @description 终止礼品规则单
     * <AUTHOR>
     * @date 2020/3/18 10:03
     */
    @PostMapping("/stopGift")
    @OperationLog(opName = "终止礼品规则单")
    public ResultEntity stopGift(@RequestBody BillGiftDefineParam param) {
        try {
            if (StringUtils.isBlank(param.getId())) {
                return paramError("请选择要终止的数据");
            }
            if (giftDefineService.stopGift(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("终止礼品规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "终止礼品规则单失败");
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "终止礼品规则单失败");
    }


    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryGifts
     * @description TODO 礼品发放不管联销售单礼品
     * <AUTHOR>
     * @date 2020/11/20 17:09
     */
    @PostMapping("/queryGifts")
    public ResultEntity queryGifts(@RequestBody BillGiftDefineParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请选择顾客");
        }
        //return success(new PageInfo<>(giftDefineService.queryGifts(param)));
        return success(giftDefineService.queryGifts(param));
    }
    
    @PostMapping("/queryGiftExclusiveOptions")
    public ResultEntity queryGiftExclusiveOptions() {
        return success(giftDefineService.queryGiftExclusiveOptions());
    }
}

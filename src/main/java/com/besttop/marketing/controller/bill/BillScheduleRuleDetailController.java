package com.besttop.marketing.controller.bill;


import cn.hutool.json.JSONObject;
import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.BillScheduleRuleDetail;
import com.besttop.marketing.model.bill.result.BillCoinProUseResult;
import com.besttop.marketing.service.bill.BillScheduleRuleDetailService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 档期规则定义单详情表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
@RestController
@RequestMapping("/billScheduleRuleDetail")
public class BillScheduleRuleDetailController extends BaseController {

    @Autowired
    private BillScheduleRuleDetailService detailService;

    /**
     * @description: 通过单品满返或者套购满返，返回电子币规则对象
     * @param: [code]
     * @return: com.besttop.common.model.ResultEntity
     * @author: Luxi
     * @date: 2021/6/25
     */
    @PostMapping("/resObject")
    @RedisCacheConvertEnable
    public ResultEntity resObject(@RequestBody String code) {
        String param = new JSONObject(code).get("code").toString();
        System.out.println();
        String msg = detailService.checkParam(param);
        if (StringUtils.isBlank(msg)) {
            return success(detailService.resObject(param));
        } else {
            return paramError(msg);
        }
    }


    @PostMapping("/findDetailList")
    @RedisCacheConvertEnable
    public ResultEntity findDetailList(@RequestBody BillScheduleRuleDetail detail) {
        if (StringUtils.isBlank(detail.getRuleDefineCode())) {
            return paramError("请选择单据");
        }
        return success(new PageInfo<>(detailService.findDetailList(detail)));
    }

}

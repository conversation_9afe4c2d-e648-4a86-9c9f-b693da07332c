package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.BillScheduleRuleGifts;
import com.besttop.marketing.service.bill.BillScheduleRuleGiftsService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 档期活动满赠明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-18
 */
@RestController
@RequestMapping("/billScheduleRuleGifts")
public class BillScheduleRuleGiftsController extends BaseController {
    @Autowired
    private BillScheduleRuleGiftsService ruleGiftsService;

    /**
     * @methodName findDetailList
     * @description 查询赠品明细接口
     * @param gifts
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/2/18 14:14
     */
    @PostMapping("/findDetailList")
    @RedisCacheConvertEnable
    public ResultEntity findDetailList(@RequestBody BillScheduleRuleGifts gifts){
        if(StringUtils.isBlank(gifts.getScheduleRuleCode())){
            return paramError("请选择单据");
        }
        return success(new PageInfo<>(ruleGiftsService.findDetailList(gifts)));
    }
}

package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupQuery;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillScheduleParam;
import com.besttop.marketing.model.bill.param.BillScheduleRuleAndDetailParam;
import com.besttop.marketing.model.bill.param.BillScheduleRuleParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillScheduleRuleService;
import com.besttop.marketing.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 档期规则定义表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
@Slf4j
@RestController
@RequestMapping("/billScheduleRule")
public class BillScheduleRuleController extends BaseController {

    @Autowired
    private BillScheduleRuleService ruleService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addSingleReturn
     * @description 新增单品满返规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addSingleReturn")
    @OperationLog(opName = "新增单品满返规则单")
    public ResultEntity addSingleReturn(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_RETURN.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增单品满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满返规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditSingleReturn
     * @description 新增审核单品满返规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addAuditSingleReturn")
    @OperationLog(opName = "新增审核单品满返规则单")
    public ResultEntity addAuditSingleReturn(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_RETURN.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核单品满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核单品满返规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核单品满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName querySingleReturn
     * @description 查询单品满返规则单
     * <AUTHOR>
     * @date 2020/2/12 13:57
     */
    @PostMapping("/querySingleReturn")
    @RedisCacheConvertEnable
    public ResultEntity querySingleReturn(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_RETURN.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateSingleReturn
     * @description 更新单品满返规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateSingleReturn")
    @OperationLog(opName = "更新单品满返规则单")
    public ResultEntity updateSingleReturn(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            long t1 = System.currentTimeMillis();
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            log.info("===[BillScheduleRuleController updateSingleReturn] time1:{}",System.currentTimeMillis()-t1);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            long t2 = System.currentTimeMillis();
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
            log.info("===[BillScheduleRuleController updateSingleReturn] time2:{}",System.currentTimeMillis()-t2);
        } catch (Exception e) {
            log.error("编辑单品满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑单品满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAudit
     * @description 编辑审核单品满返规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateAuditSingleReturn")
    @OperationLog(opName = "编辑审核单品满返规则单")
    public ResultEntity updateAuditSingleReturn(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核单品满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑审核单品满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteScheduleRule
     * @description 删除档期规则单（满返，满减，满免）
     * <AUTHOR>
     * @date 2020/2/13 9:34
     */
    @PostMapping("/deleteScheduleRule")
    @OperationLog(opName = "删除档期规则单（满返，满减，满免）")
    public ResultEntity deleteScheduleRule(@RequestBody BillScheduleRuleParam param) {
        if (CollectionUtils.isEmpty(param.getCodes())) {
            return paramError("请选择要删除的数据");
        }
        try {
            ruleService.deleteScheduleRule(param);
        } catch (Exception e) {
            log.error("删除档期规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "删除档期规则单失败");
        }
        return success();
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditScheduleRule
     * @description 审核档期规则单(满减 ， 满返 ， 满免)
     * <AUTHOR>
     * @date 2020/2/13 10:58
     */
    @PostMapping("/auditScheduleRule")
    @OperationLog(opName = "审核档期规则单(满减 ， 满返 ， 满免)")
    public ResultEntity auditScheduleRule(@RequestBody BillScheduleRuleParam param) {
        if (StringUtils.isEmpty(param.getId())) {
            return paramError("请选择要审核的数据");
        }
        try {
            ruleService.auditScheduleRule(param);
        } catch (Exception e) {
            log.error("审核档期规则单失败", e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName terminalScheduleRule
     * @description 终止档期规则单
     * <AUTHOR>
     * @date 2020/2/13 10:58
     */
    @PostMapping("/terminalScheduleRule")
    @OperationLog(opName = "终止档期规则单")
    public ResultEntity terminalScheduleRule(@RequestBody BillScheduleRuleParam param) {
        if (StringUtils.isEmpty(param.getId())) {
            return paramError("请选择要终止的数据");
        }
        try {
            ruleService.terminalScheduleRule(param);
        } catch (Exception e) {
            log.error("终止档期规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addSingleReduce
     * @description 新增单品满减规则单
     * <AUTHOR>
     * @date 2020/2/14 9:40
     */
    @PostMapping("/addSingleReduce")
    @OperationLog(opName = "新增单品满减规则单")
    public ResultEntity addSingleReduce(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_REDUCE.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增单品满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满减规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditSingleReduce
     * @description 新增审核单品满减规则单
     * <AUTHOR>
     * @date 2020/2/14 9:40
     */
    @PostMapping("/addAuditSingleReduce")
    @OperationLog(opName = "新增审核单品满减规则单")
    public ResultEntity addAuditSingleReduce(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_REDUCE.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增单品满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满减规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateSingleReduce
     * @description 编辑单品满减规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateSingleReduce")
    @OperationLog(opName = "编辑单品满减规则单")
    public ResultEntity updateSingleReduce(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑单品满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑单品满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditSingleReduce
     * @description 编辑审核单品满减规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateAuditSingleReduce")
    @OperationLog(opName = "编辑审核单品满减规则单")
    public ResultEntity updateAuditSingleReduce(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核单品满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑审核单品满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName querySingleReduce
     * @description 查询单品满减规则单
     * <AUTHOR>
     * @date 2020/2/12 13:57
     */
    @PostMapping("/querySingleReduce")
    @RedisCacheConvertEnable
    public ResultEntity querySingleReduce(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_REDUCE.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName querySingleGifts
     * @description 查询单品满赠规则单
     * <AUTHOR>
     * @date 2020/2/17 13:57
     */
    @PostMapping("/querySingleGifts")
    @RedisCacheConvertEnable
    public ResultEntity querySingleGifts(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_GIFTS.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addSingleGifts
     * @description 添加单品满赠
     * <AUTHOR>
     * @date 2020/2/18 9:54
     */
    @PostMapping("/addSingleGifts")
    @OperationLog(opName = "添加单品满赠")
    public ResultEntity addSingleGifts(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_GIFTS.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增单品满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增单品满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditSingleGifts
     * @description 新增审核单品满赠
     * <AUTHOR>
     * @date 2020/2/18 9:54
     */
    @PostMapping("/addAuditSingleGifts")
    @OperationLog(opName = "新增审核单品满赠")
    public ResultEntity addAuditSingleGifts(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_SINGLE_GIFTS.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核单品满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核单品满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核单品满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateSingleGifts
     * @description 编辑单品满赠
     * <AUTHOR>
     * @date 2020/2/18 16:28
     */
    @PostMapping("/updateSingleGifts")
    @OperationLog(opName = "编辑单品满赠")
    public ResultEntity updateSingleGifts(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑单品满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑单品满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑单品满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditSingleGifts
     * @description 编辑审核单品满赠
     * <AUTHOR>
     * @date 2020/2/18 16:28
     */
    @PostMapping("/updateAuditSingleGifts")
    @OperationLog(opName = "编辑审核单品满赠")
    public ResultEntity updateAuditSingleGifts(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核单品满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑审核单品满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑审核单品满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteScheduleRule
     * @description 删除档期规则单（满赠）
     * <AUTHOR>
     * @date 2020/2/18 14:34
     */
    @PostMapping("/deleteScheduleRuleGifts")
    @OperationLog(opName = "删除档期规则单（满赠）")
    public ResultEntity deleteScheduleRuleGifts(@RequestBody BillScheduleRuleParam param) {
        if (CollectionUtils.isEmpty(param.getCodes())) {
            return paramError("请选择要删除的数据");
        }
        try {
            ruleService.deleteScheduleRuleGifts(param);
        } catch (Exception e) {
            log.error("删除档期规则单失败", e);
            return paramError(e.getMessage());
        }
        return success();
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditScheduleRuleGifts
     * @description 审核档期规则单(满赠)
     * <AUTHOR>
     * @date 2020/2/13 10:58
     */
    @PostMapping("/auditScheduleRuleGifts")
    @OperationLog(opName = "审核档期规则单(满赠)")
    public ResultEntity auditScheduleRuleGifts(@RequestBody BillScheduleRuleParam param) {
        if (StringUtils.isEmpty(param.getId())) {
            return paramError("请选择要审核的数据");
        }
        try {
            ruleService.auditScheduleRuleGifts(param);
        } catch (Exception e) {
            log.error("审核档期规则单失败", e);
            return error(ResultEnum.ERP_MARK_AUDIT_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addArbitrageReturn
     * @description 新增套购满返规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addArbitrageReturn")
    @OperationLog(opName = "新增套购满返规则单")
    public ResultEntity addArbitrageReturn(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_RETURN.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增套购满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增套购满返规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增套购满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditArbitrageReturn
     * @description 新增审核套购满返规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addAuditArbitrageReturn")
    @OperationLog(opName = "新增审核套购满返规则单")
    public ResultEntity addAuditArbitrageReturn(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_RETURN.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核套购满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核套购满返规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核套购满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateArbitrageReturn
     * @description 编辑套购满返规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateArbitrageReturn")
    @OperationLog(opName = "编辑套购满返规则单")
    public ResultEntity updateArbitrageReturn(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑套购满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑套购满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditArbitrageReturn
     * @description 编辑审核套购满返规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateAuditArbitrageReturn")
    @OperationLog(opName = "编辑审核套购满返规则单")
    public ResultEntity updateAuditArbitrageReturn(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核套购满返规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑审核套购满返规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryArbitrageReturn
     * @description 查询套购满返规则单
     * <AUTHOR>
     * @date 2020/2/12 13:57
     */
    @PostMapping("/queryArbitrageReturn")
    @RedisCacheConvertEnable
    public ResultEntity queryArbitrageReturn(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_RETURN.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addArbitrageReduce
     * @description 新增套购满减规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addArbitrageReduce")
    @OperationLog(opName = "新增套购满减规则单")
    public ResultEntity addArbitrageReduce(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_REDUCE.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增套购满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增套购满减规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增套购满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditArbitrageReduce
     * @description 新增审核套购满减规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addAuditArbitrageReduce")
    @OperationLog(opName = "新增审核套购满减规则单")
    public ResultEntity addAuditArbitrageReduce(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_REDUCE.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核套购满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核套购满减规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核套购满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateArbitrageReduce
     * @description 编辑套购满减规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateArbitrageReduce")
    @OperationLog(opName = "编辑套购满减规则单")
    public ResultEntity updateArbitrageReduce(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑套购满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑套购满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditArbitrageReduce
     * @description 编辑审核套购满减规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateAuditArbitrageReduce")
    @OperationLog(opName = "编辑审核套购满减规则单")
    public ResultEntity updateAuditArbitrageReduce(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核套购满减规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑审核套购满减规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryArbitrageReturn
     * @description 查询套购满减规则单
     * <AUTHOR>
     * @date 2020/2/12 13:57
     */
    @PostMapping("/queryArbitrageReduce")
    @RedisCacheConvertEnable
    public ResultEntity queryArbitrageReduce(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_REDUCE.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addFree
     * @description 新增满免规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addFree")
    @OperationLog(opName = "新增满免规则单")
    public ResultEntity addFree(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_FREE.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增满免规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增满免规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增满免规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditFree
     * @description 新增审核满免规则单
     * <AUTHOR>
     * @date 2020/2/12 10:03
     */
    @PostMapping("/addAuditFree")
    @OperationLog(opName = "新增审核满免规则单")
    public ResultEntity addAuditFree(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_FREE.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核满免规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核满免规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核满免规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateFree
     * @description 编辑满免规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateFree")
    @OperationLog(opName = "编辑满免规则单")
    public ResultEntity updateFree(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑满免规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑满免规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditFree
     * @description 编辑审核满免规则单
     * <AUTHOR>
     * @date 2020/2/12 17:38
     */
    @PostMapping("/updateAuditFree")
    @OperationLog(opName = "编辑审核满免规则单")
    public ResultEntity updateAuditFree(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRule(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核满免规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑审核满免规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryFree
     * @description 查询满免规则单
     * <AUTHOR>
     * @date 2020/2/12 13:57
     */
    @PostMapping("/queryFree")
    @RedisCacheConvertEnable
    public ResultEntity queryFree(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_FREE.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryArbitrageGifts
     * @description 查询套购满赠规则单
     * <AUTHOR>
     * @date 2020/2/17 13:57
     */
    @PostMapping("/queryArbitrageGifts")
    @RedisCacheConvertEnable
    public ResultEntity queryArbitrageGifts(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.queryScheduleRule(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_GIFTS.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addArbitrageGifts
     * @description 添加套购满赠
     * <AUTHOR>
     * @date 2020/2/18 9:54
     */
    @PostMapping("/addArbitrageGifts")
    @OperationLog(opName = "添加套购满赠")
    public ResultEntity addArbitrageGifts(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_GIFTS.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增套购满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增套购满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增套购满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditArbitrageGifts
     * @description 新增审核套购满赠
     * <AUTHOR>
     * @date 2020/2/18 9:54
     */
    @PostMapping("/addAuditArbitrageGifts")
    @OperationLog(opName = "新增审核套购满赠")
    public ResultEntity addAuditArbitrageGifts(@RequestBody @Validated(GroupAdd.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode(),
                    CommonEnums.SCHEDULE_RULE_TYPE_ARBITRAGE_GIFTS.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核套购满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核套购满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核套购满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateArbitrageGifts
     * @description 编辑套购满赠
     * <AUTHOR>
     * @date 2020/2/18 16:28
     */
    @PostMapping("/updateArbitrageGifts")
    @OperationLog(opName = "编辑套购满赠")
    public ResultEntity updateArbitrageGifts(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_UNAUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑套购满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑套购满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑套购满赠规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditSingleGifts
     * @description 编辑审核套购满赠
     * <AUTHOR>
     * @date 2020/2/18 16:28
     */
    @PostMapping("/updateAuditArbitrageGifts")
    @OperationLog(opName = "编辑审核套购满赠")
    public ResultEntity updateAuditArbitrageGifts(@RequestBody @Validated(GroupUpdate.class) BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGiftsParam(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.updateScheduleRuleGifts(param, CommonEnums.SCHEDULE_RULE_STATUS_AUDIT.getCode())) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑审核套购满赠规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑审核套购满赠规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑审核套购满赠规则单失败");
    }

    /**
     * @methodName findBySelected
     * @description 查询导购开票中用到的单品满减/满返
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/13 16:51
     */
    @PostMapping("/query")
    @RedisCacheConvertEnable
    public ResultEntity query(@RequestBody @Validated(GroupQuery.class)BillScheduleParam param) {
        return success(ruleService.findBySelected(param));
    }

    /**
     * @methodName queryGiveSku
     * @description 查询单品满赠商品列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/16 10:43
     */
    @PostMapping("/queryGiveSku")
    @RedisCacheConvertEnable
    public ResultEntity queryGiveSku(@RequestBody @Validated(GroupQuery.class)BillScheduleParam param) {
        return success(ruleService.queryGiveSku(param));
    }

    /**
     * @methodName queryArbitrage
     * @description 查询导购开票中的所有套购活动
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/8 10:10
     */
    @PostMapping("/queryArbitrage")
    @RedisCacheConvertEnable
    public ResultEntity queryArbitrage(@RequestBody BillScheduleParam param) {
        if(CollectionUtil.isEmpty(param.getOrderCodes())){
            return paramError("请选择票据");
        }
        return success(ruleService.queryArbitrage(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addAuditGuideFreeCoin
     * @description 新增审核导购赠币规则单
     * <AUTHOR>
     * @date 2021/3/29 11:20
     */
    @PostMapping("/addAuditGuideFreeCoin")
    @OperationLog(opName = "新增审核导购赠币规则单")
    public ResultEntity addAuditGuideFreeCoin(@RequestBody BillScheduleRuleAndDetailParam param) {
        try {
            String msg = ruleService.checkGuideFreeCoinParam(param.getBillScheduleRule(), param.getDetailList());
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            if (ruleService.addAuditGuideFreeCoin(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("新增审核导购赠币规则单失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核导购赠币规则单失败");
        }
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增审核导购赠币规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateAuditGuideFreeCoin
     * @description 编辑审核导购赠币规则单
     * <AUTHOR>
     * @date 2021/3/29 11:20
     */
    @PostMapping("/updateAuditGuideFreeCoin")
    @OperationLog(opName = "编辑审核导购赠币规则单")
    public ResultEntity updateAuditGuideFreeCoin(@RequestBody BillScheduleRuleAndDetailParam param) {
        try {

            if (ruleService.updateAuditGuideFreeCoin(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("编辑导购赠币规则单失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "编辑导购赠币规则单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findGuideFreeCoin
     * @description 查询导购赠币规则单
     * <AUTHOR>
     * @date 2021/3/29 11:21
     */
    @PostMapping("/findGuideFreeCoin")
    @RedisCacheConvertEnable
    public ResultEntity findGuideFreeCoin(@RequestBody BillScheduleRuleParam param) {
        try {
            return success(ruleService.findGuideFreeCoin(param,
                    CommonEnums.SCHEDULE_RULE_TYPE_GUIDE_FREE_COIN.getCode()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }


    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findGuideFreeCoin
     * @description 查询导购开票可增的电子币
     * <AUTHOR>
     * @date 2021/3/29 11:21
     */
    @PostMapping("/queryGuideCoin")
    @RedisCacheConvertEnable
    public ResultEntity queryGuideCoin(@RequestBody BillScheduleParam param) {
        try {
            return success(ruleService.queryGuideCoin(param));
        } catch (Exception e) {
            log.error(e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }
}

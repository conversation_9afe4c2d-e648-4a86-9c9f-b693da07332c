package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.bill.param.BillGroupDefineParam;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.bill.BillGroupDefineService;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 捆绑商品定义单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-24
 */
@RestController
@RequestMapping("/billGroupDefine")
public class BillGroupDefineController extends BaseController {

    @Autowired
    private BillGroupDefineService billGroupDefineService;
    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * 新增捆绑商品定义
     *
     * @methodName add
     * @description 新增捆绑商品定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/24 15:25
     */
    @RequestMapping(value = "/add")
    @OperationLog(opName = "新增捆绑商品定义")
    public ResultEntity add(@RequestBody @Validated(GroupAdd.class) BillGroupDefineParam param) {
        String message=billGroupDefineService.add(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_ADD_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 编辑捆绑商品定义
     *
     * @methodName update
     * @description 编辑捆绑商品定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/24 15:25
     */
    @RequestMapping(value = "/update")
    @OperationLog(opName = "编辑捆绑商品定义")
    public ResultEntity update(@RequestBody @Validated(GroupUpdate.class) BillGroupDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要修改的数据");
        }
        String message=billGroupDefineService.update(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 删除捆绑商品定义
     *
     * @methodName del
     * @description 删除捆绑商品定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/24 15:40
     */
    @RequestMapping(value = "/del")
    @OperationLog(opName = "删除捆绑商品定义")
    public ResultEntity del(@RequestBody BillGroupDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要删除的数据");
        }
        String message=billGroupDefineService.del(param);
        if(StringUtils.isBlank(message)){
            return error(ResultEnum.ERP_MARK_DEL_ERROR);
        }else if("成功".equalsIgnoreCase(message)){
            return success();
        }else{
            return paramError(message);
        }
    }

    /**
     * 终止捆绑商品定义
     *
     * @methodName terminal
     * @description 终止捆绑商品定义
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/24 15:45
     */
    @RequestMapping(value = "/terminal")
    @OperationLog(opName = "终止捆绑商品定义")
    public ResultEntity terminal(@RequestBody BillGroupDefineParam param) {
        if(StringUtils.isBlank(param.getId())){
            return paramError("请选择需要终止的数据");
        }
        param.setStatus(CommonEnums.GROUP_DEFINE_STOP.getCode());
        param.setCancelBy(loginCacheUtil.getUserCode());
        param.setCancelTime(new Date());
        if(billGroupDefineService.updateById(param)){
            return success();
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR);
    }

    /**
     * 模糊查询列表
     *
     * @methodName findBySelected
     * @description 模糊查询列表
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/24 15:50
     */
    @RequestMapping(value = "/findBySelected")
    @RedisCacheConvertEnable
    public ResultEntity findBySelected(@RequestBody BillGroupDefineParam param) {
        return success(new PageInfo<>(billGroupDefineService.findBySelected(param)));
    }

    /**
     * 查询详情
     *
     * @methodName findByCode
     * @description 查询详情
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/3/24 15:48
     */
    @RequestMapping(value = "/findByCode")
    @RedisCacheConvertEnable
    public ResultEntity findByCode(@RequestBody BillGroupDefineParam param) {
        if(StringUtils.isBlank(param.getId()) || StringUtils.isBlank(param.getCode())){
            return paramError("请选择需要查看的数据");
        }
        return success(billGroupDefineService.findByCode(param));
    }

    /**
     * @methodName queryGroups
     * @description 根据主商品编码查询捆绑商品==导购开票
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/4/7 10:44
     */
    @PostMapping("/queryGroups")
    @RedisCacheConvertEnable
    public ResultEntity queryGroups(@RequestBody BillGroupDefineParam param){
        if (StringUtils.isBlank(param.getSkuCode())){
            return paramError("主商品编码不能为空");
        }
        return success(billGroupDefineService.queryGroups(param));
    }

}

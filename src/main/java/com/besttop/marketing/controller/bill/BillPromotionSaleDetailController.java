package com.besttop.marketing.controller.bill;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.bill.BillPromotionSaleDetail;
import com.besttop.marketing.service.bill.BillPromotionSaleDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 促销券销售明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@RestController
@RequestMapping("/billPromotionSaleDetail")
public class BillPromotionSaleDetailController extends BaseController {

    @Autowired
    private BillPromotionSaleDetailService detailService;

    /**
     * @param detail
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryDetail
     * @description 查询已销售促销券明细
     * <AUTHOR>
     * @date 2020/3/12 16:15
     */
    @PostMapping("/queryDetail")
    @RedisCacheConvertEnable
    public ResultEntity queryDetail(@RequestBody BillPromotionSaleDetail detail) {
        if (StringUtils.isBlank(detail.getSaleCode())){
            return paramError("销售单号不能为空");
        }
        return success(detailService.queryDetail(detail));
    }

}

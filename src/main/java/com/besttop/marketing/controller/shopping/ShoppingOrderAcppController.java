package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderAcpp;
import com.besttop.marketing.service.shopping.ShoppingOrderAcppService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 销售延保服务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@RestController
@RequestMapping("/shoppingOrderAcpp")
public class ShoppingOrderAcppController extends BaseController {

    @Autowired
    private ShoppingOrderAcppService shoppingOrderAcppService;

    /**
     * @param shoppingOrderAcpp
     * @return com.besttop.common.model.ResultEntity
     * @methodName addBinding
     * @description 绑定延保
     * <AUTHOR>
     * @date 2021/7/6 16:03
     */
    @OperationLog(opName = "绑定延保")
    @PostMapping("/addBinding")
    public ResultEntity addBinding(@RequestBody ShoppingOrderAcpp shoppingOrderAcpp) {

        if (StringUtils.isNotEmpty(shoppingOrderAcpp.getId())) {
            return paramError("绑定延保传入id,不能新增");
        }
        if (StringUtils.isEmpty(shoppingOrderAcpp.getOrderAcppCode())) {
            return paramError("请选择延保单据");
        }
        if (StringUtils.isEmpty(shoppingOrderAcpp.getOrderCode())) {
            return paramError("请选择被延保商品单据");
        }
        try {
            shoppingOrderAcppService.addBinding(shoppingOrderAcpp);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
        return success();
    }


    /**
     * @param shoppingOrderAcpp
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * @methodName updateCancelBinding
     * @description 取消绑定延保
     * <AUTHOR>
     * @date 2021/7/6 16:03
     */
    @OperationLog(opName = "取消绑定延保")
    @PostMapping("/updateCancelBinding")
    public ResultEntity<String> updateCancelBinding(@RequestBody ShoppingOrderAcpp shoppingOrderAcpp) {
        if (StringUtils.isNotEmpty(shoppingOrderAcpp.getId())) {
            return paramError("取消绑定延保传入id,不能新增");
        }
        if (StringUtils.isEmpty(shoppingOrderAcpp.getOrderAcppCode())) {
            return paramError("请选择取消延保单据");
        }
        try {
            shoppingOrderAcppService.updateCancelBinding(shoppingOrderAcpp);
        } catch (Exception e) {
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        }
        return success();
    }

    /**
     * @param shoppingOrder
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBinding
     * @description 查询延保可绑定列表
     * <AUTHOR>
     * @date 2021/7/6 16:03
     */
    @PostMapping("/findBinding")
    @RedisCacheConvertEnable
    public ResultEntity findBinding(@RequestBody ShoppingOrder shoppingOrder) {
        if (StringUtils.isEmpty(shoppingOrder.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(shoppingOrderAcppService.findBinding(shoppingOrder));
    }
}

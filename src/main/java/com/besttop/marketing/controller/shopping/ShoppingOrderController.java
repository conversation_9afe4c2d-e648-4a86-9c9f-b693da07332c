package com.besttop.marketing.controller.shopping;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderPromotion;
import com.besttop.marketing.model.shopping.param.*;
import com.besttop.marketing.model.shopping.result.OrderPromotionRecoilResult;
import com.besttop.marketing.model.shopping.result.OrderRecoilResult;
import com.besttop.marketing.model.shopping.result.PayOrderResults;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import com.besttop.redis.model.RedisMultiLockObject;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 销售订单主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingOrder")
public class ShoppingOrderController extends BaseController {

    @Autowired
    private ShoppingOrderService orderService;
    
    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private RedisLockUtil redisLockUtil;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOutType
     * @description 查询出库方式
     * <AUTHOR>
     * @date 2020/3/23 15:09
     */
    @PostMapping("/queryOutType")
    public ResultEntity queryOutType(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getType())) {
            return paramError("type不能为空");
        }
        if (null == param.getIsStock()) {
            return paramError("是否管库存标志不能为空");
        }
        return success(orderService.queryOutType(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPickCode
     * @description 查询提货位置
     * <AUTHOR>
     * @date 2020/3/23 16:52
     */
    @PostMapping("/queryPickCode")
    public ResultEntity queryPickCode(@RequestBody OrderQueryParam param) {
        return success(orderService.queryPickCode(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addOrder
     * @description 销售开票
     * <AUTHOR>
     * @date 2020/3/24 11:04
     */
    @PostMapping("/addOrder")
    @OperationLog(opName = "销售开票")
    public synchronized ResultEntity addOrder(@RequestBody @Validated(GroupAdd.class) OrderParam param) {
        RedisMultiLockObject lockObject = null;
        try {
            // 批量锁定
            if(StringUtils.isNotBlank(param.getOrder().getSourceCode())){
                log.info("addOrder获取缓存锁");
                lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getOrder().getSourceCode()));
                if (null == lockObject) {
                    log.info("addOrder获取缓存锁失败");
                    return paramError("有转销的单据，请扫后操作");
                }
            }
            log.info("---{orderService.checkParam(param)}");
            ResultEntity resultEntity = orderService.checkParam(param);
            if (resultEntity.getFlag() != 1) {
                return resultEntity;
            }
            Map<String, String> map = orderService.addOrder(param);
            String msg = map.get("msg") == null ? "开单成功!" : "开单成功! " + map.get("msg");
            return success(map.get("maxCode"), msg);
        } catch (Exception e) {
            log.error("销售开票失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }finally {
            if(StringUtils.isNotBlank(param.getOrder().getSourceCode())){
                redisLockUtil.releaseBatchLock(lockObject);
            }
            if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
            }
        }
    }
    
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addSpecialPriceOrder
     * @description 销售开票(特价申请单)
     * <AUTHOR>
     * @date 2021/04/23 16:40
     */
    @PostMapping("/addSpecialPriceOrder")
    @OperationLog(opName = "销售开票(特价申请单)")
    public ResultEntity addSpecialPriceOrder(@RequestBody @Validated(GroupAdd.class) OrderParam param) {
    	try {
            log.info("====addSpecialPriceOrder.checkAddSpecialPriceOrderParam(param)");
            ResultEntity check = this.orderService.checkAddSpecialPriceOrderParam(param);
        	if(null != check) {
        		return check;
        	}
        	
        	log.info("====addSpecialPriceOrder.checkParam(param)");
            ResultEntity resultEntity = orderService.checkParam(param);
            if (resultEntity.getFlag() != 1) {
                return resultEntity;
            }
            
            Map<String, String> map = orderService.addOrder(param);
            String msg = map.get("msg") == null ? "开单成功!" : "开单成功! " + map.get("msg");
            return success(map.get("maxCode"), msg);
        } catch (Exception e) {
            log.error("销售开票失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }finally {
            if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
            }
        }
    }
    
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName querySpecialPriceOrders
     * @description 查询特价申请单列表
     * <AUTHOR>
     * @date 2021/04/26 16:09
     */
    @PostMapping("/querySpecialPriceOrders")
    @RedisCacheConvertEnable
    public ResultEntity querySpecialPriceOrders(@RequestBody SpecialPriceOrderParam param) {
    	if(null == param.getRows() || param.getRows() == 0) {
    		param.setRows(30);
    	}
    	if(null == param.getPage() || param.getPage() == 0) {
    		param.setPage(1);
    	}
    	if(null == param.getIsPurchase() || param.getIsPurchase() == 0) {
    		param.setIsPurchase(2);
    	}
        return orderService.querySpecialPriceOrders(param);
    }
    
    
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName querySpecialPriceOrdersMobile
     * @description 查询特价申请单列表(移动端)
     * <AUTHOR>
     * @date 2021/04/26 16:09
     */
    @PostMapping("/querySpecialPriceOrdersMobile")
    @RedisCacheConvertEnable
    public ResultEntity querySpecialPriceOrdersMobile(@RequestBody SpecialPriceOrderParam param) {
    	if(null == param.getRows() || param.getRows() == 0) {
    		param.setRows(10);
    	}
    	if(null == param.getPage() || param.getPage() == 0) {
    		param.setPage(1);
    	}
        return this.querySpecialPriceOrders(param);
    }
    
    
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName querySpecialPriceOrdersMobile
     * @description 特价申请审批
     * <AUTHOR>
     * @date 2021/04/26 16:09
     */
    @PostMapping("/approvalSpecialPriceOrder")
    @RedisCacheConvertEnable
    public ResultEntity approvalSpecialPriceOrder(@RequestBody SpecialPriceOrderParam param) {
		
		ResultEntity check = this.orderService.checkApprovalSpecialPriceOrderParam(param);
		if(check != null) {
			return check;
		}
    	return this.orderService.approvalSpecialPriceOrder(param);
    }

    /**
     * 特价申请审核，获取销售补差相关属性
     * @param param
     * @return
     */
    @PostMapping("/querySpecialPriceSaleBalance")
    public ResultEntity querySpecialPriceSaleBalance(@RequestBody SpecialPriceOrderParam param) {
        if (StringUtils.isBlank(param.getId())) {
            return new ResultEntity(ResultEnum.COMMON_PARAM_ERROR, "特价申请单ID不能为空! ");
        }
        return success(orderService.querySpecialPriceSaleBalance(param));
    }
    
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOrders
     * @description 查询订单列表
     * <AUTHOR>
     * @date 2020/3/28 16:39
     */
    @PostMapping("/queryOrders")
    @RedisCacheConvertEnable
    public ResultEntity queryOrders(@RequestBody OrderQueryParam param) {
        return success(orderService.queryOrders(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOrder
     * @description 查询订单详情
     * <AUTHOR>
     * @date 2020/3/30 10:34
     */
    @PostMapping("/queryOrder")
    @RedisCacheConvertEnable
    public ResultEntity queryOrder(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return success();
        }
        return success(orderService.queryOrder(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOrderByCodes
     * @description 查询订单详情
     * <AUTHOR>
     * @date 2020/3/30 10:34
     */
    @PostMapping("/queryOrderByCodes")
    @RedisCacheConvertEnable
    public ResultEntity queryOrderByCodes(@RequestBody OrderQueryParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return success();
        }
        return success(orderService.queryOrderByCodes(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName deleteOrder
     * @description 删除订单
     * <AUTHOR>
     * @date 2020/3/30 16:06
     */
    @PostMapping("/deleteOrder")
    @OperationLog(opName = "删除订单")
    public ResultEntity deleteOrder(@RequestBody OrderQueryParam param) {

        RedisMultiLockObject lockObject = null;
        try {
            if (StringUtils.isBlank(param.getCode()) && StringUtils.isBlank(param.getPayNumber())) {
                return paramError("订单号不能为空");
            }
            // 批量锁定
            log.info("deleteOrder获取缓存锁 orderCodes={}",param.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getCode()));
            if (null == lockObject) {
                log.info("deleteOrder获取缓存锁失败");
                return paramError("订单已在其他业务锁定，请勿稍后再试");
            }
            if (orderService.deleteOrder(param)) {
                return success();
            }
        } catch (Exception e) {
            log.error("删除订单失败", e);
            return error(ResultEnum.ERP_MARK_DEL_ERROR, e.getMessage());
        } finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("deleteOrder释放缓存锁 orderCodes={}",param.getCode());
            if (ObjectUtils.isNotEmpty(param.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(param.getStoreSkuInventoryListKey());
            }
        }
        return error(ResultEnum.ERP_MARK_DEL_ERROR, "删除订单失败");
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPayOrder
     * @description 查询顾客待处理列表数据
     * <AUTHOR>
     * @date 2020/4/2 18:00
     */
    @PostMapping("/queryPayOrderList")
    @RedisCacheConvertEnable
    public ResultEntity queryPayOrderList(@RequestBody OrderQueryParam param) {
        log.info("====queryPayOrderList==param:{}", JSON.toJSONString(param));
    	if (StringUtils.isBlank(param.getCustomerPhone())) {
            return paramError("请输入或扫描顾客手机号 或 多点会员码");
        }
        
        PayOrderResults results = orderService.queryPayOrderList(param);
        if (null == results) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, "顾客无待处理单据，请确认");
        }
        return success(results);
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPayOrder
     * @description 根据订单号集合查询
     * <AUTHOR>
     * @date 2020/4/3 16:02
     */
    @PostMapping("/queryPayOrder")
    @RedisCacheConvertEnable
    public ResultEntity queryPayOrder(@RequestBody OrderQueryParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return paramError("订单号不能为空");
        }
        return success(orderService.queryPayOrder(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPendingOrder
     * @description 多条件查询待处理订单列表
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    @PostMapping("/queryPendingOrder")
    @RedisCacheConvertEnable
    public ResultEntity queryPendingOrder(@RequestBody OrderQueryParam param) {
        return success(orderService.queryPendingOrder(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPendingOrderToApp
     * @description 多条件查询待处理订单列表--小贝
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    @PostMapping("/queryPendingOrderToApp")
    @RedisCacheConvertEnable
    public ResultEntity queryPendingOrderToApp(@RequestBody OrderQueryParam param) {
        return success(orderService.queryPendingOrderToApp(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryProcessedOrder
     * @description 多条件查询处理中
     * <AUTHOR>
     * @date 2020/4/8 14:05
     */
    @PostMapping("/queryProcessedOrder")
    @RedisCacheConvertEnable
    public ResultEntity queryProcessedOrder(@RequestBody OrderQueryParam param) {
        System.out.println("==入参==>>" + JSON.toJSONString(param));
        if (CollectionUtils.isEmpty(param.getStatus())) {
            return paramError("状态不能为空");
        }
        return success(orderService.queryProcessedOrder(param));
    }

    /**
     * @description 已处理的订单列表
     */
    @PostMapping("/queryCompleteOrder")
    @RedisCacheConvertEnable
    public ResultEntity queryCompleteOrder(@RequestBody OrderQueryParam param) {
        System.out.println("==入参==>>" + JSON.toJSONString(param));
        if (CollectionUtils.isEmpty(param.getStatus())) {
            return paramError("状态不能为空");
        }
        return success(orderService.queryCompleteOrder(param));
    }

    /**
     * @description 已处理的订单列表
     */
    @PostMapping("/queryCompleteOrderToApp")
    @RedisCacheConvertEnable
    public ResultEntity queryCompleteOrderToApp(@RequestBody OrderQueryParam param) {
        System.out.println("==入参==>>" + JSON.toJSONString(param));
        if (CollectionUtils.isEmpty(param.getStatus())) {
            return paramError("状态不能为空");
        }
        return success(orderService.queryCompleteOrderToApp(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryProcessedOrderToApp
     * @description 多条件查询处理中，已处理的订单列表
     * <AUTHOR>
     * @date 2020/4/8 14:05
     */
    @PostMapping("/queryProcessedOrderToApp")
    @RedisCacheConvertEnable
    public ResultEntity queryProcessedOrderToApp(@RequestBody OrderQueryParam param) {

        if (CollectionUtils.isEmpty(param.getStatus())) {
            return paramError("状态不能为空");
        }

        return success(orderService.queryProcessedOrderToApp(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName countByType
     * @description 根据销售类型统计订单数量
     * <AUTHOR>
     * @date 2020/4/10 14:41
     */
    @PostMapping("/countByType")
    public ResultEntity countByType(@RequestBody OrderQueryParam param) {
        return success(orderService.countByType(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName checkIfUse
     * @description 订金转销时，判断是否已被管联
     * <AUTHOR>
     * @date 2020/4/23 18:31
     */
    @PostMapping("/checkIfUse")
    public ResultEntity checkIfUse(@RequestBody OrderQueryParam param) {
        String msg = orderService.checkIfUse(param);
        if (StringUtils.isNotBlank(msg)) {
            return paramError(msg);
        }
        return success();
    }

    /**
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName addExit
     * @description TODO 新增原单退货单
     * <AUTHOR>
     * @date 2020/5/3 11:22
     */
    @PostMapping("/addExit")
    @OperationLog(opName = "新增原单退货单")
    public ResultEntity addExit(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择退货单据");
        }
        if (StringUtils.isEmpty(orderExitChangeParam.getReturnReasonType())) {
            return paramError("请选择退货原因");
        }
        if (StringUtils.isEmpty(orderExitChangeParam.getOutType())) {
            return paramError("请选择退提货方式");
        }
        if (null == orderExitChangeParam.getSendTime()
                || orderExitChangeParam.getSendTime().before(DateUtils.addHours(new Date(), 0))) {
            return paramError("传入配送时间为空或在今天之前，请重新选择");
        }
        RedisMultiLockObject lockObject = null;

        try {
            log.info("addExit获取缓存锁 orderCode={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("addExit获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            orderService.addExit(orderExitChangeParam);
        } catch (Exception e) {
            log.error("addExit error ={}",JSON.toJSONString(e.getStackTrace()));
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("addExit释放缓存锁 orderCode={}",orderExitChangeParam.getCode());
        }
        return success();
    }

    /**
     * 新增原单换货
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName addChange
     * @description 新增原单换货
     * <AUTHOR>
     * @date 2020/5/3 11:25
     */
    @PostMapping("/addChange")
    @OperationLog(opName = "新增原单换货")
    public ResultEntity addChange(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择换货单据");
        }
        if (StringUtils.isEmpty(orderExitChangeParam.getReturnReasonType())) {
            return paramError("请选择换货原因");
        }
        if (StringUtils.isEmpty(orderExitChangeParam.getOutType())) {
            return paramError("请选择退提货方式");
        }
        if (null == orderExitChangeParam.getSendTime()
                || orderExitChangeParam.getSendTime().before(DateUtils.addHours(new Date(), 0))) {
            return paramError("传入配送时间为空或在今天之前，请重新选择");
        }
        RedisMultiLockObject lockObject = null;

        try {
            log.info("addChange获取缓存锁 orderCode={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("addChange获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            orderService.addChange(orderExitChangeParam);
        } catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("addExit释放缓存锁 orderCode={}",orderExitChangeParam.getCode());
        }
        return success();
    }

    /***
     * 定金申请退款
     * @methodName applyRefund
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/3 15:37
     */
    @PostMapping("/applyRefund")
    @OperationLog(opName = "定金申请退款")
    public ResultEntity applyRefund(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return new ResultEntity(ResultEnum.ERP_MARK_ADD_ERROR, "参数错误");
        }
        RedisMultiLockObject lockObject = null;
        try {
            log.info("applyRefund获取缓存锁 orderCode={}",param.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getCode()));
            if (null == lockObject) {
                log.info("applyRefund获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            return orderService.applyRefund(param);
        }catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("applyRefund释放缓存锁 orderCode={}",param.getCode());
        }
    }


    /***
     *定金申请退款审核
     * @methodName applyRefundAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/3 17:44
     */
    @PostMapping("/applyRefundAudit")
    @OperationLog(opName = "定金申请退款审核")
    public ResultEntity applyRefundAudit(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return new ResultEntity(ResultEnum.ERP_MARK_ADD_ERROR, "参数错误");
        }
        RedisMultiLockObject lockObject = null;
        try {
            log.info("applyRefundAudit获取缓存锁 orderCode={}",param.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getCode()));
            if (null == lockObject) {
                log.info("applyRefundAudit获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            return orderService.applyRefundAudit(param.getCode());
        }catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("applyRefundAudit释放缓存锁 orderCode={}",param.getCode());
        }
    }

    /**
     * 原单退货审核
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditExit
     * @description 原单退货审核
     * <AUTHOR>
     * @date 2020/5/4 14:14
     */
    @PostMapping("/auditExit")
    @OperationLog(opName = "原单退货审核")
    public ResultEntity auditExit(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择退货审核单据");
        }
        if (StringUtils.isEmpty(orderExitChangeParam.getOutType())) {
            return paramError("请选择退提货方式");
        }
        if (null == orderExitChangeParam.getSendTime()
                || orderExitChangeParam.getSendTime().before(DateUtils.addHours(new Date(), 0))) {
            return paramError("传入配送时间为空或在今天之前，请重新选择");
        }
        String info = "成功";
        RedisMultiLockObject lockObject = null;
        try {
            log.info("auditExit获取缓存锁 orderCode={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("auditExit获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            info = orderService.auditExit(orderExitChangeParam);
        } catch (Exception e) {
        	log.error("====auditExit==error:{}", JSON.toJSONString(e.getStackTrace()));
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("auditExit释放缓存锁 orderCode={}",orderExitChangeParam.getCode());
            if (ObjectUtils.isNotEmpty(orderExitChangeParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(orderExitChangeParam.getStoreSkuInventoryListKey());
            }
        }
        return success(info);
    }

    /**
     * 原单退货审核
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditCancelExit
     * @description 原单退货取消审核
     * <AUTHOR>
     * @date 2020/5/4 14:14
     */
    @PostMapping("/auditCancelExit")
    @OperationLog(opName = "原单退货取消审核")
    public ResultEntity auditCancelExit(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择取消退货审核单据");
        }
        RedisMultiLockObject lockObject = null;
        try {
            log.info("auditCancelExit获取缓存锁 orderCode={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("auditCancelExit获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            orderService.auditCancelExit(orderExitChangeParam);
        } catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("auditCancelExit释放缓存锁 orderCode={}",orderExitChangeParam.getCode());
            if (ObjectUtils.isNotEmpty(orderExitChangeParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(orderExitChangeParam.getStoreSkuInventoryListKey());
            }
        }
        return success();
    }

    /**
     * 原单换货审核
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditChange
     * @description 原单换货审核
     * <AUTHOR>
     * @date 2020/5/4 14:13
     */
    @PostMapping("/auditChange")
    @OperationLog(opName = "原单换货审核")
    public ResultEntity auditChange(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择换货审核单据");
        }
        if (StringUtils.isEmpty(orderExitChangeParam.getOutType())) {
            return paramError("请选择退提货方式");
        }
        if (null == orderExitChangeParam.getSendTime()
                || orderExitChangeParam.getSendTime().before(DateUtils.addHours(new Date(), 0))) {
            return paramError("传入配送时间为空或在今天之前，请重新选择");
        }
        RedisMultiLockObject lockObject = null;
        try {
            log.info("auditChange获取缓存锁 orderCode={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("auditChange获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            return success(orderService.auditChange(orderExitChangeParam));
        } catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("auditChange释放缓存锁 orderCode={}",orderExitChangeParam.getCode());
            if (ObjectUtils.isNotEmpty(orderExitChangeParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(orderExitChangeParam.getStoreSkuInventoryListKey());
            }
        }
    }

    /**
     * 原单换货审核
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName auditCancelChange
     * @description 原单取消换货审核
     * <AUTHOR>
     * @date 2020/12/16 10:13
     */
    @PostMapping("/auditCancelChange")
    @OperationLog(opName = "原单取消换货审核")
    public ResultEntity auditCancelChange(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择取消换货审核单据");
        }
        RedisMultiLockObject lockObject = null;
        try {
            log.info("auditCancelChange获取缓存锁 orderCode={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("auditCancelChange获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            return success(orderService.auditCancelChange(orderExitChangeParam));
        } catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("auditCancelChange释放缓存锁 orderCode={}",orderExitChangeParam.getCode());
            if (ObjectUtils.isNotEmpty(orderExitChangeParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(orderExitChangeParam.getStoreSkuInventoryListKey());
            }
        }
    }
    /**
     * 新增保存营销回冲单据
     *
     * @param orderPromotionRecoilResult
     * @return com.besttop.common.model.ResultEntity
     * @methodName addRecoil
     * @description 新增保存营销回冲单据
     * <AUTHOR>
     * @date 2020/5/5 17:02
     */
    @PostMapping("/addRecoil")
    @OperationLog(opName = "新增保存营销回冲单据")
    public ResultEntity addRecoil(@RequestBody OrderPromotionRecoilResult orderPromotionRecoilResult) {
        try {
            String code = orderService.addRecoil(orderPromotionRecoilResult);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", code);
            return success(jsonObject, "成功");
        } catch (Exception e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 查询销售回冲单据信息接口
     *
     * @param shoppingOrder
     * @return com.besttop.common.model.ResultEntity
     * @methodName findRecoil
     * @description 查询销售回冲单据信息接口
     * <AUTHOR>
     * @date 2020/5/5 18:26
     */
    @PostMapping("/findRecoil")
    @RedisCacheConvertEnable
    public ResultEntity findRecoil(@RequestBody ShoppingOrder shoppingOrder) {
        if (StringUtils.isEmpty(shoppingOrder.getCode())) {
            return paramError("单号不能为空");
        }
        try {
            return success(orderService.findRecoil(shoppingOrder));
        } catch (Exception e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * @param shoppingOrderPromotion
     * @return com.besttop.common.model.ResultEntity
     * @methodName findIsRecoil
     * @description 查询是否有未支付营销回冲单据
     * <AUTHOR>
     * @date 2020/5/14 14:06
     */
    @PostMapping("/findIsRecoil")
    public ResultEntity findIsRecoil(@RequestBody ShoppingOrderPromotion shoppingOrderPromotion) {
        if (StringUtils.isEmpty(shoppingOrderPromotion.getOrderCode())) {
            return paramError("单号不能为空");
        }
        try {
            String code = orderService.findIsRecoil(shoppingOrderPromotion);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", code);
            return success(jsonObject, "成功");
        } catch (Exception e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * 修改销售回冲单据
     *
     * @param orderRecoilResult
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateRecoil
     * @description TODO 修改销售回冲单据
     * <AUTHOR>
     * @date 2020/5/5 18:26
     */
    @PostMapping("/updateRecoil")
    @OperationLog(opName = "修改销售回冲单据")
    public ResultEntity updateRecoil(@RequestBody OrderRecoilResult orderRecoilResult) {
        try {
            String code = orderService.updateRecoil(orderRecoilResult);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", code);
            return success(jsonObject, "成功");
        } catch (Exception e) {
            return paramError(e.getMessage());
        }
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findBySourceCode
     * @description 根据原单号查询订单是否退货
     * <AUTHOR>
     * @date 2020/5/12 10:27
     */
    @PostMapping("/findBySourceCode")
    public ResultEntity findBySourceCode(@RequestBody ShoppingOrder param) {
        QueryWrapper<ShoppingOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(param);
        return success(orderService.list(queryWrapper));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOrderList
     * @description 查询销售单(售后服务登记使用)
     * <AUTHOR>
     * @date 2020/5/12 15:28
     */
    @PostMapping("/queryOrderList")
    @RedisCacheConvertEnable
    public ResultEntity queryOrderList(@RequestBody OrderQueryParam param) {
        return success(new PageInfo<>(orderService.queryOrderList(param)));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOrderList
     * @description 查询销售单(售后服务登记使用)
     * <AUTHOR>
     * @date 2020/5/12 15:28
     */
    @PostMapping("/queryOldOrderList")
    @RedisCacheConvertEnable
    public ResultEntity queryOldOrderList(@RequestBody OrderQueryParam param) {
        return success(new PageInfo<>(orderService.queryOldOrderList(param)));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOrderList
     * @description 查询销售单(售后回访使用)
     * <AUTHOR>
     * @date 2020/5/12 15:28
     */
    @PostMapping("/queryOrderListA")
    @RedisCacheConvertEnable
    public ResultEntity queryOrderListA(@RequestBody OrderQueryParam param) {
        List<TroubleApply> list = orderService.queryOrderList(param);
        if (null != list) {
            for (TroubleApply troubleApply : list) {
                troubleApply.setSourceCodeType("erp:trouble_record_type:7");
                troubleApply.setSourceTypeName("回访顾客");
                troubleApply.setSourceCodeType("erp:trouble_apply_source:3");
                troubleApply.setSourceCodeTypeName("PC录入");
            }
        }
        return success(new PageInfo<>(list));
    }

    /**
     * 小贝App申请退货查询原销售单信息及物流状态
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOrderExitApp
     * @description 小贝App申请退货查询原销售单信息及物流状态
     * <AUTHOR>
     * @date 2020/5/18 14:53
     */
    @PostMapping("/findOrderExitApp")
    @RedisCacheConvertEnable
    public ResultEntity findOrderExitApp(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择退货单据查询");
        }
        return success(orderService.findOrderExitApp(orderExitChangeParam.getCode()));
    }

    /**
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findOrderExitChangeApp
     * @description TODO 小贝App查询退/换货单信息及物流状态列表
     * <AUTHOR>
     * @date 2020/5/18 18:39
     */
    @PostMapping("/findOrderExitChangeApp")
    @RedisCacheConvertEnable
    public ResultEntity findOrderExitChangeApp(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getType())) {
            return paramError("请传入单据类型");
        }
        return success(orderService.findOrderExitChangeApp(orderExitChangeParam));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName checkIsRefund
     * @description 查询退货是否可退
     * <AUTHOR>
     * @date 2020/5/21 16:21
     */
    @PostMapping("/checkIsRefund")
    public ResultEntity checkIsRefund(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("退货单号不能为空");
        }
        String msg = orderService.checkIsRefund(param);
        if (StringUtils.isNotBlank(msg)) {
            return paramError(msg);
        } else {
            return success();
        }
    }


    /**
     * 新增代收代付
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName addReplace
     * @description 新增代收代付
     * <AUTHOR>
     * @date 2020/5/28 17:50
     */
    @PostMapping("/addReplace")
    @OperationLog(opName = "新增代收代付")
    public ResultEntity addReplace(@RequestBody ReplaceParam param) {
        try {
            String msg = orderService.checkReplace(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            return success(orderService.addReplace(param));
        } catch (Exception e) {
            log.info("新增代收代付失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "新增代收代付失败");
        }
    }

    /**
     * 编辑代收代付
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateReplace
     * @description 编辑代收代付
     * <AUTHOR>
     * @date 2020/5/28 17:50
     */
    @PostMapping("/updateReplace")
    @OperationLog(opName = "编辑代收代付")
    public ResultEntity updateReplace(@RequestBody ReplaceParam param) {
        RedisMultiLockObject lockObject = null;
        try {
            String msg = orderService.checkReplace(param);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
            log.info("updateReplace获取缓存锁 orderCode={}",param.getOrder().getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(param.getOrder().getCode()));
            if (null == lockObject) {
                log.info("updateReplace获取缓存锁失败");
                return paramError("订单在其他业务执行中，请稍后再试");
            }
            return success(orderService.updateReplace(param));
        } catch (Exception e) {
            log.info("编辑代收代付失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "编辑代收代付失败");
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("updateReplace释放缓存锁 orderCode={}",param.getOrder().getCode());
        }

    }

    /**
     * 查询代收代付
     *
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryReplace
     * @description 查询代收代付
     * <AUTHOR>
     * @date 2020/5/28 17:50
     */
    @PostMapping("/queryReplace")
    @RedisCacheConvertEnable
    public ResultEntity queryReplace(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("代收代付单号不能为空");
        }
        return success(orderService.queryReplace(param));
    }

    /**
     * 查询代收代付
     * param
     *
     * @return com.besttop.common.model.ResultEntity
     * @methodName test
     * @description 查询代收代付
     * <AUTHOR>
     * @date 2020/5/28 17:50
     */
    @PostMapping("/test")
    @RedisCacheConvertEnable
    public ResultEntity test(@RequestBody JSONObject jsonObject) {
        orderService.preSaleToSale(jsonObject);
        return success();
    }

    /***
     * 查询物流配送状态
     * @methodName findLogisticsDeliver
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/7/1 16:26
     */
    @PostMapping("/findLogisticsDeliver")
    @RedisCacheConvertEnable
    public ResultEntity findLogisticsDeliver(@RequestBody OrderLogisticsParam param) {
        return orderService.findLogisticsDeliver(param);
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryAuditOrder
     * @description 查询待审核，已审核的订单（小程序）
     * <AUTHOR>
     * @date 2020/7/9 9:37
     */
    @PostMapping("/queryAuditOrder")
    @RedisCacheConvertEnable
    public ResultEntity queryAuditOrder(@RequestBody OrderQueryParam param) {
//        if (StringUtils.isBlank(param.getOrderStatus())) {
//            return paramError("订单状态不能为空");
//        }
        return success(new PageInfo<>(orderService.queryAuditOrder(param)));
    }

    /**
     * 预售转销 手动
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName handPresaleToSale
     * @description 预售转销 手动
     * <AUTHOR>
     * @date 2020/12/10 14:13
     */
    @PostMapping("/handPresaleToSale")
    @OperationLog(opName = "预售转销")
    public ResultEntity handPresaleToSale(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选转销单据");
        }
        RedisMultiLockObject lockObject = null;
        try {
            // 批量锁定
            log.info("handPresaleToSale获取缓存锁 orderCodes={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("handPresaleToSale获取缓存锁失败");
                return paramError("订单已在其他业务锁定，请勿稍后再试");
            }
            return success(orderService.handPresaleToSale(orderExitChangeParam));
        } catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("handPresaleToSale释放缓存锁 orderCodes={}",orderExitChangeParam.getCode());
            if (ObjectUtils.isNotEmpty(orderExitChangeParam.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(orderExitChangeParam.getStoreSkuInventoryListKey());
            }
        }
    }


    /**
     * 退换货售后服务编辑
     *
     * @param orderExitChangeParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateChangeOrExit
     * @description 退换货售后服务编辑
     * <AUTHOR>
     * @date 2020/5/4 14:14
     */
    @PostMapping("/updateChangeOrExit")
    @OperationLog(opName = "退换货售后服务编辑")
    public ResultEntity updateChangeOrExit(@RequestBody OrderExitChangeParam orderExitChangeParam) {
        if (StringUtils.isEmpty(orderExitChangeParam.getCode())) {
            return paramError("请选择退单据");
        }
        if (null != orderExitChangeParam.getSendTime()
                && orderExitChangeParam.getSendTime().before(DateUtils.addHours(new Date(), 0))) {
            return paramError("传入配送时间为空或在今天之前，请重新选择");
        }
        RedisMultiLockObject lockObject = null;
        try {
            // 批量锁定
            log.info("updateChangeOrExit获取缓存锁 orderCodes={}",orderExitChangeParam.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(orderExitChangeParam.getCode()));
            if (null == lockObject) {
                log.info("updateChangeOrExit获取缓存锁失败");
                return paramError("订单已在其他业务锁定，请勿稍后再试");
            }
            orderService.updateChangeOrExit(orderExitChangeParam);
        } catch (Exception e) {
            return paramError(e.getMessage());
        }finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("updateChangeOrExit释放缓存锁 orderCodes={}",orderExitChangeParam.getCode());
        }
        return success();
    }
    
    @PostMapping("/queryUserInfos")
    @OperationLog(opName = "退换货售后服务编辑")
    public ResultEntity queryUserInfos() {
        return success(loginCacheUtil.getCacheInfo());
    }

    /***
     * 查询物流配送状态
     * @methodName findLogisticsDeliver
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2021/10/13 16:26
     */
    @PostMapping("/findRefundOrder")
    @RedisCacheConvertEnable
    public ResultEntity findRefundOrder(@RequestBody OrderLogisticsParam param) {
        return orderService.findRefundOrder(param);
    }

    /**
     * 支付中订单作废 手动
     * @param orderCode
     * @return
     * @methodName cancelOrder
     * @description 支付中订单作废 手动
     **/
    @PostMapping("/cancelOrder")
    @OperationLog(opName = "支付中订单作废 手动")
    public ResultEntity cancelOrder(@RequestBody OrderQueryParam order){
        RedisMultiLockObject lockObject = null;
        try {
            if (StringUtils.isBlank(order.getCode())) {
                return paramError("订单号不能为空");
            }
            // 批量锁定
            log.info("cancelOrder 获取缓存锁 orderCodes={}",order.getCode());
            lockObject = this.redisLockUtil.getPaySaveLock(Arrays.asList(order.getCode()));
            if (null == lockObject) {
                log.info("cancelOrder 获取缓存锁失败");
                return paramError("订单已在其他业务锁定，请勿稍后再试");
            }
            if (orderService.cancelOrder(order)) {
                return success();
            }
        } catch (Exception e) {
            log.error("订单作废失败", e);
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, e.getMessage());
        } finally {
            redisLockUtil.releaseBatchLock(lockObject);
            log.info("cancelOrder 释放缓存锁 orderCodes={}",order.getCode());
            if (ObjectUtils.isNotEmpty(order.getStoreSkuInventoryListKey())) {
                redisLockUtil.releaseBatchLock(order.getStoreSkuInventoryListKey());
            }
        }
        return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "订单作废失败");
    }

    /***
     * 查询普通退货原单号
     * @methodName queryNonsaleReturnCode
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2024/12/23 12:21
     */
    @PostMapping("/queryNonsaleReturnCode")
    public ResultEntity queryNonsaleReturnCode(@RequestBody OrderQueryParam param) {
        if (StringUtils.isEmpty(param.getCode())) {
            return paramError("请输入销售单号");
        }
        return success(orderService.queryNonsaleReturnCode(param));
    }
}

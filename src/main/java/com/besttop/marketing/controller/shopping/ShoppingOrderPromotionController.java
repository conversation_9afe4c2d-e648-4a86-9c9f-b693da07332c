package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.shopping.ShoppingOrderPromotion;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.service.shopping.ShoppingOrderPromotionService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 销售订单促销活动表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/shoppingOrderPromotion")
public class ShoppingOrderPromotionController extends BaseController {
    @Autowired
    private ShoppingOrderPromotionService shoppingOrderPromotionService;

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectIsPromotionRecoil
     * @description 查询是否需要营销回冲
     * @params [shoppingOrderPromotion]
     * <AUTHOR>
     * @date 2020/5/4 15:22
     */
    @PostMapping("/selectIsPromotionRecoil")
    public ResultEntity selectIsPromotionRecoil(@RequestBody OrderQueryParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return paramError("单号不能为空");
        }
        String str = shoppingOrderPromotionService.selectIsPromotionRecoil(param.getOrderCodes());
        if (!str.equals("ok")) {
            return success(true, str);
        }
        return success(false);
    }

    /**
     * 根据单号查询待会冲项目
     *
     * @param shoppingOrderPromotion
     * @return com.besttop.common.model.ResultEntity
     * @methodName findPromotionRecoil
     * @description 根据单号查询待会冲项目
     * <AUTHOR>
     * @date 2020/5/4 18:01
     */
    @PostMapping("/findPromotionRecoil")
    @RedisCacheConvertEnable
    public ResultEntity findPromotionRecoil(@RequestBody ShoppingOrderPromotion shoppingOrderPromotion) {
        if (StringUtils.isEmpty(shoppingOrderPromotion.getOrderCode())) {
            return paramError("单号不能为空");
        }
        try {
            return success(shoppingOrderPromotionService.findPromotionRecoil(shoppingOrderPromotion));
        } catch (Exception e) {
            return paramError(e.getMessage());
        }
    }


}

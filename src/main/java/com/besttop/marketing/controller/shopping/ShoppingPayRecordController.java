package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.shopping.ShoppingOrderSku;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.model.shopping.param.ShoppingPayRecordParam;
import com.besttop.marketing.service.shopping.ShoppingPayRecordService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 结算支付记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@RestController
@RequestMapping("/shoppingPayRecord")
@Slf4j
public class ShoppingPayRecordController extends BaseController {

    @Autowired
    private ShoppingPayRecordService payRecordService;

    /**
     * @param record
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPayRecord
     * @description 查询支付记录
     * <AUTHOR>
     * @date 2020/3/12 15:43
     */
    @PostMapping("/queryPayRecord")
    @RedisCacheConvertEnable
    public ResultEntity queryPayRecord(@RequestBody ShoppingPayRecord record) {
        record.setIsReturn(0);
        return success(payRecordService.queryPayRecord(record));
    }
    
    
    @PostMapping("/updatePayRecord")
    public ResultEntity updatePayRecord(@RequestBody ShoppingPayRecord record) {
    	record.setIsReturn(null);
    	record.setIsPayRefund(null);
        return success(payRecordService.updateById(record));
    }

    @PostMapping("/updateWeChatPayRecord")
    public ResultEntity updateWeChatPayRecord(@RequestBody ShoppingPayRecord record) {
    	if(StringUtils.isBlank(record.getPayVoucher())) {
    		return paramError("凭证号不能为空 !");
    	}
    	if(null == record.getPayAmount() && record.getPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
    		return paramError("请输入正确的支付金额 !");
    	}
    	String msg = this.payRecordService.updateWeChatPayRecord(record);
    	if("ok".equalsIgnoreCase(msg)) {
    		return success();
    	}else {
    		return error(ResultEnum.COMMON_ERROR);
    	}
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPayRecordByCode
     * @description 根据订单号集合查询支付记录
     * <AUTHOR>
     * @date 2020/4/24 14:12
     */
    @PostMapping("/queryPayRecordByCode")
    @RedisCacheConvertEnable
    public ResultEntity queryPayRecordByCode(@RequestBody OrderQueryParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return paramError("订单号不能为空");
        }
        try {
            return success(payRecordService.queryPayRecordByCode(param));
        } catch (Exception e) {
            log.error("根据订单号集合查询支付记录异常:", e.getMessage());
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, e.getMessage());
        }
    }

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryRefundPayRecordByCode
     * @description 退款查询支付记录
     * @params [param]
     * <AUTHOR>
     * @date 2020/5/3 17:13
     */
    @PostMapping("/queryRefundPayRecordByCode")
    @RedisCacheConvertEnable
    public ResultEntity queryRefundPayRecordByCode(@RequestBody ShoppingOrderSku sku) {
        if (StringUtils.isEmpty(sku.getOrderCode())) {
            return paramError("退款单号不能为空");
        }
        return success(payRecordService.queryRefundPayRecordByCode(sku));
    }

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryRefundPayRecordByCode
     * @description 退款查询支付记录
     * @params [param]
     * <AUTHOR>
     * @date 2020/5/3 17:13
     */
    @PostMapping("/queryRefundRecordByCode")
    @RedisCacheConvertEnable
    public ResultEntity queryRefundRecordByCode(@RequestBody ShoppingPayRecord shoppingPayRecord) {
        if (StringUtils.isEmpty(shoppingPayRecord.getPayNumber())) {
            return paramError("退款流水不能为空");
        }
        return success(payRecordService.queryRefundRecordByCode(shoppingPayRecord));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryShoppingPayRecords
     * @description 根据支付类型统计收款，为app 个人中心报表支付记录统计提供接口
     * <AUTHOR>
     * @date 2020/5/11 13:44
     */
    @PostMapping(value = "/queryShoppingPayRecords")
    @RedisCacheConvertEnable
    public ResultEntity queryShoppingPayRecords(@RequestBody ShoppingPayRecordParam param) {
        return success(payRecordService.queryShoppingPayRecords(param));
    }

    /**
     * @methodName queryArrearsRecord
     * @description 查询欠款还款记录
     * @param record
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2021/1/7 10:17
     */
    @PostMapping("/queryArrearsRecord")
    @RedisCacheConvertEnable
    public ResultEntity queryArrearsRecord(@RequestBody ShoppingPayRecord record){
        if (StringUtils.isBlank(record.getPayNumber())){
            return paramError("流水号不能为空");
        }
        return success(payRecordService.queryArrearsRecord(record));
    }
}


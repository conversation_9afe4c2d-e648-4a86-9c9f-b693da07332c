package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.shopping.ShoppingDeliver;
import com.besttop.marketing.model.shopping.param.ShoppingDeliverParam;
import com.besttop.marketing.service.shopping.ShoppingDeliverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 临时送货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingDeliver")
public class ShoppingDeliverController extends BaseController {

    @Autowired
    private ShoppingDeliverService shoppingDeliverService;


    /***
     *临时送货单新增或者修改
     * @methodName addDeliver
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:44
     */
    @PostMapping("/addAndUpdateDeliver")
    @OperationLog(opName = "临时送货单新增或者修改")
    public ResultEntity addAndUpdateDeliver(@RequestBody ShoppingDeliverParam param) {
        try {
            String message;
            if (StringUtils.isNotBlank(param.getId())) {
                message = "修改成功";
            } else {
                message = "新增成功";
            }
            shoppingDeliverService.addAndUpdateDeliver(param);
            return success(message);
        } catch (Exception e) {
            log.error("临时送货单新增或修改失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *临时送货单新增或者修改审核
     * @methodName add
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:45
     */
    @PostMapping("/addOrUpdateOrAudit")
    @OperationLog(opName = "临时送货单新增或者修改审核")
    public ResultEntity auditOrAddOrUpdate(@RequestBody ShoppingDeliverParam param) {
        try {
            shoppingDeliverService.addOrUpdateOrAudit(param);
            return success("审核成功");
        } catch (Exception e) {
            log.error("临时送货单修改审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     * 判断是否关联的物流配送单是否全部为待分拣
     */
    @PostMapping("/isSorting")
    @OperationLog(opName = "判断是否关联的物流配送单是否全部为待分拣")
    public ResultEntity isSorting(@RequestBody ShoppingDeliverParam param) {
        try {
            return new ResultEntity(ResultEnum.SUCCESS, shoppingDeliverService.isSorting(param), "修改物流配送成功");
        } catch (Exception e) {
            log.error("判断是否关联的物流配送单是否全部为待分拣失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     * 审核状态下修改配送地址
     */
    @PostMapping("/updateDeliver")
    @OperationLog(opName = "审核状态下修改配送地址")
    public ResultEntity updateDeliver(@RequestBody ShoppingDeliverParam param) {
        try {
            shoppingDeliverService.updateDeliver(param);
            return new ResultEntity(ResultEnum.SUCCESS, "成功");
        } catch (Exception e) {
            log.error("审核状态下修改配送地址失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *临时送货单取消审核
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:55
     */
    @PostMapping("/cancelAudit")
    @OperationLog(opName = "临时送货单取消审核")
    public ResultEntity cancelAudit(@RequestBody ShoppingDeliverParam param) {
        try {
            shoppingDeliverService.cancelAudit(param);
            return success("取消审核成功");
        } catch (Exception e) {
            log.error("临时送货单取消审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     *删除临时送货单
     * @methodName deleteInfo
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 13:54
     */
    @PostMapping("/deleteInfo")
    @OperationLog(opName = "删除临时送货单")
    public ResultEntity deleteInfo(@RequestBody ShoppingDeliverParam param) {
        try {
            shoppingDeliverService.deleteInfo(param);
            return success("删除成功");
        } catch (Exception e) {
            log.error("临时送货单删除失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *临时送货单查询列表
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 11:15
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<List<ShoppingDeliver>> findAll(@RequestBody ShoppingDeliverParam param) {
        try {
            return shoppingDeliverService.findAll(param);
        } catch (Exception e) {
            log.error("临时送货单查询失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     *临时送货单查询详情
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 11:16
     */
    @PostMapping("/findInfoByCode")
    @RedisCacheConvertEnable
    public ResultEntity<ShoppingDeliverParam> findInfoByCode(@RequestBody ShoppingDeliverParam param) {
        try {
            return shoppingDeliverService.findInfoByCode(param);
        } catch (Exception e) {
            log.error("临时送货单查询详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

}

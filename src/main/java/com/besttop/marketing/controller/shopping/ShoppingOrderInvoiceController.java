package com.besttop.marketing.controller.shopping;


import com.alibaba.fastjson.JSONObject;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.controller.shopping.invoice.InvoiceAuthorization;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderInvoice;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.service.shopping.ShoppingOrderInvoiceService;
import com.besttop.marketing.service.shopping.ShoppingPayRecordService;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 订单税票信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/shoppingOrderInvoice")
public class ShoppingOrderInvoiceController extends BaseController {

    @Autowired
    private ShoppingOrderInvoiceService invoiceService;
    @Autowired
    private LoginCacheUtil loginCacheUtil;
    @Autowired
    private ShoppingPayRecordService payRecordService;

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryInvoiceConfig
     * @description 查询开票配置信息
     * <AUTHOR>
     * @date 2020/6/16 11:10
     */
    @PostMapping("/queryInvoiceConfig")
    public ResultEntity queryInvoiceConfig(@RequestBody ShoppingOrderInvoice invoice) {
        if (CollectionUtils.isEmpty(invoice.getOrderCodes())) {
            return paramError("订单号不能为空");
        }
        // 发票授权
        String storeCode = StringUtils.isBlank(invoice.getStoreCode()) ? loginCacheUtil.getStoreCode() : invoice.getStoreCode();
        InvoiceAuthorization invoiceAuthorization = invoiceService.queryInvoiceConfig(storeCode);
        if (null == invoiceAuthorization || invoiceAuthorization.getIsEnable() == 0) {
            return error(ResultEnum.ERP_MARK_QUERY_ERROR, "机构未配置/启用开票信息【发票授权管理】，请联系管理员");
        }
        // 查询是否有开票额度
        if (!"app".equals(invoice.getFlag())) {
            List<ShoppingPayRecord> records = payRecordService.queryPayRecordByOrderInvoice(invoice.getOrderCodes().get(0));
            BigDecimal invoiceAmount = invoiceService.queryInvoiceAmount(invoice.getOrderCodes(), invoice.getFlag(), records);
            if (records.size() > 0 && invoiceAmount.compareTo(BigDecimal.ZERO) == 0) {
                return error(ResultEnum.ERP_MARK_QUERY_ERROR, "支付方式不支持开票，请联系管理员");
            }
            if (records.size() <= 0 && invoiceAmount.compareTo(BigDecimal.ZERO) == 0) {
                return error(ResultEnum.ERP_MARK_QUERY_ERROR, "税价合计为0，无法开票");
            }
        }
        return success(invoiceAuthorization);
    }


    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryInvoiceRecord
     * @description 查询开票记录
     * <AUTHOR>
     * @date 2020/9/18 14:16
     */
    @PostMapping("/queryInvoiceRecord")
    public ResultEntity queryInvoiceRecord(@RequestBody ShoppingOrderInvoice invoice) {
        if (StringUtils.isBlank(invoice.getOrderCode())) {
            paramError("单号不能为空");
        }
        return success(new PageInfo<>(invoiceService.queryInvoiceRecord(invoice)));
    }

    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryInvoiceOrderCode
     * @description 查询开过票的单号
     * <AUTHOR>
     * @date 2020/9/18 16:06
     */
    @PostMapping("/queryInvoiceOrderCode")
    public ResultEntity queryInvoiceOrderCode(@RequestBody ShoppingOrderInvoice invoice) {
        return success(invoiceService.queryInvoiceOrderCode(invoice));
    }


    /**
     * @param param 开发票记录
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryCustomerInvoice
     * @description TODO
     * <AUTHOR>
     * @date 2021/1/25 13:54
     */
    @PostMapping("/queryCustomerInvoice")
    public ResultEntity queryCustomerInvoice(@RequestBody ShoppingOrder param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("顾客不能为空");
        }
        return success(new PageInfo<>(invoiceService.queryCustomerInvoice(param)));
    }

    @PostMapping("/callBack")
    public Map callBack(@RequestBody JSONObject jsonObject) {
        return invoiceService.callBack(jsonObject);
    }

}

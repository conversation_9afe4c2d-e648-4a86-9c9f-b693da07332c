package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.service.shopping.ShoppingPayThirdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 结算支付第三方日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/shoppingPayThird")
public class ShoppingPayThirdController extends BaseController {

    @Autowired
    private ShoppingPayThirdService thirdService;

    /**
     * @methodName queyCardValueDetail
     * @description 查询储值卡支付明细
     * @param record
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/7/6 15:03
     */
    @PostMapping("/queyCardValueDetail")
    public ResultEntity queyCardValueDetail(@RequestBody ShoppingPayRecord record){
       if (StringUtils.isBlank(record.getPayVoucher())){
            return paramError("支付凭证不能为空");
        }
        return success(thirdService.queyCardValueDetail(record));
    }

}

package com.besttop.marketing.controller.shopping;


import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.service.shopping.ShoppingPayRecordTempService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 小贝收银退款临时表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@RestController
@RequestMapping("/shoppingPayRecordTemp")
public class ShoppingPayRecordTempController extends BaseController {

    @Autowired
    private ShoppingPayRecordTempService recordTempService;

    /**
     * @param record
     * @return com.besttop.common.model.ResultEntity
     * @methodName findRefundRecord
     * @description 小贝查询退款记录
     * <AUTHOR>
     * @date 2020/7/14 16:31
     */
    @PostMapping("/findRefundRecord")
    @RedisCacheConvertEnable
    public ResultEntity findRefundRecord(@RequestBody ShoppingPayRecord record) {
        if (StringUtils.isBlank(record.getPayNumber())) {
            return paramError("支付流水不能为空");
        }
        return success(recordTempService.findRefundRecord(record));
    }

}

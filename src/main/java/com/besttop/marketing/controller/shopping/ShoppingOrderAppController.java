package com.besttop.marketing.controller.shopping;

import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.service.shopping.ShoppingOrderSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 销售订单(顾客小程序) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingOrderApp")
public class ShoppingOrderAppController extends BaseController {

    @Autowired
    private ShoppingOrderSkuService orderSkuService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDfh
     * @description 查询待发货订单
     * <AUTHOR>
     * @date 2020/4/26 10:44
     */
    @PostMapping("/findDfh")
    public ResultEntity findDfh(@RequestBody OrderQueryParam param) {
        return success(orderSkuService.findDfh(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDsh
     * @description 查询待收货订单
     * <AUTHOR>
     * @date 2020/4/26 10:45
     */
    @PostMapping("/findDsh")
    public ResultEntity findDsh(@RequestBody OrderQueryParam param) {
        return success(orderSkuService.findDsh(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDpj
     * @description 查询待评价订单
     * <AUTHOR>
     * @date 2020/4/26 10:45
     */
    @PostMapping("/findDpj")
    public ResultEntity findDpj(@RequestBody OrderQueryParam param) {
        return success(orderSkuService.findDpj(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName findYwc
     * @description 查询已完成订单
     * <AUTHOR>
     * @date 2020/4/26 10:45
     */
    @PostMapping("/findYwc")
    public ResultEntity findYwc(@RequestBody OrderQueryParam param) {
        return success(orderSkuService.findYwc(param));
    }

}

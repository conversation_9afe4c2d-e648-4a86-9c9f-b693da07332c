package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.shopping.ShoppingSaleReceipt;
import com.besttop.marketing.model.shopping.param.ShoppingSaleReceiptParam;
import com.besttop.marketing.service.shopping.ShoppingSaleReceiptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 订单厂家送货回执表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingSaleReceipt")
public class ShoppingSaleReceiptController extends BaseController {

    @Autowired
    private ShoppingSaleReceiptService receiptService;

    /**
     * @param receipt
     * @return com.besttop.common.model.ResultEntity
     * @methodName receipt
     * @description 回执
     * <AUTHOR>
     * @date 2020/4/21 16:05
     */
    @PostMapping("/receipt")
    @OperationLog(opName = "回执")
    public ResultEntity receipt(@RequestBody ShoppingSaleReceipt receipt) {
        try {
            String msg = receiptService.receipt(receipt);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
        } catch (Exception e) {
            log.error("回执失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "回执失败");
        }
        return success();
    }

    /**
     * @param receipt
     * @return com.besttop.common.model.ResultEntity
     * @methodName cancelReceipt
     * @description 取消回执
     * <AUTHOR>
     * @date 2020/4/21 16:05
     */
    @PostMapping("/cancelReceipt")
    @OperationLog(opName = "取消回执")
    public ResultEntity cancelReceipt(@RequestBody ShoppingSaleReceipt receipt) {
        try {
            String msg = receiptService.cancelReceipt(receipt);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
        } catch (Exception e) {
            log.error("取消回执失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "取消回执失败");
        }
        return success();
    }

    /**
     * @param shoppingSaleReceiptParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName findReceipt
     * @description 小程序查询未回执、已回执单据
     * <AUTHOR>
     * @date 2021/6/18 15:34
     */
    @PostMapping("/findReceipt")
    @RedisCacheConvertEnable
    public ResultEntity findReceipt(@RequestBody ShoppingSaleReceiptParam shoppingSaleReceiptParam) {
        return success(receiptService.findReceipt(shoppingSaleReceiptParam));
    }

    /**
     * @param receiptParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateReceipt
     * @description 回执
     * <AUTHOR>
     * @date 2020/4/21 16:05
     */
    @PostMapping("/updateReceipt")
    @OperationLog(opName = "小程序无码确认")
    public ResultEntity updateReceipt(@RequestBody ShoppingSaleReceiptParam receiptParam) {
        try {
            String msg = receiptService.updateReceipt(receiptParam);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
        } catch (Exception e) {
            log.error("确认失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "确认失败");
        }
        return success();
    }

    /**
     * @param receipt
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateCancelReceipt
     * @description 取消回执
     * <AUTHOR>
     * @date 2020/4/21 16:05
     */
    @PostMapping("/updateCancelReceipt")
    @OperationLog(opName = "小程序取消确认")
    public ResultEntity updateCancelReceipt(@RequestBody ShoppingSaleReceipt receipt) {
        try {
            String msg = receiptService.updateCancelReceipt(receipt);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
        } catch (Exception e) {
            log.error("取消确认失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "取消确认失败");
        }
        return success();
    }

    /**
     * @param receiptParam
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateReceiptApplet
     * @description 小程序顾客确认签收(回执)
     * <AUTHOR>
     * @date 2021/7/19 14:21
     */
    @PostMapping("/updateReceiptApplet")
    @OperationLog(opName = "小程序顾客确认签收(回执)")
    public ResultEntity updateReceiptApplet(@RequestBody ShoppingSaleReceiptParam receiptParam) {
        try {
            String msg = receiptService.updateReceiptApplet(receiptParam);
            if (StringUtils.isNotBlank(msg)) {
                return paramError(msg);
            }
        } catch (Exception e) {
            log.error("确认失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "确认失败");
        }
        return success();
    }
}

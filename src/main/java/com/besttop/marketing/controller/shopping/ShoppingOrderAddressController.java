package com.besttop.marketing.controller.shopping;


import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.shopping.ShoppingOrderAddress;
import com.besttop.marketing.model.shopping.param.OrderExitChangeParam;
import com.besttop.marketing.model.shopping.param.OrderOrdinaryParam;
import com.besttop.marketing.service.shopping.ShoppingOrderAddressService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 销售订单送货地址表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingOrderAddress")
public class ShoppingOrderAddressController extends BaseController {

    @Autowired
    private ShoppingOrderAddressService orderAddressService;

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateSendAddress
     * @description 校验修改配送地址的订单
     * @params [address]
     * <AUTHOR>
     * @date 2020/4/22 17:39
     */
    @PostMapping("/checkParam")
    public ResultEntity checkParam(@RequestBody ShoppingOrderAddress address) {
        String msg = orderAddressService.checkParam(address);
        if (StringUtils.isNotBlank(msg)) {
            return paramError(msg);
        }
        return success();
    }

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateSendAddress
     * @description 修改配送地址
     * @params [address]
     * <AUTHOR>
     * @date 2020/4/22 17:39
     */
    @PostMapping("/updateSendAddress")
    @OperationLog(opName = "修改配送地址")
    public ResultEntity updateSendAddress(@RequestBody @Validated(GroupUpdate.class) ShoppingOrderAddress address) {
        try {
            String message = orderAddressService.updateSendAddress(address);
            String[] msg = message.split(",");
            if ("OK".equals(msg[0])) {
                if (msg.length > 1) {
                    return success(msg[1]);
                }
                return success();
            }else {
                return error(ResultEnum.ERP_MARK_UPDATE_ERROR, message);
            }
        } catch (Exception e) {
            log.error("修改配送地址失败:{}", JSON.toJSON(e.getStackTrace()));
            log.error("修改配送地址失败:{}", JSON.toJSON(e.getMessage()));
            return error(ResultEnum.ERP_MARK_UPDATE_ERROR, "修改配送地址失败");
        }
    }

    /***
     *普通退货登记-新增或者修改
     * @methodName addDeliver
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:44
     */
    @PostMapping("/addAndUpdateDeliver")
    @OperationLog(opName = "普通退货登记-新增或者修改")
    public ResultEntity addAndUpdateDeliver(@RequestBody OrderOrdinaryParam param) {
        try {
            String message;
            if (StringUtils.isNotBlank(param.getOrder().getId())) {
                message = "修改成功";
            } else {
                message = "新增成功";
            }
            orderAddressService.addAndUpdateDeliver(param);
            return success(message);
        } catch (Exception e) {
            log.error("普通退货登记新增失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *普通退货登记新增或者修改审核
     * @methodName add
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:45
     */
    @PostMapping("/addOrUpdateOrAudit")
    @OperationLog(opName = "普通退货登记新增或者修改审核")
    public ResultEntity addOrUpdateOrAudit(@RequestBody OrderOrdinaryParam param) {
        try {
            orderAddressService.addOrUpdateOrAudit(param);
            return success("审核成功");
        } catch (Exception e) {
            log.error("普通退货登记修改审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /**
     * 取消普通退货登记
     */
    @PostMapping("/cancelAudit")
    @OperationLog(opName = "普通退货登记取消审核")
    public ResultEntity cancelAudit(@RequestBody OrderOrdinaryParam param) {
        try {
            orderAddressService.cancelAudit(param);
            return success("取消成功");
        } catch (Exception e) {
            log.error("普通退货登记取消成功失败" + e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     *删除普通退货登记
     * @methodName deleteInfo
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 13:54
     */
    @PostMapping("/deleteInfo")
    @OperationLog(opName = "删除普通退货登记")
    public ResultEntity deleteInfo(@RequestBody OrderExitChangeParam param) {
        try {
            orderAddressService.deleteInfo(param);
            return success("删除成功");
        } catch (Exception e) {
            log.error("普通退货登记删除失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     *普通退货登记查询详情
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 11:16
     */
    @PostMapping("/findInfoByCode")
    @RedisCacheConvertEnable
    public ResultEntity<OrderOrdinaryParam> findInfoByCode(@RequestBody OrderExitChangeParam param) {
        try {
            return orderAddressService.findInfoByCode(param);
        } catch (Exception e) {
            log.error("普通退货登记查询详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

}

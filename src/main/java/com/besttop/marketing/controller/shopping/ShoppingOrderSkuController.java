package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.model.shopping.param.ServiceParam;
import com.besttop.marketing.service.shopping.ShoppingOrderSkuService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 销售开单SKU表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/shoppingOrderSku")
public class ShoppingOrderSkuController extends BaseController {

    @Autowired
    private ShoppingOrderSkuService shoppingOrderSkuService;

    /***
     * 售后订单 查询换货
     * @methodName getWorkload
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/3 17:00
     */
    @RequestMapping(value = "/getChangeInfo")
    @RedisCacheConvertEnable
    public ResultEntity getChangeInfo(@RequestBody ServiceParam param) {
        return shoppingOrderSkuService.getChangeOrReturn(param, CommonEnums.SHOPPING_ORDER_TYPE_E.getCode());
    }


    /***
     * 售后订单 查询退货
     * @methodName getWorkload
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/3 17:00
     */
    @RequestMapping(value = "/getReturnInfo")
    @RedisCacheConvertEnable
    public ResultEntity getReturnInfo(@RequestBody ServiceParam param) {
        return shoppingOrderSkuService.getChangeOrReturn(param, CommonEnums.SHOPPING_ORDER_TYPE_D.getCode());
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryDeposit
     * @description 根据顾客，sku查询订金
     * <AUTHOR>
     * @date 2020/6/4 9:39
     */
    @PostMapping("/queryDeposit")
    public ResultEntity queryDeposit(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCustomerCode())) {
            return paramError("请选择顾客");
        }
        if (StringUtils.isBlank(param.getSkuCode())) {
            return paramError("请选择SKU");
        }
        return success(shoppingOrderSkuService.queryDeposit(param));
    }

    /**
     * Description:
     *
     * <AUTHOR>
     * @date: 2020-10-27 13:54
     * @param:[param]
     * @return:com.besttop.common.model.ResultEntity
     */
    @PostMapping("/querySkuByCode")
    public ResultEntity querySkuByCode(@RequestBody OrderQueryParam param) {
        if (StringUtils.isBlank(param.getCode())) {
            return paramError("请选择单据号");
        }
        return success(shoppingOrderSkuService.querySkuByCode(param.getCode()));
    }


}

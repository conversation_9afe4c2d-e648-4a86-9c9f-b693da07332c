package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.shopping.ShoppingSaleRecord;
import com.besttop.marketing.model.shopping.param.PayOrderParam;
import com.besttop.marketing.model.shopping.param.ShoppingPayRecordParam;
import com.besttop.marketing.service.shopping.ShoppingSaleRecordService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 销售记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/shoppingSaleRecord")
public class ShoppingSaleRecordController extends BaseController {

    @Autowired
    private ShoppingSaleRecordService recordService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPrintInfo
     * @description 查询打印小票信息
     * <AUTHOR>
     * @date 2020/4/20 10:23
     */
    @PostMapping("/queryPrintInfo")
    @RedisCacheConvertEnable
    public ResultEntity queryPrintInfo(@RequestBody PayOrderParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return paramError("订单号不能为空");
        }
        if(StringUtils.isBlank(param.getType())){
            param.setType(CommonEnums.SHOPPING_ORDER_TYPE_A.getCode());
        }
        return success(recordService.queryPrintInfo(param));
    }


    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPrintInfo
     * @description 查询打印延保信息
     * <AUTHOR>
     * @date 2020/4/20 10:23
     */
    @PostMapping("/queryPrintAcpp")
    @RedisCacheConvertEnable
    public ResultEntity queryPrintAcpp(@RequestBody PayOrderParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return paramError("订单号不能为空");
        }
        if(StringUtils.isBlank(param.getType())){
            param.setType(CommonEnums.SHOPPING_ORDER_TYPE_A.getCode());
        }
        return success(recordService.queryPrintAcpp(param));
    }

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity<java.util.List < com.besttop.marketing.model.shopping.ShoppingSaleRecord>>
     * @methodName queryShoppingSaleRecords
     * @description 查询销售记录
     * <AUTHOR>
     * @date 2020/5/11 15:27
     */
    @PostMapping(value = "/queryShoppingSaleRecords")
    public ResultEntity<List<ShoppingSaleRecord>> queryShoppingSaleRecords(@RequestBody ShoppingPayRecordParam param) {
        return success(recordService.queryShoppingSaleRecords(param));
    }

}

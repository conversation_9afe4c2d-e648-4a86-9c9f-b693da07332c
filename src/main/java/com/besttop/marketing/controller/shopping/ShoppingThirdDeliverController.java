package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.shopping.ShoppingThirdDeliver;
import com.besttop.marketing.model.shopping.param.ShoppingThirdDeliverParam;
import com.besttop.marketing.service.shopping.ShoppingThirdDeliverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 代管库存送货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingThirdDeliver")
public class ShoppingThirdDeliverController extends BaseController {

    @Autowired
    private ShoppingThirdDeliverService shoppingThirdDeliverService;


    /***
     *代管库存送货单新增或者修改
     * @methodName addDeliver
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:44
     */
    @PostMapping("/addAndUpdateDeliver")
    @OperationLog(opName = "代管库存送货单新增或者修改")
    public ResultEntity addAndUpdateDeliver(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            String message;
            if (StringUtils.isNotBlank(param.getId())) {
                message = "修改成功";
            } else {
                message = "新增成功";
            }
            shoppingThirdDeliverService.addAndUpdateDeliver(param);
            return success(message);
        } catch (Exception e) {
            log.error("代管库存送货单新增或修改失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *代管库存送货单新增或者修改审核
     * @methodName add
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:45
     */
    @PostMapping("/addOrUpdateOrAudit")
    @OperationLog(opName = "代管库存送货单新增或者修改审核")
    public ResultEntity auditOrAddOrUpdate(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            if(null == param.getType()){
                param.setType(CommonEnums.SHOPPING_THIRD_DELIVER_TYPE_A.getCode());
            }
            shoppingThirdDeliverService.addOrUpdateOrAudit(param);
            return success("审核成功");
        } catch (Exception e) {
            log.error("代管库存送货单修改审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     * 判断是否关联的物流配送单是否全部为待分拣
     */
    @PostMapping("/isSorting")
    @OperationLog(opName = "判断是否关联的物流配送单是否全部为待分拣")
    public ResultEntity isSorting(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            return new ResultEntity(ResultEnum.SUCCESS, shoppingThirdDeliverService.isSorting(param), "修改物流配送成功");
        } catch (Exception e) {
            log.error("判断是否关联的物流配送单是否全部为待分拣失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     * 审核状态下修改配送地址
     */
    @PostMapping("/updateDeliver")
    @OperationLog(opName = "审核状态下修改配送地址")
    public ResultEntity updateDeliver(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            shoppingThirdDeliverService.updateDeliver(param);
            return new ResultEntity(ResultEnum.SUCCESS, "ok");
        } catch (Exception e) {
            log.error("审核状态下修改配送地址失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *代管库存送货单取消审核
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:55
     */
    @PostMapping("/cancelAudit")
    @OperationLog(opName = "代管库存送货单取消审核")
    public ResultEntity cancelAudit(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            shoppingThirdDeliverService.cancelAudit(param);
            return success("取消审核成功");
        } catch (Exception e) {
            log.error("代管库存送货单取消审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *代管库存 退货
     * @methodName refund
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:55
     */
    @PostMapping("/refund")
    @OperationLog(opName = "代管库存退货")
    public ResultEntity refund(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            shoppingThirdDeliverService.refund(param);
            return success("取消退货成功");
        } catch (Exception e) {
            log.error("代管库存送货单取消审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     *代管库存 退货审核
     * @methodName refundAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 10:55
     */
    @PostMapping("/refundAudit")
    @OperationLog(opName = "代管库存退货审核")
    public ResultEntity refundAudit(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            shoppingThirdDeliverService.refundAudit(param);
            return success("取消退货成功");
        } catch (Exception e) {
            log.error("代管库存送货单取消审核失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *删除代管库存送货单
     * @methodName deleteInfo
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 13:54
     */
    @PostMapping("/deleteInfo")
    @OperationLog(opName = "删除代管库存送货单")
    public ResultEntity deleteInfo(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            shoppingThirdDeliverService.deleteInfo(param);
            return success("删除成功");
        } catch (Exception e) {
            log.error("代管库存送货单删除失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }


    /***
     *代管库存送货单查询列表
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 11:15
     */
    @PostMapping("/findAll")
    @RedisCacheConvertEnable
    public ResultEntity<List<ShoppingThirdDeliver>> findAll(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            return shoppingThirdDeliverService.findAll(param);
        } catch (Exception e) {
            log.error("代管库存送货单查询失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

    /***
     *代管库存送货单查询详情
     * @methodName cancelAudit
     * @description
     * @params [param]
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/5/11 11:16
     */
    @PostMapping("/findInfoByCode")
    @RedisCacheConvertEnable
    public ResultEntity<ShoppingThirdDeliverParam> findInfoByCode(@RequestBody ShoppingThirdDeliverParam param) {
        try {
            return shoppingThirdDeliverService.findInfoByCode(param);
        } catch (Exception e) {
            log.error("代管库存送货单查询详情失败", e);
            return error(ResultEnum.ERP_MARK_ADD_ERROR, e.getMessage());
        }
    }

}

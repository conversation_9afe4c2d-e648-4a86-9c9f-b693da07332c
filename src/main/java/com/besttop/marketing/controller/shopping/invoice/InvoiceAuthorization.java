package com.besttop.marketing.controller.shopping.invoice;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.besttop.common.prefix.RedisPrefix_ERP;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.datacache.annotation.RedisCacheConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * 发票授权
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Data
@Accessors(chain = true)
public class InvoiceAuthorization {

    private static final long serialVersionUID = 1L;

    /**
     * 是否启用:0停用1启用,默认停用
     */
    private Integer isEnable = 0;

    /**
     * 生效公司
     */
    @NotNull(message = "公司不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    @RedisCacheConvert(prefix = RedisPrefix_ERP.JI_GOU_XIN_XI, convertList = {"name:effectCompanyName"})
    private String effectCompany;

    /**
     * 请求连接
     */
    @NotNull(message = "请求连接不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String url;

    /**
     * appKey
     */
    @NotNull(message = "appKey不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String appKey;

    /**
     * 接口名称
     */
    @NotNull(message = "接口名称不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String appApi;

    /**
     * 并发请求数（默认10）
     */
    @NotNull(message = "并发请求数不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private Integer appRate;

    /**
     * 数据加密格式
     */
    @TableField("app_signType")
    @NotNull(message = "数据加密格式不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String appSigntype;

    /**
     * 接口版本
     */
    @TableField("app_apiVersion")
    @NotNull(message = "接口版本不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String appApiversion;

    /**
     * 令牌
     */
    @TableField("app_accessToken")
    @NotNull(message = "令牌不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String appAccesstoken;

    /**
     * 授权码
     */
    @NotNull(message = "授权码不能为空", groups = {GroupAdd.class, GroupUpdate.class})
    private String appSecret;

    /**
     * 发件人
     */
    private String email;

    /**
     * 邮箱账号
     */
    private String emailCode;

    /**
     * 邮箱密码
     */
    private String acceptCode;

    /**
     * 邮箱地址
     */
    private String serviceAddress;

    /**
     * 二维码地址
     */
    private String qrUrl;

    @TableField("create_by")
    private String createBy;
    @TableField("create_time")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    @TableField("update_by")
    private String updateBy;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @TableField("update_time")
    private Date updateTime;

    @TableId(
            type = IdType.ID_WORKER_STR
    )
    private String id;

}

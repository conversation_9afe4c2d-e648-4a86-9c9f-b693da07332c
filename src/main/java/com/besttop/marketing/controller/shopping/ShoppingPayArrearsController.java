package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.shopping.param.PayOrderParam;
import com.besttop.marketing.service.shopping.ShoppingPayArrearsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 结算支付欠款表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@RestController
@RequestMapping("/shoppingPayArrears")
public class ShoppingPayArrearsController extends BaseController {

    @Autowired
    private ShoppingPayArrearsService payArrearsService;

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryArrears
     * @description 查询欠款数据
     * <AUTHOR>
     * @date 2020/5/3 17:41
     */
    @PostMapping("/queryArrears")
    @RedisCacheConvertEnable
    public ResultEntity queryArrears(@RequestBody PayOrderParam param) {
        if (CollectionUtils.isEmpty(param.getOrderCodes())) {
            return paramError("订单号不能为空");
        }
        if (StringUtils.isBlank(param.getPayNumber())) {
            return paramError("支付流水号不能为空");
        }
        return success(payArrearsService.queryArrears(param));
    }

}

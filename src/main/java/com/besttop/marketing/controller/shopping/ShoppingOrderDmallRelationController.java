package com.besttop.marketing.controller.shopping;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.mapper.shopping.ShoppingOrderDmallRelationMapper;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderDmallRelation;
import com.besttop.marketing.model.shopping.ShoppingOrderPromotion;
import com.besttop.marketing.model.shopping.param.*;
import com.besttop.marketing.model.shopping.result.OrderPromotionRecoilResult;
import com.besttop.marketing.model.shopping.result.OrderRecoilResult;
import com.besttop.marketing.model.shopping.result.PayOrderResults;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import com.besttop.redis.model.RedisMultiLockObject;
import com.besttop.redis.utils.LoginCacheUtil;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 销售订单主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Slf4j
@RestController
@RequestMapping("/shoppingOrderDmall")
public class ShoppingOrderDmallRelationController extends BaseController {
	
	@Autowired
	private ShoppingOrderDmallRelationMapper shoppingOrderDmallRelationMapper;
	
    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryOutType
     * @description 查询出库方式
     * <AUTHOR>
     * @date 2020/3/23 15:09
     */
    @PostMapping("/addOrderRelation")
    public ResultEntity addOrderRelation(@RequestBody Map<String, Object> param) {
        
    	if(MapUtil.isEmpty(param)) {
    		return paramError("参数不能为空");
    	}
    	
    	ShoppingOrderDmallRelation sodr = new ShoppingOrderDmallRelation();
		if(null != param.get("orderCode")) { 
			sodr.setOrderCode(param.get("orderCode").toString());
		}
		if(null != param.get("dmallOrderCode")) {
			sodr.setDmallOrderCode(param.get("dmallOrderCode").toString());
		}
		sodr.setCreateTime(new Date());
		sodr.setType(1);
		if(null != param.get("createTime")) {
			sodr.setCreateTime(DateUtil.parse(param.get("createTime").toString(), "yyyy-MM-dd HH:mm:ss"));
		}
		if(null != param.get("type")) {
			sodr.setType(Integer.valueOf(param.get("type").toString()));
		}
		
		log.info("====addOrderRelation=ShoppingOrderDmallRelation:{}", JSON.toJSONString(sodr));
		int insert = this.shoppingOrderDmallRelationMapper.insert(sodr);
		log.info("====addOrderRelation=insert:{}", insert);
		
		if(insert > 0) {
			return success();
		} else {
			return error(ResultEnum.ADD_ERROR);
		}
    }

    
}

package com.besttop.marketing.controller.shopping;


import com.besttop.common.controller.BaseController;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.marketing.model.shopping.param.ProviderShareParam;
import com.besttop.marketing.model.shopping.result.BillProviderFeeResult;
import com.besttop.marketing.service.shopping.ShoppingProviderShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <p>
 * 供应商促销分摊表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@RestController
@RequestMapping("/shoppingProviderShare")
public class ShoppingProviderShareController extends BaseController {
    @Autowired
    private ShoppingProviderShareService shoppingProviderShareService;

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectProviderShare
     * @description 费用监控单查询明细使用
     * @params [providerShareParam]
     * <AUTHOR>
     * @date 2020/4/17 14:50
     */
    @PostMapping("/select")
    @RedisCacheConvertEnable
    public ResultEntity<BillProviderFeeResult> selectProviderShare(@RequestBody @Validated(GroupAdd.class) ProviderShareParam providerShareParam) {
        return success(shoppingProviderShareService.selectProviderShare(providerShareParam));
    }

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectProviderShare
     * @description 查询分摊金额
     * @params [providerShareParam]
     * <AUTHOR>
     * @date 2020/4/17 14:50
     */
    @PostMapping("/selectFee")
    @RedisCacheConvertEnable
    public ResultEntity selectProviderShareFee(@RequestBody @Validated(GroupAdd.class) ProviderShareParam providerShareParam) {
        if(null == providerShareParam.getStartDate() || null == providerShareParam.getEndDate()){
            return paramError("时间不能为空");
        }
        BigDecimal fee = shoppingProviderShareService.selectProviderShareFee(providerShareParam);
        if(fee.compareTo(BigDecimal.ZERO)==0){
            return paramError("没有可选择的费用");
        }
        return success(fee);
    }

    //根据公司查询所有合同
    @PostMapping("/selectcontractByStoreCode")
    @RedisCacheConvertEnable
    public ResultEntity<BillProviderFeeResult> selectcontractByStoreCode(@RequestBody @Validated(GroupAdd.class) ProviderShareParam providerShareParam) {
        return success(shoppingProviderShareService.selectcontractByStoreCode(providerShareParam));
    }
}

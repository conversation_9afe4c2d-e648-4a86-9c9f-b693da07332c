package com.besttop.marketing.controller.old;


import com.alibaba.fastjson.JSON;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.old.OldfornewOperConfigSku;
import com.besttop.marketing.model.old.param.OldfornewOperConfigSkuParam;
import com.besttop.marketing.service.old.OldfornewOperConfigSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.besttop.common.controller.BaseController;

/**
 * <p>
 * 以旧换新sku配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@RestController
@RequestMapping("/oldfornewOperConfigSku")
@Slf4j
public class OldfornewOperConfigSkuController extends BaseController {

    @Autowired
    private OldfornewOperConfigSkuService oldfornewOperConfigSkuService;

    /**
     * 新增编辑旧换新sku配置单
     * @methodName add
     * @description 新增编辑旧换新sku配置单
     * @param oldfornewOperConfigSkuParam
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/5/4 9:49
     */
    @OperationLog(opName = "新增编辑旧换新sku配置单")
    @PostMapping("/addOrUpdate")
    @RedisCacheConvertEnable
    public ResultEntity addOrUpdate(@RequestBody OldfornewOperConfigSkuParam oldfornewOperConfigSkuParam) {
        try {
            return success(oldfornewOperConfigSkuService.addOrUpdate(oldfornewOperConfigSkuParam));
        } catch (Exception e) {
            log.info("==============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_ADD_ERROR, e.getMessage());
        }
    }


    /**
     * 条件查询
     * @methodName find
     * @description  条件查询
     * @param oldfornewOperConfigSkuParam
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/5/4 10:52
     */
    @PostMapping("/find")
    @RedisCacheConvertEnable
    public ResultEntity find(@RequestBody OldfornewOperConfigSkuParam oldfornewOperConfigSkuParam) {
        return success(oldfornewOperConfigSkuService.find(oldfornewOperConfigSkuParam));
    }

    /**
     * 小程序查询
     * @methodName findByApplet
     * @description  小程序查询
     * @param oldfornewOperConfigSku
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/4/27 10:52
     */
    @PostMapping("/findByApplet")
    @RedisCacheConvertEnable
    public ResultEntity findByApplet(@RequestBody OldfornewOperConfigSku oldfornewOperConfigSku) {

        return success(oldfornewOperConfigSkuService.findByApplet(oldfornewOperConfigSku));
    }
}

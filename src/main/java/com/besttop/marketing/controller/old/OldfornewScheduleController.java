package com.besttop.marketing.controller.old;


import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.old.OldfornewSchedule;
import com.besttop.marketing.model.old.param.OldfornewScheduleParam;
import com.besttop.marketing.service.old.OldfornewScheduleService;
import com.besttop.redis.model.RedisMultiLockObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 以旧换新推券规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@RestController
@RequestMapping("/oldfornewSchedule")
@Slf4j
public class OldfornewScheduleController extends BaseController {

    @Autowired
    private OldfornewScheduleService oldfornewScheduleService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    /**
     * 新增以旧换新推券规则单
     * @methodName add
     * @description 新增以旧换新推券规则单
     * @param oldfornewScheduleParam
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/27 9:49
     */
    @OperationLog(opName = "新增以旧换新推券规则单")
    @PostMapping("/add")
    @RedisCacheConvertEnable
    public ResultEntity add(@RequestBody OldfornewScheduleParam oldfornewScheduleParam) {
        if (StringUtils.isNotEmpty(oldfornewScheduleParam.getId())) {
            return paramError("新增以旧换新推券规则单传入id,不能新增");
        }

        try {
            return success(oldfornewScheduleService.add(oldfornewScheduleParam));
        } catch (Exception e) {
            log.info("==============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * 删除以旧换新推券规则单
     * @methodName delete
     * @description 删除以旧换新推券规则单
     * @param oldfornewSchedule
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2022/6/1 10:48
     */
    @OperationLog(opName = "删除以旧换新推券规则单")
    @PostMapping("/delete")
    public ResultEntity<String> delete(@RequestBody OldfornewSchedule oldfornewSchedule) {
        if (CollectionUtils.isEmpty(oldfornewSchedule.getIds())) {
            return paramError("请选择未审核单据删除");
        }
        RedisMultiLockObject redisLock = null;
        if (CollectionUtils.isNotEmpty(oldfornewSchedule.getIds())) {
            // 主键id
            redisLock = redisLockUtil.getAuditExitLock(oldfornewSchedule.getIds());
        }
        try {
            oldfornewScheduleService.delete(oldfornewSchedule);
        } catch (Exception e) {
            log.info("=============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_DEL_ERROR, e.getMessage());
        }finally {
            if (null != redisLock) {
                redisLockUtil.releaseBatchLock(redisLock);
            }
        }
        return success();
    }

    /**
     * 修改/审核以旧换新推券规则单
     * @methodName update
     * @description 修改/审核以旧换新推券规则单
     * @param oldfornewScheduleParam
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2022/6/1 10:50
     */
    @OperationLog(opName = "修改/审核以旧换新推券规则单")
    @PostMapping("/update")
    @RedisCacheConvertEnable
    public ResultEntity update(@RequestBody OldfornewScheduleParam oldfornewScheduleParam) {
        if (StringUtils.isEmpty(oldfornewScheduleParam.getId())) {
            return paramError("修改以旧换新推券规则单传入id为空");
        }
        String key = "";
        if (StringUtils.isNotBlank(oldfornewScheduleParam.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewScheduleParam.getId());
        }
        try {
            return success(oldfornewScheduleService.update(oldfornewScheduleParam));
        } catch (Exception e) {
            log.info("==========异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }


    /**
     * 取消以旧换新推券规则单
     * @methodName updateCancel
     * @description 取消以旧换新推券规则单
     * @param oldfornewSchedule
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/27 10:50
     */
    @OperationLog(opName = "取消以旧换新推券规则单")
    @PostMapping("/updateCancel")
    @RedisCacheConvertEnable
    public ResultEntity updateCancel(@RequestBody OldfornewSchedule oldfornewSchedule) {
        if (StringUtils.isEmpty(oldfornewSchedule.getId())) {
            return paramError("取消以旧换新推券规则单传入id为空");
        }
        String key = "";
        if (StringUtils.isNotBlank(oldfornewSchedule.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewSchedule.getId());
        }
        try {
            return success(oldfornewScheduleService.updateCancel(oldfornewSchedule));
        } catch (Exception e) {
            log.info("=============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }

    /**
     * 条件查询
     * @methodName find
     * @description  条件查询
     * @param oldfornewScheduleParam
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/4/27 10:52
     */
    @PostMapping("/find")
    @RedisCacheConvertEnable
    public ResultEntity find(@RequestBody OldfornewScheduleParam oldfornewScheduleParam) {
        return success(oldfornewScheduleService.find(oldfornewScheduleParam));
    }

    /**
     * @methodName findDetail
     * @description 查询明细
     * @param oldfornewScheduleParam
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/4/27 17:17
     */
    @PostMapping("/findDetail")
    @RedisCacheConvertEnable
    public ResultEntity findDetail(@RequestBody OldfornewScheduleParam oldfornewScheduleParam){
        if (StringUtils.isEmpty(oldfornewScheduleParam.getCode())) {
            return paramError("以旧换新推券规则单code传参为空，不能查询");
        }
        return success(oldfornewScheduleService.findDetail(oldfornewScheduleParam));
    }

    /**
     * 通过sku查询生效券
     * @methodName findEffectiveCouponBySku
     * @description  通过sku查询生效券
     * @param oldfornewScheduleParam
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/4/27 10:52
     */
    @PostMapping("/findEffectiveCouponBySku")
    @RedisCacheConvertEnable
    public ResultEntity findEffectiveCouponBySku(@RequestBody OldfornewScheduleParam oldfornewScheduleParam) {
        if (StringUtils.isEmpty(oldfornewScheduleParam.getSkuCode())) {
            return paramError("skuCode传参为空，不能查询");
        }
        return success(oldfornewScheduleService.findEffectiveCouponBySku(oldfornewScheduleParam));
    }
}

package com.besttop.marketing.controller.old;


import com.alibaba.fastjson.JSON;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.datacache.annotation.RedisCacheConvertEnable;
import com.besttop.edp.utils.RedisLockUtil;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.model.old.OldfornewSkuDetail;
import com.besttop.marketing.model.old.param.OldfornewSkuDetailParam;
import com.besttop.marketing.service.old.OldfornewSkuDetailService;
import com.besttop.redis.model.RedisMultiLockObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 以旧换新单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@RestController
@RequestMapping("/oldfornewSkuDetail")
@Slf4j
public class OldfornewSkuDetailController extends BaseController {

    @Autowired
    private OldfornewSkuDetailService oldfornewSkuDetailService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    /**
     * 新增以旧换新单
     * @methodName add
     * @description 新增以旧换新单
     * @param oldfornewSkuDetail
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/27 9:49
     */
    @OperationLog(opName = "新增以旧换新单")
    @PostMapping("/add")
    @RedisCacheConvertEnable
    public ResultEntity add(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (StringUtils.isNotEmpty(oldfornewSkuDetail.getId())) {
            return paramError("新增以旧换新单传入id,不能新增");
        }
        if (StringUtils.isEmpty(oldfornewSkuDetail.getSkuCode())) {
            return paramError("请选择回收商品");
        }

        if (StringUtils.isEmpty(oldfornewSkuDetail.getCustomerCode())) {
            return paramError("请选择顾客");
        }
        try {
            return success(oldfornewSkuDetailService.add(oldfornewSkuDetail));
        } catch (Exception e) {
            log.info("==============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_ADD_ERROR, e.getMessage());
        }
    }

    /**
     * 删除以旧换新单
     * @methodName delete
     * @description 删除以旧换新单
     * @param oldfornewSkuDetail
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2022/6/1 10:48
     */
    @OperationLog(opName = "删除以旧换新单")
    @PostMapping("/delete")
    public ResultEntity<String> delete(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (CollectionUtils.isEmpty(oldfornewSkuDetail.getIds())) {
            return paramError("请选择未审核单据删除");
        }
        RedisMultiLockObject redisLock = null;
        if (CollectionUtils.isNotEmpty(oldfornewSkuDetail.getIds())) {
            // 主键id
            redisLock = redisLockUtil.getAuditExitLock(oldfornewSkuDetail.getIds());
        }
        try {
            oldfornewSkuDetailService.delete(oldfornewSkuDetail);
        } catch (Exception e) {
            log.info("=============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_DEL_ERROR, e.getMessage());
        }finally {
            if (null != redisLock) {
                redisLockUtil.releaseBatchLock(redisLock);
            }
        }
        return success();
    }

    /**
     * 修改以旧换新单
     * @methodName update
     * @description 修改以旧换新单
     * @param oldfornewSkuDetail
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2022/6/1 10:50
     */
    @OperationLog(opName = "修改以旧换新单")
    @PostMapping("/update")
    @RedisCacheConvertEnable
    public ResultEntity update(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (StringUtils.isEmpty(oldfornewSkuDetail.getId())) {
            return paramError("修改以旧换新单传入id为空");
        }
        if (StringUtils.isEmpty(oldfornewSkuDetail.getSkuCode())) {
            return paramError("请选择回收商品");
        }
        if (StringUtils.isEmpty(oldfornewSkuDetail.getCustomerCode())) {
            return paramError("请选择顾客");
        }
        String key = "";
        if (StringUtils.isNotBlank(oldfornewSkuDetail.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewSkuDetail.getId());
        }
        try {
            return success(oldfornewSkuDetailService.update(oldfornewSkuDetail));
        } catch (Exception e) {
            log.info("==========异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }

    /**
     * 审核以旧换新单
     * @methodName updateAudit
     * @description 审核以旧换新单
     * @param oldfornewSkuDetail
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/27 10:50
     */
    @OperationLog(opName = "审核以旧换新单")
    @PostMapping("/updateAudit")
    @RedisCacheConvertEnable
    public ResultEntity updateAudit(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (StringUtils.isEmpty(oldfornewSkuDetail.getId())) {
            return paramError("审核以旧换新单传入id为空");
        }

        if (StringUtils.isEmpty(oldfornewSkuDetail.getCouponCode())) {
            return paramError("请选择券");
        }

        String key = "";
        if (StringUtils.isNotBlank(oldfornewSkuDetail.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewSkuDetail.getId());
        }
        try {
            return success(oldfornewSkuDetailService.updateAudit(oldfornewSkuDetail));
        } catch (Exception e) {
            log.info("============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }
    /**
     * 确认以旧换新单
     * @methodName updateConfirm
     * @description 确认以旧换新单
     * @param oldfornewSkuDetail
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/27 10:50
     */
    @OperationLog(opName = "确认以旧换新单")
    @PostMapping("/updateConfirm")
    @RedisCacheConvertEnable
    public ResultEntity updateConfirm(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (StringUtils.isEmpty(oldfornewSkuDetail.getId())) {
            return paramError("确认以旧换新单传入id为空");
        }

        String key = "";
        if (StringUtils.isNotBlank(oldfornewSkuDetail.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewSkuDetail.getId());
        }
        try {
            return success(oldfornewSkuDetailService.updateConfirm(oldfornewSkuDetail));
        } catch (Exception e) {
            log.info("==============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }
    /**
     * 作废以旧换新单
     * @methodName updateCancel
     * @description 作废以旧换新单
     * @param oldfornewSkuDetail
     * @return com.besttop.common.model.ResultEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/4/27 10:50
     */
    @OperationLog(opName = "作废以旧换新单")
    @PostMapping("/updateCancel")
    @RedisCacheConvertEnable
    public ResultEntity updateCancel(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (StringUtils.isEmpty(oldfornewSkuDetail.getId())) {
            return paramError("作废以旧换新单传入id为空");
        }
        String key = "";
        if (StringUtils.isNotBlank(oldfornewSkuDetail.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewSkuDetail.getId());
        }
        try {
            return success(oldfornewSkuDetailService.updateCancel(oldfornewSkuDetail));
        } catch (Exception e) {
            log.info("=============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }

    /**
     * 条件查询
     * @methodName find
     * @description  条件查询
     * @param oldfornewSkuDetailParam
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/4/27 10:52
     */
    @PostMapping("/find")
    @RedisCacheConvertEnable
    public ResultEntity find(@RequestBody OldfornewSkuDetailParam oldfornewSkuDetailParam) {
        return success(oldfornewSkuDetailService.find(oldfornewSkuDetailParam));
    }

    /**
     * 小程序查询
     * @methodName findByCustomer
     * @description  小程序查询
     * @param oldfornewSkuDetailParam
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2023/4/27 10:52
     */
    @PostMapping("/findByCustomer")
    @RedisCacheConvertEnable
    public ResultEntity findByCustomer(@RequestBody OldfornewSkuDetailParam oldfornewSkuDetailParam) {
        if (StringUtils.isEmpty(oldfornewSkuDetailParam.getCustomerCode())) {
            return paramError("请传入顾客编码");
        }
        return success(oldfornewSkuDetailService.findByCustomer(oldfornewSkuDetailParam));
    }

    @OperationLog(opName = "手动发放以旧换新券")
    @PostMapping("/updateConfirmCoupon")
    @RedisCacheConvertEnable
    public ResultEntity updateConfirmCoupon(@RequestBody OldfornewSkuDetail oldfornewSkuDetail) {
        if (StringUtils.isEmpty(oldfornewSkuDetail.getId())) {
            return paramError("确认以旧换新单传入id为空");
        }

        String key = "";
        if (StringUtils.isNotBlank(oldfornewSkuDetail.getId())) {
            // 主键id
            key = redisLockUtil.getAuditExitLock(oldfornewSkuDetail.getId());
        }
        try {
            return success(oldfornewSkuDetailService.updateConfirmCoupon(oldfornewSkuDetail));
        } catch (Exception e) {
            log.info("==============异常:", JSON.toJSONString(e.getStackTrace()));
            return error(ResultEnum.ERP_SETT_UPDATE_ERROR, e.getMessage());
        }finally {
            if (StringUtils.isNotBlank(key)) {
                redisLockUtil.releaseLock(key);
            }
        }
    }
}

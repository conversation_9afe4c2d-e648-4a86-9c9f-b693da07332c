package com.besttop.marketing.controller.print;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.besttop.marketing.feign.InvoiceFeignClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.besttop.common.controller.BaseController;
import com.besttop.common.enums.ResultEnum;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.validate.GroupAdd;
import com.besttop.common.validate.GroupUpdate;
import com.besttop.log.annotation.OperationLog;
import com.besttop.marketing.controller.customer.CustomerInvoiceController;
import com.besttop.marketing.controller.shopping.ShoppingOrderInvoiceController;
import com.besttop.marketing.controller.thirdparty.NuoNuoInvoiceController;
import com.besttop.marketing.feign.SmsFeignClient;
import com.besttop.marketing.feign.SystemFeignClient;
import com.besttop.marketing.model.customer.CustomerInvoice;
import com.besttop.marketing.model.print.param.SelfPrintQueryParam;
import com.besttop.marketing.model.shopping.ShoppingOrderInvoice;
import com.besttop.marketing.model.thirdparty.nuonuo.param.NuoNuoElecronicInvoiceInfo;
import com.besttop.marketing.model.thirdparty.param.Email;
import com.besttop.marketing.service.print.SelfPrintService;
import com.besttop.marketing.util.GetRedisUtil;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * Title: SelfPrintController
 * </p>
 * <p>
 * Description: SelfPrintController
 * </p>
 * <p>
 * Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018
 * </p>
 * <p>
 * 自助打印 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-24
 */

@Slf4j
@RestController
@RequestMapping("/selfPrint")
public class SelfPrintController extends BaseController {

	@Autowired
	private SmsFeignClient smsService;

	@Autowired
	private SelfPrintService selfPrintService;

	@Autowired
	private ShoppingOrderInvoiceController invoiceController;

	@Autowired
	private CustomerInvoiceController customerInvoiceController;

	@Autowired
	private InvoiceFeignClient invoiceFeignClient;

	@Autowired
	private SystemFeignClient systemFeignClient;

	@Autowired
	private ShoppingOrderInvoiceController shoppingOrderInvoiceController;
	
//    @Autowired
//    private GetRedisUtil getRedisUtil;

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName queryOrders
	 * @description 手机号/流水号/订单号 查订单信息
	 * <AUTHOR>
	 * @date 2020/3/23 15:09
	 */
	@PostMapping("/queryOrders")
	public ResultEntity queryOrders(@RequestBody SelfPrintQueryParam param) {
		log.info("----IN com.besttop.marketing.controller.print.SelfPrintController.queryOrders()");
		log.info("----param: {}", JSON.toJSON(param));
		Map<String, Object> params = null;
		if (StringUtils.isBlank(param.getCode())) {
			if (StringUtils.isBlank(param.getPhone()) || StringUtils.isBlank(param.getCheckCode())) {
				return this.paramError("手机号和验证码都不能为空!");
			} else {
				params = new HashMap<>();
				params.put("phone", param.getPhone());
				params.put("code", param.getCheckCode());
				ResultEntity check = this.smsService.checkCode(params);
				if (check.getFlag() != 1) {
					return this.paramError("验证码输入不正确!");
				}
			}
		}

		if (param.getPage() != null && param.getPage() <= 0) {
			return this.paramError("当前页码不能小于0 !");
		}
		if (param.getRows() != null && param.getRows() <= 0) {
			return this.paramError("每页记录条数不能小于0 !");
		}
		return this.success(new PageInfo(this.selfPrintService.queryOrderInfo(param)));
	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName sendCode
	 * @description 发验证码
	 * <AUTHOR>
	 * @date 2020/3/29 11:26
	 */
//	@Deprecated
//	public ResultEntity sendCode(@RequestBody SelfPrintQueryParam param) {
//		if (StringUtils.isBlank(param.getPhone())) {
//			return this.paramError("手机号不能为空!");
//		}
//
//		Map<String, Object> params = new HashMap<>();
//		params.put("phone", param.getPhone());
//		params.put("validityTime", 600);
//
//		return this.smsService.sendCode(params);
//	}

	/**
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName checkCode
	 * @description 校验验证码
	 * <AUTHOR>
	 * @date 2020/3/29 11:26
	 */
//	@Deprecated
//	public ResultEntity checkCode(@RequestBody SelfPrintQueryParam param) {
//
//		if (StringUtils.isBlank(param.getPhone()) || StringUtils.isBlank(param.getCheckCode())) {
//			return this.paramError("手机号和验证码都不能为空!");
//		}
//
//		Map<String, Object> params = new HashMap<>();
//		params.put("phone", param.getPhone());
//		params.put("code", param.getCheckCode());
//
//		return this.smsService.checkCode(params);
//	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName queryInvoiceConfig
	 * @description 查询顾客开票配置信息
	 * <AUTHOR>
	 * @date 2021/03/31 19:06
	 */
	@PostMapping("/queryInvoiceConfig")
	public ResultEntity queryInvoiceConfig(@RequestBody ShoppingOrderInvoice invoice) {
		ArrayList<String> orderCodes = new ArrayList<String>();
		orderCodes.add(invoice.getOrderCode());
		invoice.setOrderCodes(orderCodes);
		return invoiceController.queryInvoiceConfig(invoice);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName queryInvoice
	 * @description 查询顾客税票信息
	 * <AUTHOR>
	 * @date 2021/04/01 11:06
	 */
	@PostMapping("/queryInvoice")
	public ResultEntity queryInvoice(@RequestBody CustomerInvoice invoice) {
		// {"customerCode":"00000579"}
		return customerInvoiceController.queryInvoice(invoice);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName addInvoice
	 * @description 新增税票信息
	 * <AUTHOR>
	 * @date 2021/04/01 18:22
	 */
	@PostMapping("/addInvoice")
	@OperationLog(opName = "新增税票信息")
	public ResultEntity addInvoice(@RequestBody @Validated(GroupAdd.class) CustomerInvoice invoice) {

		return customerInvoiceController.addInvoice(invoice);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName updateInvoice
	 * @description 更新税票信息
	 * <AUTHOR>
	 * @date 2021/04/01 18:22
	 */
	@PostMapping("/updateInvoice")
	@OperationLog(opName = "更新税票信息")
	public ResultEntity updateInvoice(@RequestBody @Validated(GroupUpdate.class) CustomerInvoice invoice) {

		return customerInvoiceController.updateInvoice(invoice);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName deleteInvoice
	 * @description 删除税票信息
	 * <AUTHOR>
	 * @date 2021/04/01 18:22
	 */
	@PostMapping("/deleteInvoice")
	@OperationLog(opName = "删除税票信息")
	public ResultEntity deleteInvoice(@RequestBody CustomerInvoice invoice) {

		return customerInvoiceController.deleteInvoice(invoice);
	}

	/**
	 * @methodName findByRedisKey
	 * @description 查询发票收款人信息
	 * @param param
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2021/04/01 14:16
	 */
	@PostMapping("/queryInvoicePayee")
	public ResultEntity findByRedisKey(@RequestBody Map<String, String> param) {
		return success(systemFeignClient.findOption(param));
	}

	/**
	 * @methodName requestBilling
	 * @description 开具/回冲发票
	 * @param invoiceInfo
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2021/04/01 14:38
	 */
	@PostMapping("/requestBilling")
	public ResultEntity<Set<Map<String, Object>>> requestBilling(
			@RequestBody @Validated(GroupAdd.class) NuoNuoElecronicInvoiceInfo invoiceInfo) {
		ResultEntity<Set<Map<String, Object>>> checkResult = this.checkOrder(invoiceInfo);
		if (checkResult != null) {
			return checkResult;
		}
		if (!StringUtils.isNotBlank(invoiceInfo.getStoreCode())) {
			String storeCode = this.selfPrintService.queryStoreCodeByOrderCode(invoiceInfo.getOrderCodes().get(0));
			if (StringUtils.isNotBlank(storeCode)) {
				invoiceInfo.setStoreCode(storeCode);
			}
		}
		return invoiceFeignClient.requestBilling(invoiceInfo);
	}

	private ResultEntity<Set<Map<String, Object>>> checkOrder(NuoNuoElecronicInvoiceInfo invoiceInfo) {
		// 检查申请开正票的单子
		if (invoiceInfo.getOrderCodes().size() > 0 && invoiceInfo.getInvoiceType() == 1
				&& invoiceInfo.getOperationType() == 1) {
			if (this.selfPrintService.checkRefundOrderRequestBlueInvvoicve(invoiceInfo)) {
				return new ResultEntity<>(ResultEnum.ERROR, null, "退货单，不能开发票！");
			}
			if (this.selfPrintService.checkSourceOrderRequestBlueInvvoicve(invoiceInfo)) {
				return new ResultEntity<>(ResultEnum.ERROR, null, "发起退款的销售单，不能开发票！");
			}
		}
		return null;
	}

	/**
	 * @methodName requestBillingAcpp
	 * @description 请求开具发票V2.0.0
	 * @param invoiceInfo
	 * @return com.besttop.common.model.ResultEntity
	 * <AUTHOR>
	 * @date 2021/04/01 15:50
	 */
	@PostMapping("/requestBillingAcpp")
	@ResponseBody
	public ResultEntity<Set<Map<String, Object>>> requestBillingAcpp(
			@RequestBody @Validated(GroupAdd.class) NuoNuoElecronicInvoiceInfo invoiceInfo) {
		ResultEntity<Set<Map<String, Object>>> checkResult = this.checkOrder(invoiceInfo);
		if (checkResult != null) {
			return checkResult;
		}
		if (!StringUtils.isNotBlank(invoiceInfo.getStoreCode())) {
			String storeCode = this.selfPrintService.queryStoreCodeByOrderCode(invoiceInfo.getOrderCodes().get(0));
			if (StringUtils.isNotBlank(storeCode)) {
				invoiceInfo.setStoreCode(storeCode);
			}
		}
		return invoiceFeignClient.requestBillingAcpp(invoiceInfo);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName queryInvoiceRecord
	 * @description 查询发票记录
	 * <AUTHOR>
	 * @date 2021/04/01 17:12
	 */
	@PostMapping("/queryInvoiceRecord")
	public ResultEntity queryInvoiceRecord(@RequestBody ShoppingOrderInvoice invoice) {
		invoice.setIsRed(0);
		return shoppingOrderInvoiceController.queryInvoiceRecord(invoice);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName queryInvoiceResult
	 * @description 开票结果查询
	 * <AUTHOR>
	 * @date 2021/04/01 17:55
	 */
	@PostMapping("/queryInvoiceResult")
	@ResponseBody
	public ResultEntity queryInvoiceResult(@RequestBody NuoNuoElecronicInvoiceInfo invoiceInfo) {
		Map<String, Object> obj = this.selfPrintService.queryInvoiceResult(invoiceInfo);
		if (CollectionUtils.isNotEmpty(obj)) {
			return this.success(obj);
		}
		return invoiceFeignClient.queryInvoiceResult(invoiceInfo);
	}

	/**
	 * @param invoice
	 * @return com.besttop.common.model.ResultEntity
	 * @methodName sendEmail
	 * @description 发邮件
	 * <AUTHOR>
	 * @date 2021/04/01 18:58
	 */
	@PostMapping("/sendEmail")
	@ResponseBody
	public ResultEntity sendEmail(@RequestBody Email email) {
		return invoiceFeignClient.sendEmail(email);
	}
	
//	@PostMapping("/testKey")
//	@ResponseBody
//	public ResultEntity testKey(@RequestBody Map<String, String> param) {
//		String result = this.getRedisUtil.getSystemName(param.get("key"));
//		log.info("----key.value :  {}",result);
//		return this.success(result);
//	}
}

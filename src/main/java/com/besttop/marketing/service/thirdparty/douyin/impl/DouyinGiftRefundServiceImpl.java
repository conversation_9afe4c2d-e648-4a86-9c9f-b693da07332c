package com.besttop.marketing.service.thirdparty.douyin.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinGiftRefundRecordMapper;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinGiftVerificationRecordMapper;
import com.besttop.marketing.model.manual.ManualGiftRecord;
import com.besttop.marketing.model.thirdparty.douyin.*;
import com.besttop.marketing.service.manual.ManualGiftRecordService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinGiftRefundService;
import com.besttop.mybatis.utils.PrimaryKeyUtil;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 抖音礼品券退款服务实现类
 * 负责处理抖音礼品券的退款业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DouyinGiftRefundServiceImpl implements DouyinGiftRefundService {
    
    private final DouyinCouponService douyinCouponService;
    private final ManualGiftRecordService manualGiftRecordService;
    private final DouyinGiftRefundRecordMapper refundRecordMapper;
    private final DouyinGiftVerificationRecordMapper verificationRecordMapper;
    private final PrimaryKeyUtil primaryKeyUtil;
    
    @Autowired
    public DouyinGiftRefundServiceImpl(
            DouyinCouponService douyinCouponService,
            ManualGiftRecordService manualGiftRecordService,
            DouyinGiftRefundRecordMapper refundRecordMapper,
            DouyinGiftVerificationRecordMapper verificationRecordMapper,
            PrimaryKeyUtil primaryKeyUtil) {
        this.douyinCouponService = douyinCouponService;
        this.manualGiftRecordService = manualGiftRecordService;
        this.refundRecordMapper = refundRecordMapper;
        this.verificationRecordMapper = verificationRecordMapper;
        this.primaryKeyUtil = primaryKeyUtil;
    }
    
    @Override
    public Map<String, Object> processUnverifiedRefund(String couponCode, String refundId) {
        log.info("处理未核销券退款: couponCode={}, refundId={}", couponCode, refundId);
        
        try {
            // 1. 查询券信息
            List<DouyinCoupon> coupons = douyinCouponService.findByCouponCode(couponCode);
            if (coupons == null || coupons.isEmpty()) {
                return buildErrorResponse("券码不存在");
            }
            DouyinCoupon coupon = coupons.get(0);
            
            // 2. 验证券状态
            if (!DouyinCoupon.STATUS_UNUSED.equals(coupon.getCouponStatus())) {
                return buildErrorResponse("券已使用，无法退款");
            }
            
            // 3. 调用现有退款服务
            Map<String, Object> refundResult = douyinCouponService.processRefund(
                coupon.getDouyinOrderId(), refundId);
            
            if (!(Boolean) refundResult.get("success")) {
                return refundResult;
            }
            
            // 4. 创建礼品退款记录
            createGiftRefundRecord(coupon, refundId, DouyinGiftRefundRecord.TYPE_UNVERIFIED, 
                                 DouyinGiftRefundRecord.SCENARIO_DIRECT_REFUND, null);
            
            log.info("未核销券退款处理成功: couponCode={}", couponCode);
            return buildSuccessResponse("退款处理成功");
            
        } catch (Exception e) {
            log.error("处理未核销券退款异常: {}", couponCode, e);
            return buildErrorResponse("退款处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> syncVerifiedRefundInfo(String couponCode, String refundId, 
                                                      Map<String, Object> refundInfo) {
        log.info("同步已核销券退款信息: couponCode={}, refundId={}", couponCode, refundId);
        
        try {
            // 1. 查询券信息
            List<DouyinCoupon> coupons = douyinCouponService.findByCouponCode(couponCode);
            if (coupons == null || coupons.isEmpty()) {
                return buildErrorResponse("券码不存在");
            }
            DouyinCoupon coupon = coupons.get(0);
            
            // 2. 验证券已核销
            if (!DouyinCoupon.STATUS_USED.equals(coupon.getCouponStatus())) {
                return buildErrorResponse("券未核销，无法同步退款信息");
            }
            
            // 3. 查询核销记录
            DouyinGiftVerificationRecord verificationRecord = 
                verificationRecordMapper.selectByCouponCode(couponCode);
            if (verificationRecord == null) {
                return buildErrorResponse("核销记录不存在");
            }
            
            // 4. 处理礼品发放单退款
            if (StringUtils.hasText(verificationRecord.getGiftRecordId())) {
                handleGiftRecordRefund(verificationRecord.getGiftRecordId(), refundInfo);
            }
            
            // 5. 更新券状态
            coupon.setCouponStatus(DouyinCoupon.STATUS_REFUNDED);
            coupon.setRefundId(refundId);
            coupon.setRefundCompleteTime(new Date());
            douyinCouponService.updateById(coupon);
            
            // 6. 创建礼品退款记录
            createGiftRefundRecord(coupon, refundId, DouyinGiftRefundRecord.TYPE_VERIFIED, 
                                 DouyinGiftRefundRecord.SCENARIO_INFO_SYNC, refundInfo);
            
            log.info("已核销券退款信息同步成功: couponCode={}", couponCode);
            return buildSuccessResponse("退款信息同步成功");
            
        } catch (Exception e) {
            log.error("同步已核销券退款信息异常: {}", couponCode, e);
            return buildErrorResponse("退款信息同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理礼品发放单退款
     */
    private void handleGiftRecordRefund(String giftRecordId, Map<String, Object> refundInfo) {
        ManualGiftRecord giftRecord = manualGiftRecordService.getById(giftRecordId);
        if (giftRecord == null) {
            log.warn("礼品发放单不存在: {}", giftRecordId);
            return;
        }
        
        String giftType = giftRecord.getGiftType();
        String currentStatus = giftRecord.getStatus();
        
        log.info("处理礼品发放单退款: giftRecordId={}, giftType={}, status={}", 
                giftRecordId, giftType, currentStatus);
        
        switch (currentStatus) {
            case ManualGiftRecord.STATUS_PENDING:
                // 待发放状态，直接取消
                giftRecord.setStatus(ManualGiftRecord.STATUS_CANCELLED);
                giftRecord.setReason("抖音退款取消发放");
                break;
                
            case ManualGiftRecord.STATUS_GRANTED:
                // 已发放状态，需要回收处理
                processGrantedGiftRefund(giftRecord, giftType, refundInfo);
                break;
                
            case ManualGiftRecord.STATUS_SHIPPED:
                // 已发货状态，需要特殊处理
                processShippedGiftRefund(giftRecord, giftType, refundInfo);
                break;
                
            default:
                log.warn("礼品发放单状态不支持退款: giftRecordId={}, status={}", 
                        giftRecordId, currentStatus);
                return;
        }
        
        manualGiftRecordService.updateById(giftRecord);
    }
    
    /**
     * 处理已发放礼品的退款
     */
    private void processGrantedGiftRefund(ManualGiftRecord giftRecord, String giftType, 
                                         Map<String, Object> refundInfo) {
        switch (giftType) {
            case "A":
                // 电子币退款：调用客户账户服务扣减余额
                processElectronicCoinRefund(giftRecord, refundInfo);
                break;
            case "B":
                // 自营商品退款：调用库存服务回收库存
                processSelfOperatedProductRefund(giftRecord, refundInfo);
                break;
            case "C":
                // 联营商品退款：通知供应商处理
                processJointVentureProductRefund(giftRecord, refundInfo);
                break;
        }
        
        giftRecord.setStatus(ManualGiftRecord.STATUS_REFUNDED);
        giftRecord.setReason("抖音客服/来客系统退款");
    }
    
    /**
     * 处理已发货礼品的退款
     */
    private void processShippedGiftRefund(ManualGiftRecord giftRecord, String giftType, 
                                         Map<String, Object> refundInfo) {
        log.info("处理已发货礼品退款: giftRecordId={}, giftType={}", giftRecord.getId(), giftType);
        
        // 已发货商品需要特殊处理，比如创建退货单等
        // 这里简化处理，实际应根据业务需求实现
        giftRecord.setStatus(ManualGiftRecord.STATUS_REFUNDED);
        giftRecord.setReason("抖音退款-已发货商品退款");
        
        // TODO: 实现具体的退货处理逻辑
        log.warn("已发货商品退款处理待完善: giftRecordId={}", giftRecord.getId());
    }
    
    /**
     * 处理电子币退款
     */
    private void processElectronicCoinRefund(ManualGiftRecord giftRecord, Map<String, Object> refundInfo) {
        log.info("处理电子币退款: giftRecordId={}",
                giftRecord.getId());
        
        // TODO: 调用客户账户服务扣减电子币余额
        // CustomerAccountService accountService = SpringContext.getBean(CustomerAccountService.class);
        // accountService.deductElectronicCoin(giftRecord.getCustomerPhone(), giftRecord.getAmount());
        
        log.info("电子币退款处理完成: giftRecordId={}", giftRecord.getId());
    }
    
    /**
     * 处理自营商品退款
     */
    private void processSelfOperatedProductRefund(ManualGiftRecord giftRecord, Map<String, Object> refundInfo) {
        log.info("处理自营商品退款: giftRecordId={}",
                giftRecord.getId());
        
        // TODO: 调用库存服务回收库存
        // InventoryService inventoryService = SpringContext.getBean(InventoryService.class);
        // inventoryService.recoverStock(giftRecord.getProductCode(), giftRecord.getQuantity(), giftRecord.getStoreCode());
        
        log.info("自营商品退款处理完成: giftRecordId={}", giftRecord.getId());
    }
    
    /**
     * 处理联营商品退款
     */
    private void processJointVentureProductRefund(ManualGiftRecord giftRecord, Map<String, Object> refundInfo) {
        log.info("处理联营商品退款: giftRecordId={}",
                giftRecord.getId());
        
        // TODO: 发送供应商退款通知
        // SupplierNotificationService notificationService = SpringContext.getBean(SupplierNotificationService.class);
        // notificationService.sendRefundNotification(giftRecord, refundInfo);
        
        log.info("联营商品退款处理完成: giftRecordId={}", giftRecord.getId());
    }
    

    
    @Override
    public List<DouyinGiftRefundRecord> getRefundRecords(String couponCode) {
        log.info("根据券码查询退款记录: couponCode={}", couponCode);
        
        if (StringUtils.isEmpty(couponCode)) {
            throw new IllegalArgumentException("券码不能为空");
        }
        
        try {
            List<DouyinGiftRefundRecord> records = refundRecordMapper.selectByCouponCode(couponCode);
            log.info("券码 {} 查询到退款记录数: {}", couponCode, records.size());
            return records;
            
        } catch (Exception e) {
            log.error("根据券码查询退款记录异常: {}", couponCode, e);
            throw new RuntimeException("查询退款记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建礼品退款记录
     */
    private void createGiftRefundRecord(DouyinCoupon coupon, String refundId, 
                                       String refundType, String refundScenario, 
                                       Map<String, Object> refundInfo) {
        DouyinGiftRefundRecord record = new DouyinGiftRefundRecord();
        record.setId(primaryKeyUtil.generateKey("t_douyin_gift_refund_record", "DGR", 10));
        record.setRefundId(refundId);
        record.setCouponCode(coupon.getCouponCode());
        record.setCouponId(coupon.getId());
        record.setRefundType(refundType);
        record.setRefundScenario(refundScenario);
        // record.setGiftRecordId(coupon.getGiftRecordId()); // 需要扩展DouyinCoupon实体
        record.setRefundStatus(DouyinGiftRefundRecord.STATUS_SUCCESS);
        record.setRefundRequestTime(new Date());
        record.setRefundCompleteTime(new Date());
        
        if (refundInfo != null) {
            record.setRefundAmount((BigDecimal) refundInfo.get("refund_amount"));
            record.setRefundReason((String) refundInfo.get("refund_reason"));
            record.setSyncFromSource((String) refundInfo.get("source"));
            record.setRequestPayloadJson(JSON.toJSONString(refundInfo, SerializerFeature.WriteMapNullValue));
        }
        
        record.setCreateBy("DOUYIN_GIFT_REFUND");
        refundRecordMapper.insert(record);
        
        log.info("创建礼品退款记录成功: recordId={}, couponCode={}, refundType={}", 
                record.getId(), coupon.getCouponCode(), refundType);
    }
    
    private Map<String, Object> buildSuccessResponse(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        return result;
    }
    
    private Map<String, Object> buildErrorResponse(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        return result;
    }
}
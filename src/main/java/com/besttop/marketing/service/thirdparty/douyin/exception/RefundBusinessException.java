package com.besttop.marketing.service.thirdparty.douyin.exception;

import lombok.Getter;

/**
 * 退款业务异常
 * 职责：处理退款业务逻辑中的异常情况
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-23
 */
@Getter
public class RefundBusinessException extends RuntimeException {
    
    /** 错误代码 */
    private final String errorCode;
    
    /**
     * 构造函数
     * @param message 异常信息
     */
    public RefundBusinessException(String message) {
        super(message);
        this.errorCode = "REFUND_BUSINESS_ERROR";
    }
    
    /**
     * 构造函数
     * @param errorCode 错误代码
     * @param message 异常信息
     */
    public RefundBusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     * @param message 异常信息
     * @param cause 原因异常
     */
    public RefundBusinessException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "REFUND_BUSINESS_ERROR";
    }
}
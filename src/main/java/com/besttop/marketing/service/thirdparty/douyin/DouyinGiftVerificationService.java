package com.besttop.marketing.service.thirdparty.douyin;

import com.besttop.marketing.dto.gift.DouyinGiftCancelRequest;
import com.besttop.marketing.dto.gift.DouyinGiftCancelResult;
import com.besttop.marketing.model.manual.ManualGiftRecord;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponRule;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponVerifyResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinGiftVerificationRecord;

import java.util.List;
import java.util.Map;

/**
 * 抖音礼品券核销服务接口
 * 负责处理抖音礼品券的核销业务逻辑
 */
public interface DouyinGiftVerificationService {
    
    /**
     * 查询礼品核销记录
     * @param params 查询参数
     * @return 核销记录列表
     */
    List<DouyinGiftVerificationRecord> listVerificationRecords(Map<String, Object> params);
    
    /**
     * 根据券码查询核销记录
     * @param couponCode 券码
     * @return 核销记录
     */
    DouyinGiftVerificationRecord getVerificationRecord(String couponCode);

    /**
     * 撤销礼品券核销
     * @param request 撤销请求参数
     * @return 撤销结果
     */
    DouyinGiftCancelResult cancelGiftVerification(DouyinGiftCancelRequest request);

    /**
     * 礼品券核销（接收已验证的券和规则，避免重复验证）
     * @param coupon 已验证的券信息
     * @param rule 已验证的券规则
     * @param storeCode 门店编码
     * @param staffId 操作员ID
     * @param params 附加参数
     * @return 核销结果
     */
    DouyinGiftVerifyResult verifyGiftCouponWithValidatedData(DouyinCoupon coupon, DouyinCouponRule rule,
                                                            String storeCode, String staffId,
                                                            Map<String, Object> params);

    List<DouyinGiftVerificationRecord> listCustomerGiftVerifications(String customerCode);

    /**
     * 礼品券核销结果对象
     */
    class DouyinGiftVerifyResult {
        private boolean success;
        private String message;
        private DouyinCoupon couponInfo;
        private DouyinCouponRule couponRule;
        private String giftType;
        private DouyinGiftVerificationRecord verificationRecord;
//        private ManualGiftRecord giftRecord;
        private String verificationType;
        private Map<String, Object> additionalInfo;
        
        public static DouyinGiftVerifyResult success(DouyinCouponVerifyResult couponResult, 
                                                    DouyinGiftVerificationRecord verificationRecord,
                                                    ManualGiftRecord giftRecord) {
            DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
            result.setSuccess(true);
            result.setMessage("礼品券核销成功");
            result.setCouponInfo(couponResult.getCouponInfo());
            result.setCouponRule(couponResult.getCouponRule());
            result.setVerificationRecord(verificationRecord);
//            result.setGiftRecord(giftRecord);
            result.setGiftType(verificationRecord.getGiftType());
            result.setVerificationType("GIFT_" + verificationRecord.getGiftType());
            return result;
        }
        
        public static DouyinGiftVerifyResult fail(String message) {
            DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
            result.setSuccess(false);
            result.setMessage(message);
            return result;
        }
        
        public static DouyinGiftVerifyResult fromCouponResult(DouyinCouponVerifyResult couponResult) {
            DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
            result.setSuccess(couponResult.isSuccess());
            result.setMessage(couponResult.getMessage());
            result.setCouponInfo(couponResult.getCouponInfo());
            result.setCouponRule(couponResult.getCouponRule());
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public DouyinCoupon getCouponInfo() { return couponInfo; }
        public void setCouponInfo(DouyinCoupon couponInfo) { this.couponInfo = couponInfo; }
        
        public DouyinCouponRule getCouponRule() { return couponRule; }
        public void setCouponRule(DouyinCouponRule couponRule) { this.couponRule = couponRule; }
        
        public String getGiftType() { return giftType; }
        public void setGiftType(String giftType) { this.giftType = giftType; }
        
        public DouyinGiftVerificationRecord getVerificationRecord() { return verificationRecord; }
        public void setVerificationRecord(DouyinGiftVerificationRecord verificationRecord) { 
            this.verificationRecord = verificationRecord; 
        }
        
//        public ManualGiftRecord getGiftRecord() { return giftRecord; }
//        public void setGiftRecord(ManualGiftRecord giftRecord) { this.giftRecord = giftRecord; }
        
        public String getVerificationType() { return verificationType; }
        public void setVerificationType(String verificationType) { this.verificationType = verificationType; }
        
        public Map<String, Object> getAdditionalInfo() { return additionalInfo; }
        public void setAdditionalInfo(Map<String, Object> additionalInfo) { this.additionalInfo = additionalInfo; }
    }
}
package com.besttop.marketing.service.thirdparty.douyin.context;

import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinPreOrder;
import com.besttop.marketing.model.thirdparty.douyin.DouyinRefundAudit;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 退款请求处理上下文
 * 职责：封装请求处理过程中的所有状态信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-23
 */
@Data
@Builder
public class RefundRequestContext {
    
    // ================================
    // 基础请求信息
    // ================================
    
    /** 售后ID */
    private String afterSaleId;
    
    /** 订单ID */
    private String orderId;
    
    /** 券码列表 */
    private List<String> couponCodes;
    
    /** 原始请求体 */
    private Map<String, Object> originalRequest;
    
    // ================================
    // 业务对象
    // ================================
    
    /** 订单信息 */
    private DouyinPreOrder order;
    
    /** 券码列表 */
    private List<DouyinCoupon> coupons;
    
    /** 已存在的退款审核记录 */
    private DouyinRefundAudit existingRefund;
    
    // ================================
    // 处理状态
    // ================================
    
    /** 缓存键 */
    private String cacheKey;
    
    /** 退款场景类型 */
    private RefundScenarioType scenarioType;
    
    /** 处理开始时间 */
    private long startTime;
    
    // ================================
    // 场景类型枚举
    // ================================
    
    /**
     * 退款处理场景枚举
     */
    public enum RefundScenarioType {
        /** 正常退款 */
        NORMAL_REFUND("正常退款"),
        
        /** 补码场景 */
        SUPPLEMENT_CODE("补码场景"),
        
        /** 已处理 */
        ALREADY_PROCESSED("已处理"),
        
        /** 订单未发券 */
        ORDER_NOT_ISSUED("订单未发券");
        
        private final String description;
        
        RefundScenarioType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
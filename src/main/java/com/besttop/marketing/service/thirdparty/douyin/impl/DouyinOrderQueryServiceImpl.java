package com.besttop.marketing.service.thirdparty.douyin.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCouponMapper;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCertificateVerifyMapper;
import com.besttop.marketing.model.douyin.response.DouyinOrderDetail;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCertificateVerify;
import com.besttop.marketing.service.thirdparty.douyin.DouyinErrorCodeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抖音订单状态查询服务实现
 * 
 * <AUTHOR> @since 2024-12-25
 */
@Slf4j
@Service
public class DouyinOrderQueryServiceImpl {
    
    @Autowired
    private DouyinCouponMapper douyinCouponMapper;
    
    @Autowired
    private DouyinCertificateVerifyMapper douyinCertificateVerifyMapper;
    
    @Autowired
    private DouyinErrorCodeHandler errorCodeHandler;
    
    /**
     * 处理订单状态查询请求
     * 
     * @param requestBody 查询请求参数
     * @return 查询响应结果
     */
    public Map<String, Object> handleOrderStatusQuery(Map<String, Object> requestBody) {
        try {
            // 1. 参数校验
            String orderId = (String) requestBody.get("order_id");
            if (StringUtils.isEmpty(orderId)) {
                return errorCodeHandler.buildOrderQueryErrorResponse("ORDER_NOT_FOUND", "订单ID不能为空");
            }
            
            log.info("[订单状态查询] 开始查询订单: {}", orderId);
            
            // 2. 查询订单相关券码
            List<DouyinCoupon> coupons = douyinCouponMapper.selectList(
                new QueryWrapper<DouyinCoupon>().eq("douyin_order_id", orderId)
            );
            
            if (coupons.isEmpty()) {
                return errorCodeHandler.buildOrderQueryErrorResponse("ORDER_NOT_FOUND", "未查询到抖音订单id");
            }
            
            // 3. 构建订单详情
            DouyinOrderDetail orderDetail = buildOrderDetail(orderId, coupons);
            
            // 4. 构建成功响应
            Map<String, Object> result = new HashMap<>();
            result.put("order_detail", orderDetail);
            
            return errorCodeHandler.buildSuccessResponse(result);
            
        } catch (Exception e) {
            log.error("[订单状态查询] 查询异常: {}", requestBody.get("order_id"), e);
            return errorCodeHandler.buildOrderQueryErrorResponse("SYSTEM_ERROR_RETRY", "系统异常，可重试");
        }
    }
    
    /**
     * 构建订单详情
     */
    private DouyinOrderDetail buildOrderDetail(String orderId, List<DouyinCoupon> coupons) {
        DouyinOrderDetail orderDetail = new DouyinOrderDetail();
        orderDetail.setOrderId(orderId);
        
        // 获取第一个券的信息作为订单基础信息
        DouyinCoupon firstCoupon = coupons.get(0);
        orderDetail.setOutOrderId(firstCoupon.getThirdOrderId());
        
        // 计算订单状态 (简化处理，实际需要根据业务规则)
        orderDetail.setOrderStatus(calculateOrderStatus(coupons));
        
        // 构建券码列表
        List<DouyinOrderDetail.CodeRecord> codes = buildCodeRecords(coupons);
        orderDetail.setCodes(codes);
        
        // 统计数量
        orderDetail.setTotalQuantity(coupons.size());
        orderDetail.setUsedQuantity((int) coupons.stream()
            .filter(c -> DouyinCoupon.STATUS_USED.equals(c.getCouponStatus())).count());
        orderDetail.setRefundQuantity((int) coupons.stream()
            .filter(c -> DouyinCoupon.STATUS_REFUNDED.equals(c.getCouponStatus())).count());
        
        return orderDetail;
    }
    
    /**
     * 计算订单状态
     */
    private Integer calculateOrderStatus(List<DouyinCoupon> coupons) {
        // 简化状态计算逻辑
        boolean hasUsed = coupons.stream().anyMatch(c -> DouyinCoupon.STATUS_USED.equals(c.getCouponStatus()));
        boolean hasRefunded = coupons.stream().anyMatch(c -> DouyinCoupon.STATUS_REFUNDED.equals(c.getCouponStatus()));
        boolean allUsedOrRefunded = coupons.stream().allMatch(c -> 
            DouyinCoupon.STATUS_USED.equals(c.getCouponStatus()) || 
            DouyinCoupon.STATUS_REFUNDED.equals(c.getCouponStatus()));
        
        if (allUsedOrRefunded) {
            return 1; // 已完成
        } else if (hasUsed || hasRefunded) {
            return 201; // 可使用（部分使用）
        } else {
            return 200; // 已支付
        }
    }
    
    /**
     * 构建券码记录列表
     */
    private List<DouyinOrderDetail.CodeRecord> buildCodeRecords(List<DouyinCoupon> coupons) {
        List<DouyinOrderDetail.CodeRecord> codes = new ArrayList<>();
        
        for (DouyinCoupon coupon : coupons) {
            DouyinOrderDetail.CodeRecord codeRecord = new DouyinOrderDetail.CodeRecord();
            codeRecord.setValue(coupon.getCouponCode());
            codeRecord.setType("code"); // 三方码
            
            // 设置核销状态
            if (DouyinCoupon.STATUS_USED.equals(coupon.getCouponStatus())) {
                codeRecord.setFulfilStatus(1); // 已核销
                codeRecord.setFulfilTime(coupon.getVerificationTime() != null ? 
                    coupon.getVerificationTime().getTime() / 1000 : null);
            } else if (DouyinCoupon.STATUS_REFUNDED.equals(coupon.getCouponStatus())) {
                codeRecord.setFulfilStatus(3); // 已退款
            } else {
                codeRecord.setFulfilStatus(2); // 未核销
            }
            
            codeRecord.setVerifyToken(coupon.getVerifyToken());
            codeRecord.setPoiId(coupon.getPoiId());
            
            codes.add(codeRecord);
        }
        
        return codes;
    }
    
    /**
     * 处理券码查询请求
     * 
     * @param requestBody 查询请求参数
     * @return 查询响应结果
     */
    public Map<String, Object> handleCodeQuery(Map<String, Object> requestBody) {
        try {
            // 1. 参数校验
            String code = (String) requestBody.get("code");
            if (StringUtils.isEmpty(code)) {
                return errorCodeHandler.buildOrderQueryErrorResponse("CODE_NOT_FOUND", "券码不能为空");
            }
            
            log.info("[券码查询] 开始查询券码: {}", code);
            
            // 2. 查询券码信息
            DouyinCoupon coupon = douyinCouponMapper.selectByCouponCode(code);
            if (coupon == null) {
                return errorCodeHandler.buildOrderQueryErrorResponse("CODE_NOT_FOUND", "未查询到相关券码code");
            }
            
            // 3. 构建券码详情
            List<DouyinCoupon> coupons = new ArrayList<>();
            coupons.add(coupon);
            DouyinOrderDetail orderDetail = buildOrderDetail(coupon.getDouyinOrderId(), coupons);
            
            // 4. 构建成功响应
            Map<String, Object> result = new HashMap<>();
            result.put("order_detail", orderDetail);
            
            return errorCodeHandler.buildSuccessResponse(result);
            
        } catch (Exception e) {
            log.error("[券码查询] 查询异常: {}", requestBody.get("code"), e);
            return errorCodeHandler.buildOrderQueryErrorResponse("SYSTEM_ERROR_RETRY", "系统异常，可重试");
        }
    }
}

package com.besttop.marketing.service.thirdparty.douyin.exception;

/**
 * 退款参数校验异常
 * 职责：处理退款请求参数校验失败的异常情况
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-23
 */
public class RefundValidationException extends RuntimeException {
    
    /**
     * 构造函数
     * @param message 异常信息
     */
    public RefundValidationException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * @param message 异常信息
     * @param cause 原因异常
     */
    public RefundValidationException(String message, Throwable cause) {
        super(message, cause);
    }
}
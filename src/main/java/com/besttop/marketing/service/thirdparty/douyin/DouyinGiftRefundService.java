package com.besttop.marketing.service.thirdparty.douyin;

import com.besttop.marketing.model.thirdparty.douyin.DouyinGiftRefundRecord;

import java.util.List;
import java.util.Map;

/**
 * 抖音礼品券退款服务接口
 * 负责处理抖音礼品券的退款业务逻辑
 */
public interface DouyinGiftRefundService {
    
    /**
     * 处理未核销券退款
     * @param couponCode 券码
     * @param refundId 退款ID
     * @return 处理结果
     */
    Map<String, Object> processUnverifiedRefund(String couponCode, String refundId);
    
    /**
     * 同步已核销券退款信息
     * @param couponCode 券码
     * @param refundId 退款ID
     * @param refundInfo 退款信息
     * @return 处理结果
     */
    Map<String, Object> syncVerifiedRefundInfo(String couponCode, String refundId, Map<String, Object> refundInfo);
    
    /**
     * 查询退款记录列表
     * @param params 查询参数
     * @return 退款记录列表
     */
    List<DouyinGiftRefundRecord> listRefundRecords(Map<String, Object> params);
    
    /**
     * 根据券码查询退款记录
     * @param couponCode 券码
     * @return 退款记录列表
     */
    List<DouyinGiftRefundRecord> getRefundRecords(String couponCode);
}
package com.besttop.marketing.service.thirdparty.douyin.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.besttop.marketing.dto.gift.DouyinGiftCancelRequest;
import com.besttop.marketing.dto.gift.DouyinGiftCancelResult;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinGiftVerificationRecordMapper;
import com.besttop.marketing.model.PrefixEncoding;
import com.besttop.marketing.model.manual.ManualGiftRecord;
import com.besttop.marketing.model.thirdparty.douyin.*;
import com.besttop.marketing.model.thirdparty.douyin.param.CancelVerificationRequest;
import com.besttop.marketing.dto.gift.ManualGiftRecordCreateDTO;
import com.besttop.marketing.service.manual.ManualGiftRecordService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinGiftVerificationService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSyncService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponService;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.util.CodeUtils;
import com.besttop.mybatis.utils.PrimaryKeyUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 抖音礼品券核销服务实现类
 * 负责处理抖音礼品券的核销业务逻辑
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DouyinGiftVerificationServiceImpl extends ServiceImpl<DouyinGiftVerificationRecordMapper, DouyinGiftVerificationRecord>
        implements DouyinGiftVerificationService {
    
    private final ManualGiftRecordService manualGiftRecordService;
    private final DouyinGiftVerificationRecordMapper verificationRecordMapper;
    private final DouyinSyncService douyinSyncService;
    private final DouyinCouponService douyinCouponService;
    private final PrimaryKeyUtil primaryKeyUtil;
    private final CodeUtils codeUtils;
    private final LoginCacheUtil loginCacheUtil;

    @Autowired
    public DouyinGiftVerificationServiceImpl(
            ManualGiftRecordService manualGiftRecordService,
            DouyinGiftVerificationRecordMapper verificationRecordMapper,
            DouyinSyncService douyinSyncService,
            DouyinCouponService douyinCouponService,
            PrimaryKeyUtil primaryKeyUtil,
            CodeUtils codeUtils,
            LoginCacheUtil loginCacheUtil) {
        this.manualGiftRecordService = manualGiftRecordService;
        this.verificationRecordMapper = verificationRecordMapper;
        this.douyinSyncService = douyinSyncService;
        this.douyinCouponService = douyinCouponService;
        this.primaryKeyUtil = primaryKeyUtil;
        this.codeUtils = codeUtils;
        this.loginCacheUtil = loginCacheUtil;
    }

    /**
     * 确定礼品类型
     */
    private String determineGiftType(DouyinCouponRule rule) {
        // 根据券规则的 groupon_type 确定礼品类型
        String grouponType = rule.getGrouponType();
        if ("erp:douyin_coupon_groupon_type:1".equals(grouponType)) {
            return "A"; // 代金券对应电子币
        } else if ("erp:douyin_coupon_groupon_type:2".equals(grouponType)) {
            // todo 根据商品属性进一步判断B类还是C类
            return "B";
        }
        return "B"; // 默认B类
    }
    
    /**
     * 创建礼品核销记录
     */
    private DouyinGiftVerificationRecord createVerificationRecord(
            DouyinCoupon coupon, DouyinCouponRule rule, String giftType,
            String storeCode, String staffId, DouyinCouponVerifyResult couponResult) {
        
        DouyinGiftVerificationRecord record = new DouyinGiftVerificationRecord();
        record.setCouponCode(coupon.getCouponCode());
        record.setCouponId(coupon.getId());
        record.setDouyinOrderId(coupon.getDouyinOrderId());
        record.setVerificationTime(new Date());
        record.setStoreCode(storeCode);
        record.setStaffId(staffId);
        record.setGiftType(giftType);
        record.setVerificationStatus(DouyinGiftVerificationRecord.STATUS_SUCCESS);
        record.setSyncStatus(DouyinGiftVerificationRecord.SYNC_STATUS_PENDING);
        record.setCreateBy("DOUYIN_GIFT_VERIFY");
        
        // 设置礼品发放状态为待发放
        record.setGiftIssueStatus(DouyinGiftVerificationRecord.GIFT_ISSUE_STATUS_PENDING);
        
        // 设置顾客信息
        record.setCustomerCode(coupon.getCustomerCode());
        record.setPhone(coupon.getPhone());
        
        verificationRecordMapper.insert(record);
        
        log.info("创建礼品核销记录成功: recordId={}, couponCode={}, giftType={}, 礼品发放状态={}, 顾客手机号={}", 
                record.getId(), coupon.getCouponCode(), giftType, record.getGiftIssueStatus(), record.getPhone());
        
        return record;
    }
    
    /**
     * 创建待发放礼品记录
     */
    private ManualGiftRecord createPendingGiftRecord(
            DouyinCoupon coupon, DouyinCouponRule rule, String giftType,
            DouyinGiftVerificationRecord verificationRecord, Map<String, Object> params) {

        // 构建礼品发放参数
        ManualGiftRecordCreateDTO createDTO = buildGiftRecordDTO(
            coupon, rule, giftType, verificationRecord, params);

        // 验证DTO数据
        createDTO.validate();
        createDTO.setDefaults();

        // 创建礼品发放记录
        ManualGiftRecord giftRecord = new ManualGiftRecord();

        // 设置基础信息
        giftRecord.setCode(primaryKeyUtil.generateKey("manual_gift_record", PrefixEncoding.MANUAL_GIFT_RECORD, codeUtils.findLenth(CommonEnums.BILL_CODE_TYPE.getCode(), null)));
        giftRecord.setTelephone(createDTO.getCustomerPhone());
        giftRecord.setStoreCode(createDTO.getStoreCode());
        giftRecord.setGiftType(createDTO.getGiftType());
        giftRecord.setType(CommonEnums.SCHEDULE_GIFT_RECORD_TYPE__WEI_FA_FANG.getCode());
        giftRecord.setTag("grant"); // 发放标识
        giftRecord.setIsFlag(0); // 待发放状态
        giftRecord.setIsRecycle(0);
        giftRecord.setCreateBy("DOUYIN_GIFT");

        // 根据礼品类型设置具体参数
        switch (giftType) {
            case "A":
//                giftRecord.setAmount(createDTO.getAmount());
//                giftRecord.setDescription("抖音代金券核销发放电子币");
                break;
            case "B":
            case "C":
                giftRecord.setSkuCode(createDTO.getProductCode());
                giftRecord.setSkuName(createDTO.getProductName());
                break;

        }

        // 设置抖音特有字段
        giftRecord.setDouyinCouponCode(coupon.getCouponCode());
        giftRecord.setDouyinVerificationRecordId(verificationRecord.getId());
        giftRecord.setGiftType(CommonEnums.MANUAL_GIFT_RECORD_GIFT_TYPE_D.getCode()); // 设置为抖音券类型

        // 保存记录
        manualGiftRecordService.save(giftRecord);

        log.info("创建待发放礼品记录成功: giftRecordId={}, couponCode={}, giftType={}",
                giftRecord.getId(), coupon.getCouponCode(), giftType);

        return giftRecord;
    }
    
    /**
     * 构建礼品发放单创建参数
     */
    private ManualGiftRecordCreateDTO buildGiftRecordDTO(
            DouyinCoupon coupon, DouyinCouponRule rule, String giftType,
            DouyinGiftVerificationRecord verificationRecord, Map<String, Object> params) {
        
        ManualGiftRecordCreateDTO dto = new ManualGiftRecordCreateDTO();
        
        // 基础信息
        dto.setCustomerPhone(coupon.getPhone());
        dto.setStoreCode(verificationRecord.getStoreCode());
        dto.setOperatorId(verificationRecord.getStaffId());
        dto.setSource("DOUYIN_GIFT_VERIFY");
        dto.setSourceOrderId(coupon.getDouyinOrderId());
        
        // 根据礼品类型设置具体参数
        switch (giftType) {
            case "A":
                setupGiftTypeA(dto, coupon, rule, params);
                break;
            case "B":
                setupGiftTypeB(dto, coupon, rule, params);
                break;
            case "C":
                setupGiftTypeC(dto, coupon, rule, params);
                break;
        }
        
        return dto;
    }
    
    private void setupGiftTypeA(ManualGiftRecordCreateDTO dto, DouyinCoupon coupon, 
                               DouyinCouponRule rule, Map<String, Object> params) {
        dto.setGiftType("A");
        dto.setAmount(rule.getPayAmount()); // 代金券金额转为电子币
        dto.setAccountType("ELECTRONIC_COIN");
        dto.setDescription("抖音代金券核销发放电子币");
    }
    
    private void setupGiftTypeB(ManualGiftRecordCreateDTO dto, DouyinCoupon coupon,
                               DouyinCouponRule rule, Map<String, Object> params) {
        dto.setGiftType("B");
//        dto.setProductCode(rule.getProductCode());
//        dto.setProductName(rule.getProductName());
        dto.setQuantity(1);
        dto.setDeliveryType("LOGISTICS");
        dto.setDescription("抖音礼品券核销发放自营商品");
    }
    
    private void setupGiftTypeC(ManualGiftRecordCreateDTO dto, DouyinCoupon coupon,
                               DouyinCouponRule rule, Map<String, Object> params) {
        dto.setGiftType("C");
//        dto.setProductCode(rule.getProductCode());
//        dto.setProductName(rule.getProductName());
//        dto.setSupplierCode(rule.getSupplierCode());
        dto.setQuantity(1);
        dto.setDeliveryType("SUPPLIER_DIRECT");
        dto.setDescription("抖音礼品券核销发放联营商品");
    }
    


    @Override
    public DouyinGiftVerificationRecord getVerificationRecord(String couponCode) {
        return verificationRecordMapper.selectByCouponCode(couponCode);
    }

    @Override
    public DouyinGiftVerifyResult verifyGiftCouponWithValidatedData(DouyinCoupon coupon, DouyinCouponRule rule,
                                                                   String storeCode, String staffId,
                                                                   Map<String, Object> params) {
    String couponCode = coupon.getCouponCode();
    log.info("开始礼品券核销（使用已验证数据）: 券码={}, 门店={}, 操作员={}", couponCode, storeCode, staffId);
        // 1. 确定礼品类型
        String giftType = determineGiftType(rule);

        // 2. 创建一个虚拟的券核销结果（用于创建记录）
        DouyinCouponVerifyResult mockCouponResult = new DouyinCouponVerifyResult();
        mockCouponResult.setSuccess(true);
        mockCouponResult.setCouponInfo(coupon);
        mockCouponResult.setCouponRule(rule);

        // 3. 创建礼品核销记录
        DouyinGiftVerificationRecord verificationRecord = createVerificationRecord(
                coupon, rule, giftType, storeCode, staffId, mockCouponResult);

        // 4. 创建待发放的ManualGiftRecord记录
        ManualGiftRecord pendingGiftRecord = createPendingGiftRecord(
                coupon, rule, giftType, verificationRecord, params);

        // 5. 更新核销记录状态，关联礼品发放记录
        verificationRecord.setVerificationStatus("SUCCESS");
        verificationRecord.setSyncStatus("PENDING");
        verificationRecord.setGiftRecordId(pendingGiftRecord.getId());
        verificationRecordMapper.updateById(verificationRecord);

        log.info("礼品券核销成功: 券码={}, 礼品类型={}, 核销后礼品处于待发放状态, 礼品记录ID={}",
                couponCode, giftType, pendingGiftRecord.getId());

        // 构建结果对象，但不包含礼品记录信息
        DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
        result.setSuccess(true);
        result.setCouponInfo(mockCouponResult.getCouponInfo());
        result.setCouponRule(mockCouponResult.getCouponRule());
        result.setVerificationRecord(verificationRecord);
        result.setGiftType(giftType);
        result.setMessage("核销成功，礼品处于待发放状态");
        result.setVerificationType("GIFT_" + giftType);

        return result;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DouyinGiftCancelResult cancelGiftVerification(DouyinGiftCancelRequest request) {


        try {
            String storeCode = loginCacheUtil.getStoreCode();
            String staffId = loginCacheUtil.getUserCode();
            request.setStoreCode(storeCode);
            request.setStaffId(staffId);
            // 1. 参数验证
            validateCancelRequest(request);
            log.info("开始撤销礼品券核销: couponCode={}, storeCode={}, staffId={}",
                    request.getCouponCode(), request.getStoreCode(), request.getStaffId());
            // 2. 查询核销记录
            DouyinGiftVerificationRecord verificationRecord = getVerificationRecord(request.getCouponCode());
            if (verificationRecord == null) {
                return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC001", "未找到核销记录");
            }

            // 3. 查询券信息
            List<DouyinCoupon> coupons = douyinCouponService.findByCouponCode(request.getCouponCode());
            if (coupons == null || coupons.isEmpty()) {
                return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC002", "券码不存在");
            }
            DouyinCoupon coupon = coupons.get(0);

            // 4. 验证券状态
            if (!DouyinCoupon.STATUS_USED.equals(coupon.getCouponStatus())) {
                return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC003", "券未核销，无法撤销");
            }

            // 5. 检查撤销时间限制（除非强制撤销）
            if (!request.getForceCancel() && !canCancelVerification(verificationRecord)) {
                return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC004", "超过撤销时间限制（1小时）");
            }

            // 6. 调用抖音撤销核销API
            DouyinGiftCancelResult cancelResult = callDouyinCancelApi(coupon, verificationRecord, request);
            if (!cancelResult.isSuccess()) {
                return cancelResult;
            }

            // 7. 更新券状态
            updateCouponStatusAfterCancel(coupon);

            // 8. 更新核销记录状态
            updateVerificationRecordAfterCancel(verificationRecord, request);

            log.info("礼品券核销撤销成功: couponCode={}", request.getCouponCode());

            DouyinGiftCancelResult result = DouyinGiftCancelResult.success(request.getCouponCode(), "撤销成功");
            result.setVerificationRecordId(verificationRecord.getId());
            result.setGiftType(verificationRecord.getGiftType());
            result.setCouponStatus(DouyinCoupon.STATUS_UNUSED);
            result.setDouyinResponse(cancelResult.getDouyinResponse());

            return result;

        } catch (Exception e) {
            log.error("撤销礼品券核销异常: couponCode={}", request.getCouponCode(), e);
            return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC999", "撤销失败：" + e.getMessage());
        }
    }
    

    /**
     * 验证撤销请求参数
     */
    private void validateCancelRequest(DouyinGiftCancelRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("撤销请求不能为空");
        }
        if (request.getCouponCode() == null || request.getCouponCode().trim().isEmpty()) {
            throw new IllegalArgumentException("券码不能为空");
        }
        if (request.getStoreCode() == null || request.getStoreCode().trim().isEmpty()) {
            throw new IllegalArgumentException("门店编码不能为空");
        }

    }

    /**
     * 检查是否可以撤销核销（时间限制检查）
     */
    private boolean canCancelVerification(DouyinGiftVerificationRecord verificationRecord) {
        if (verificationRecord.getVerificationTime() == null) {
            return false;
        }

        // 检查是否超过1小时
        long verificationTime = verificationRecord.getVerificationTime().getTime();
        long currentTime = System.currentTimeMillis();
        long diffHours = (currentTime - verificationTime) / (1000 * 60 * 60);

        return diffHours < 1; // 1小时内可以撤销
    }

    /**
     * 调用抖音撤销核销API
     */
    private DouyinGiftCancelResult callDouyinCancelApi(DouyinCoupon coupon,
                                                      DouyinGiftVerificationRecord verificationRecord,
                                                      DouyinGiftCancelRequest request) {
        try {
            // 构建撤销请求
            CancelVerificationRequest cancelRequest = new CancelVerificationRequest();
            cancelRequest.setCertificateId(coupon.getDouyinCertificateId());
            cancelRequest.setVerifyId(coupon.getVerifyId());

            // 获取门店POI ID
            String poiId = getStorePoiId(request.getStoreCode());
            if (poiId == null) {
                return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC005", "未找到门店对应的抖音门店ID");
            }

            // 调用抖音撤销API
            Map<String, Object> result = douyinSyncService.cancelVerification(cancelRequest, poiId);

            if ((Boolean) result.getOrDefault("success", false)) {
                DouyinGiftCancelResult cancelResult = DouyinGiftCancelResult.success(request.getCouponCode(), "抖音API撤销成功");
                cancelResult.setDouyinResponse(result);
                return cancelResult;
            } else {
                String errorMsg = (String) result.getOrDefault("error_message", "撤销核销失败");
                // 处理特殊错误信息
                if (errorMsg.contains("超过一个小时")) {
                    errorMsg = "验券超过一个小时不可撤销";
                } else if (errorMsg.contains("无核销记录")) {
                    errorMsg = "无核销记录可撤销";
                }
                return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC006", errorMsg);
            }

        } catch (Exception e) {
            log.error("调用抖音撤销API异常: couponCode={}", request.getCouponCode(), e);
            return DouyinGiftCancelResult.fail(request.getCouponCode(), "GC007", "调用抖音API失败：" + e.getMessage());
        }
    }

    /**
     * 获取门店POI ID
     */
    private String getStorePoiId(String storeCode) {
        // 这里应该调用实际的门店服务获取POI ID
        // 暂时返回null，需要根据实际业务逻辑实现
        // 可以参考DouyinCouponRefund中的实现：daoMapper.findLifePoi(storeCode)
        try {
            // TODO: 实现获取门店POI ID的逻辑
            return "default_poi_id"; // 临时返回，实际应该查询数据库
        } catch (Exception e) {
            log.error("获取门店POI ID失败: storeCode={}", storeCode, e);
            return null;
        }
    }

    /**
     * 处理礼品发放单撤销
     */
    private void handleGiftRecordCancel(DouyinGiftVerificationRecord verificationRecord, DouyinGiftCancelRequest request) {
        if (verificationRecord.getGiftRecordId() == null) {
            log.warn("核销记录没有关联的礼品发放单: verificationRecordId={}", verificationRecord.getId());
            return;
        }

        try {
            ManualGiftRecord giftRecord = manualGiftRecordService.getById(verificationRecord.getGiftRecordId());
            if (giftRecord == null) {
                log.warn("礼品发放单不存在: giftRecordId={}", verificationRecord.getGiftRecordId());
                return;
            }

            // 根据礼品发放单状态进行相应处理
            String currentStatus = giftRecord.getIsFlag() != null ? giftRecord.getIsFlag().toString() : "0";

            switch (currentStatus) {
                case "0": // 待发放状态
                    // 直接取消发放
                    giftRecord.setIsFlag(-1); // 设置为取消状态
                    // 注意：ManualGiftRecord可能没有setUpdateBy和setUpdateTime方法，需要检查实体类
                    manualGiftRecordService.updateById(giftRecord);
                    log.info("礼品发放单已取消: giftRecordId={}", giftRecord.getId());
                    break;

                case "1": // 已发放状态
                    // 需要回收处理
                    processGrantedGiftCancel(giftRecord, verificationRecord.getGiftType(), request);
                    break;

                default:
                    log.warn("礼品发放单状态不支持撤销: giftRecordId={}, status={}",
                            giftRecord.getId(), currentStatus);
            }

        } catch (Exception e) {
            log.error("处理礼品发放单撤销异常: giftRecordId={}", verificationRecord.getGiftRecordId(), e);
            throw new RuntimeException("处理礼品发放单撤销失败", e);
        }
    }

    /**
     * 处理已发放礼品的撤销
     */
    private void processGrantedGiftCancel(ManualGiftRecord giftRecord, String giftType, DouyinGiftCancelRequest request) {
        log.info("处理已发放礼品撤销: giftRecordId={}, giftType={}", giftRecord.getId(), giftType);

        switch (giftType) {
            case "A":
                // 电子币撤销：需要从客户账户扣减
                // TODO: 调用客户账户服务扣减电子币
                break;

            case "B":
                // 自营商品撤销：回收库存
                log.info("处理自营商品撤销: productCode={}",
                        giftRecord.getSkuCode());
                // TODO: 调用库存服务回收库存
                break;

            case "C":
                // 联营商品撤销：通知供应商
                log.info("处理联营商品撤销: productCode={}",
                         giftRecord.getSkuCode());
                // TODO: 通知供应商处理撤销
                break;

            default:
                log.warn("未知的礼品类型: {}", giftType);
        }

        // 更新礼品发放单状态为已撤销
        giftRecord.setIsFlag(-2); // 设置为撤销状态
        // 注意：ManualGiftRecord可能没有setUpdateBy和setUpdateTime方法，需要检查实体类
        manualGiftRecordService.updateById(giftRecord);
    }

    /**
     * 更新券状态为未使用
     */
    private void updateCouponStatusAfterCancel(DouyinCoupon coupon) {
        coupon.setCouponStatus(DouyinCoupon.STATUS_UNUSED);
        coupon.setVerificationTime(null);
        coupon.setVerifyId(null);
        douyinCouponService.updateById(coupon);
        log.info("券状态已更新为未使用: couponCode={}", coupon.getCouponCode());
    }

    /**
     * 更新核销记录状态
     */
    private void updateVerificationRecordAfterCancel(DouyinGiftVerificationRecord verificationRecord,
                                                    DouyinGiftCancelRequest request) {
        verificationRecord.setVerificationStatus("CANCELLED");
        verificationRecord.setSyncStatus("CANCELLED");
        // 设置礼品发放状态为已作废
        verificationRecord.setGiftIssueStatus(DouyinGiftVerificationRecord.GIFT_ISSUE_STATUS_CANCELLED);
        verificationRecordMapper.updateById(verificationRecord);
        log.info("核销记录状态已更新为已撤销，礼品发放状态已更新为已作废: recordId={}", verificationRecord.getId());
    }
}
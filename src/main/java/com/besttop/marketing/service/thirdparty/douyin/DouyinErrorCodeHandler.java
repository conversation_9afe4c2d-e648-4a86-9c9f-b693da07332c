package com.besttop.marketing.service.thirdparty.douyin;

import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * 抖音错误码标准化处理器
 * 
 * <AUTHOR> @since 2024-12-25
 */
@Component
public class DouyinErrorCodeHandler {
    
    /**
     * 验券结果错误码映射 - 三方码
     */
    private static final Map<String, Integer> THIRD_PARTY_VERIFY_ERROR_MAP;
    static {
        Map<String, Integer> map = new HashMap<>();
        map.put("COUPON_ALREADY_USED", 1208);      // 券码已核销
        map.put("COUPON_IN_REFUND", 1205);         // 券码正在退款中（用户主动退款）
        map.put("COUPON_NOT_STARTED", 1206);       // 券码未到可使用日期
        map.put("COUPON_EXPIRED_REFUND", 1207);    // 券码正在退款中（过期自动退）
        map.put("COUPON_REFUNDED", 1209);          // 券码已退款
        map.put("COUPON_NOT_FOUND", 1212);         // 无法查到券信息
        map.put("OTHER_ERROR", 1211);              // 其他错误
        THIRD_PARTY_VERIFY_ERROR_MAP = map;
    }
    
    /**
     * 验券结果错误码映射 - 抖音码
     */
    private static final Map<String, Integer> DOUYIN_VERIFY_ERROR_MAP;
    static {
        Map<String, Integer> map = new HashMap<>();
        map.put("COUPON_IN_REFUND", 3);            // 券码正在退款中（用户主动退款）
        map.put("COUPON_NOT_STARTED", 5);          // 券码未到可使用日期
        map.put("COUPON_EXPIRED_REFUND", 32);      // 券码正在退款中（过期自动退）
        map.put("COUPON_ALREADY_USED", 2);         // 券码已核销
        map.put("COUPON_REFUNDED", 4);             // 券码已退款
        map.put("COUPON_REFUND_APPLYING", 31);     // 券码退款中（用户申请）
        map.put("COUPON_EXPIRED", 6);              // 券码已过期，系统将自动发起退款
        DOUYIN_VERIFY_ERROR_MAP = map;
    }
    
    /**
     * 订单状态查询错误码映射
     */
    private static final Map<String, Integer> ORDER_QUERY_ERROR_MAP;
    static {
        Map<String, Integer> map = new HashMap<>();
        map.put("ORDER_NOT_FOUND", 1);             // 未查询到抖音订单id
        map.put("CODE_NOT_FOUND", 2);              // 未查询到相关券码code
        map.put("SYSTEM_ERROR_RETRY", 3);          // 服务商内部错误，可重试（如超时）
        map.put("SYSTEM_ERROR_NO_RETRY", 4);       // 服务商内部错误，不可重试（如宕机了）
        map.put("SCENE_NOT_SUPPORT", 5);           // 不支持该场景
        map.put("OTHER_ERROR", 99999);             // 其他未预定义的异常情况
        ORDER_QUERY_ERROR_MAP = map;
    }
    
    /**
     * 发券失败原因映射
     */
    private static final Map<String, Integer> COUPON_FAIL_REASON_MAP;
    static {
        Map<String, Integer> map = new HashMap<>();
        map.put("PRODUCT_NOT_EXIST", 1);           // 商品不存在
        map.put("PRODUCT_OFFLINE", 2);             // 商品已下线
        map.put("SALE_NOT_STARTED", 3);            // 未到商品开始售卖时间
        map.put("SALE_ENDED", 4);                  // 已过商品结束售卖时间
        map.put("STOCK_EMPTY", 5);                 // 商品库存售罄
        map.put("PURCHASE_LIMIT", 6);              // 已达到购买上限
        map.put("PRICE_CHECK_FAIL", 7);            // 价格校验失败
        map.put("OTHER_ERROR", 20);                // 其他异常（服务商自定义）
        COUPON_FAIL_REASON_MAP = map;
    }
    
    /**
     * 构建验券错误响应
     * 
     * @param errorType 错误类型
     * @param isThirdParty 是否为三方码
     * @param message 错误消息
     * @return 错误响应
     */
    public Map<String, Object> buildVerifyErrorResponse(String errorType, boolean isThirdParty, String message) {
        Map<String, Integer> errorMap = isThirdParty ? THIRD_PARTY_VERIFY_ERROR_MAP : DOUYIN_VERIFY_ERROR_MAP;
        Integer errorCode = errorMap.getOrDefault(errorType, isThirdParty ? 1211 : -1);
        
        Map<String, Object> response = new HashMap<>();
        response.put("result", errorCode);
        response.put("msg", message);
        response.put("code", "");
        response.put("verify_id", "");
        response.put("certificate_id", "");
        response.put("order_id", "");
        return response;
    }
    
    /**
     * 构建订单查询错误响应
     * 
     * @param errorType 错误类型
     * @param message 错误消息
     * @return 错误响应
     */
    public Map<String, Object> buildOrderQueryErrorResponse(String errorType, String message) {
        Integer errorCode = ORDER_QUERY_ERROR_MAP.getOrDefault(errorType, 99999);
        
        Map<String, Object> response = new HashMap<>();
        response.put("error_code", errorCode);
        response.put("description", message);
        response.put("result", new HashMap<>());
        return response;
    }
    
    /**
     * 构建发券失败响应
     * 
     * @param failReason 失败原因
     * @param message 错误消息
     * @return 发券响应
     */
    public Map<String, Object> buildCouponFailResponse(String failReason, String message) {
        Integer reasonCode = COUPON_FAIL_REASON_MAP.getOrDefault(failReason, 20);
        
        Map<String, Object> response = new HashMap<>();
        response.put("result", 2);  // 失败
        response.put("fail_reason", message);
        response.put("codes", new String[0]);
        return response;
    }
    
    /**
     * 构建标准成功响应
     * 
     * @param data 响应数据
     * @return 成功响应
     */
    public Map<String, Object> buildSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("error_code", 0);
        response.put("description", "success");
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }
    
    /**
     * 构建标准错误响应
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @return 错误响应
     */
    public Map<String, Object> buildErrorResponse(int errorCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("error_code", errorCode);
        response.put("description", message);
        return response;
    }
    
    /**
     * 获取验券成功响应
     * 
     * @param code 券码
     * @param verifyId 核销ID
     * @param certificateId 券证书ID
     * @param orderId 订单ID
     * @return 验券成功响应
     */
    public Map<String, Object> buildVerifySuccessResponse(String code, String verifyId, 
                                                         String certificateId, String orderId) {
        Map<String, Object> response = new HashMap<>();
        response.put("result", 0);
        response.put("msg", "验券成功");
        response.put("code", code != null ? code : "");
        response.put("verify_id", verifyId != null ? verifyId : "");
        response.put("certificate_id", certificateId != null ? certificateId : "");
        response.put("order_id", orderId != null ? orderId : "");
        return response;
    }
}

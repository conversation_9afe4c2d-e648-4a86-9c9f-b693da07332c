package com.besttop.marketing.common;

import com.alibaba.fastjson.JSON;
import com.besttop.marketing.mapper.common.DaoMapper;
import com.besttop.marketing.mapper.shopping.ShoppingOrderSkuMapper;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.shopping.result.OrderSkuResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponVerifyResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponLog;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponVerifyService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSyncService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponService;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCouponLogMapper;
import com.besttop.marketing.mapper.shopping.ShoppingOrderMapper;
import com.besttop.marketing.model.thirdparty.douyin.param.VerifyCouponRequest;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description 抖音券支付
 * @date 2025/6/23 15:21
 */
@Component
@Slf4j
public class DouyinCouponPayBak implements Pay {

    @Autowired
    private LoginCacheUtil loginCacheUtil;
    
    @Autowired
    private DouyinCouponVerifyService douyinCouponVerifyService;

    @Autowired
    private DouyinSyncService douyinSyncService;

    @Autowired
    private DouyinCouponService douyinCouponService;

    @Autowired
    private DouyinCouponLogMapper douyinCouponLogMapper;

    @Autowired
    private ShoppingOrderSkuMapper shoppingOrderSkuMapper;

    @Autowired
    private ShoppingOrderMapper shoppingOrderMapper;

    @Autowired
    private DaoMapper daoMapper;


    @Override
    public PayResult pay(PaymentParam paymentParam) {
        long startTime = System.currentTimeMillis();
        String user = this.loginCacheUtil.getUserCode() + CommonConstant.MIDDLE_BAR + this.loginCacheUtil.getUserName();
        String store = this.loginCacheUtil.getStoreCode();
        
        // 获取券码，前端传入抖音券码
        String couponCode = paymentParam.getPayVoucher();
        BigDecimal payAmount = paymentParam.getPayAmount();
        String orderNo = paymentParam.getOrderNo();
        List<String> orderCodes = paymentParam.getOrderCodes();
        // 初始化返回结果
        PayResult payResult = new PayResult();
        
        try {
            // 1. 参数校验
            if (StringUtils.isBlank(couponCode)) {
                log.error("抖音券支付失败：券码不能为空");
                return new PayResult(CommonConstant.INTEGER_ZERO, "券码不能为空", null);
            }
            
            if (payAmount == null || payAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.error("抖音券支付失败：支付金额必须大于0");
                return new PayResult(CommonConstant.INTEGER_ZERO, "支付金额必须大于0", null);
            }
            
            if (StringUtils.isBlank(store)) {
                log.error("抖音券支付失败：门店编码不能为空");
                return new PayResult(CommonConstant.INTEGER_ZERO, "门店编码不能为空", null);
            }

            // 根据订单号商品对应的品牌品类
            List<OrderSkuResult> orderSkuResults = shoppingOrderSkuMapper.querySkuClassBrandByOrderCode(orderCodes);
            
            // 2. 准备核销参数
            Map<String, Object> verifyParams = new HashMap<>();
            verifyParams.put("orderAmount", payAmount);
            verifyParams.put("orderNo", orderNo);
            verifyParams.put("payType", paymentParam.getPayType());
            verifyParams.put("payTime", new Date());
            verifyParams.put("orderCodes", orderCodes);
            verifyParams.put("skuCode", orderSkuResults.get(0).getSkuCode());
            verifyParams.put("classCode",orderSkuResults.get(0).getClassCode().substring(0,2));
            verifyParams.put("brandCode", orderSkuResults.get(0).getBrandCode());
            verifyParams.put("payAmount", payAmount);
            
            // 3. 校验电话号码一致性（针对三方码）
            String phoneValidationResult = validateCustomerPhone(couponCode, paymentParam);
            if (phoneValidationResult != null) {
                String errorMsg = "抖音券支付失败：" + phoneValidationResult;
                log.error(errorMsg);
                recordPayLog(couponCode, orderNo, user, store, payAmount, false, errorMsg);
                return new PayResult(CommonConstant.INTEGER_ZERO, errorMsg, null);
            }

            // 4. 调用券核销服务（支持提货券和三方码）
            log.info("开始核销抖音券，券码: {}, 门店: {}, 支付金额: {}", couponCode, store, payAmount);
            DouyinCouponVerifyResult verifyResult = verifyDouyinCoupon(couponCode, store, user, verifyParams);

            // 5. 处理核销结果
            if (!verifyResult.isSuccess()) {
                String errorMsg = "抖音券支付失败：" + verifyResult.getMessage();
                log.error(errorMsg);
                recordPayLog(couponCode, orderNo, user, store, payAmount, false, errorMsg);
                return new PayResult(CommonConstant.INTEGER_ZERO, errorMsg, null);
            }
            
            // 6. 获取核销券信息
            DouyinCoupon coupon = verifyResult.getCouponInfo();
//            DouyinCouponRule rule = verifyResult.getCouponRule();

            // 7. 构建支付成功结果
//            String transactionId = System.currentTimeMillis() + NumberUtils.get6Random();
            
            payResult.setPayStatus(CommonConstant.INTEGER_ONE); // 支付成功
            payResult.setTransactionId(coupon.getId());
            payResult.setThirdPayMsg("抖音券支付成功");
            payResult.setThirdPartyName("抖音券");
            
            // 如果有验证类型，添加到支付结果
            if (StringUtils.isNotEmpty(verifyResult.getVerificationType())) {
                if ("CASH".equals(verifyResult.getVerificationType())) {
                    payResult.setThirdPartyName("抖音代金券");
                } else if ("GIFT".equals(verifyResult.getVerificationType())) {
                    payResult.setThirdPartyName("抖音提货券");
                }
            }

            // 7. 记录支付日志
            log.info("抖音券支付成功，券码: {}, 交易ID: {}, 金额: {}, 订单号: {}", 
                    couponCode, couponCode, payAmount, orderNo);
            
            return payResult;
        } catch (Exception e) {
            String errorMsg = "抖音券支付处理异常: " + e.getMessage();
            log.error("抖音券支付异常", e);
            recordPayLog(couponCode, orderNo, user, store, payAmount, false, errorMsg);
            return new PayResult(CommonConstant.INTEGER_ZERO, errorMsg, null);
        }
    }
    
    /**
     * 记录支付日志
     * 
     * @param couponCode 券码
     * @param orderNo 订单号
     * @param userId 用户ID
     * @param storeCode 门店编码
     * @param payAmount 支付金额
     * @param success 是否成功
     * @param message 消息
     */
    private void recordPayLog(String couponCode, String orderNo, String userId, 
                              String storeCode, BigDecimal payAmount, boolean success, String message) {
        try {
            DouyinCouponLog log = new DouyinCouponLog();
            log.setId(UUID.randomUUID().toString().replace("-", ""));
            
            // 设置操作信息
            log.setOperationType(success ? "PAY_SUCCESS" : "PAY_FAILED");
            log.setIdempotencyKey(orderNo + "_PAY_" + System.currentTimeMillis());
            log.setRequestSource("EDP_PAY");
            
            // 设置请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("coupon_code", couponCode);
            requestData.put("order_no", orderNo);
            requestData.put("store_code", storeCode);
            requestData.put("user_id", userId);
            requestData.put("pay_amount", payAmount);
            log.setRequestPayloadJson(JSON.toJSONString(requestData));
            
            // 设置响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", success);
            responseData.put("message", message);
            log.setResponsePayloadJson(JSON.toJSONString(responseData));
            
            // 设置操作人和时间
            log.setOperatorId(userId);
            log.setOperationTime(new Date());
            
            // 设置结果状态
            log.setResultStatus(success ? DouyinCouponLog.RESULT_SUCCESS : DouyinCouponLog.RESULT_FAILED);
            
            // 如果失败，设置错误信息
            if (!success) {
                log.setErrorCode("PAY_FAILED");
                log.setErrorMessage(message);
            }
            
            // 插入日志记录
            douyinCouponLogMapper.insert(log);
            
        } catch (Exception e) {
            log.error("记录抖音券支付日志异常", e);
            // 记录日志失败不影响主流程
        }
    }

    /**
     * 核销抖音券（支持提货券和三方码）
     *
     * @param couponCode 券码
     * @param storeCode 门店编码
     * @param staffId 操作员ID
     * @param verifyParams 核销参数
     * @return 核销结果
     */
    private DouyinCouponVerifyResult verifyDouyinCoupon(String couponCode, String storeCode,
                                                       String staffId, Map<String, Object> verifyParams) {
        try {
            log.info("开始核销抖音券: 券码={}, 门店={}, 操作员={}", couponCode, storeCode, staffId);

            // 1. 先查询券信息，判断券类型
            List<DouyinCoupon> coupons = douyinCouponService.findByCouponCode(couponCode);
            if (coupons == null || coupons.isEmpty()) {
                log.warn("券码不存在: {}", couponCode);
                DouyinCouponVerifyResult result = new DouyinCouponVerifyResult();
                result.setSuccess(false);
                result.setMessage("券码不存在");
                return result;
            }

            DouyinCoupon coupon = coupons.get(0);

            // 判断支付金额是否和券的划线价格一致
            if (coupon.getListPrice().compareTo((BigDecimal)verifyParams.get("payAmount")) != 0) {
                log.error("支付金额和券的划线价格不一致: {}", coupon.getListPrice());
                DouyinCouponVerifyResult result = new DouyinCouponVerifyResult();
                result.setSuccess(false);
                result.setMessage("支付金额和金额不一致");
                return result;
            }

            String verificationType = determineVerificationType(coupon);

            // 2. 根据券类型选择不同的核销方式
            DouyinCouponVerifyResult result;
            if ("THIRD_PARTY_CODE".equals(verificationType)) {
                // 三方码核销：需要调用抖音API
                result = verifyThirdPartyCoupon(coupon, storeCode, staffId, verifyParams);
            } else {
                // 提货券核销：暂时注释掉，只使用三方码验券
                // result = douyinCouponVerifyService.verifyCoupon(couponCode, storeCode, staffId, verifyParams);
                result = new DouyinCouponVerifyResult();
                result.setSuccess(false);
                result.setMessage("当前仅支持三方码核销，提货券核销功能已暂停");
                log.warn("提货券核销功能已暂停: 券码={}", couponCode);
            }

            // 3. 设置验证类型到结果中
            if (result.isSuccess()) {
                result.setVerificationType(verificationType);
            }

            return result;

        } catch (Exception e) {
            log.error("核销抖音券异常: 券码={}", couponCode, e);
            DouyinCouponVerifyResult result = new DouyinCouponVerifyResult();
            result.setSuccess(false);
            result.setMessage("核销处理异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 判断券的验证类型
     *
     * @param coupon 券信息
     * @return 验证类型：THIRD_PARTY_CODE（三方码）或 GIFT（提货券）
     */
    private String determineVerificationType(DouyinCoupon coupon) {
        // 根据券的特征判断类型
        // 三方码特征：通常有特定的券码格式或来源标识
        if (isThirdPartyCoupon(coupon)) {
            return "THIRD_PARTY_CODE";
        } else {
            return "GIFT"; // 提货券
        }
    }

    /**
     * 判断是否为三方码
     *
     * @param coupon 券信息
     * @return true-三方码，false-提货券
     */
    private boolean isThirdPartyCoupon(DouyinCoupon coupon) {
        // 判断逻辑：
        // 1. 检查券码格式（三方码通常有特定前缀或格式）
        // 2. 检查券来源或类型字段
        // 3. 检查是否有抖音订单ID（三方码通常关联抖音订单）

        String couponCode = coupon.getCouponCode();
        String douyinOrderId = coupon.getDouyinOrderId();

        // 如果有抖音订单ID，且券码不是EDP生成的格式，则认为是三方码
        if (StringUtils.isNotBlank(douyinOrderId) && !isEdpGeneratedCouponCode(couponCode)) {
            return true;
        }

        // 可以根据实际业务规则进一步完善判断逻辑
        return false;
    }

    /**
     * 判断是否为EDP生成的券码格式
     *
     * @param couponCode 券码
     * @return true-EDP生成，false-非EDP生成
     */
    private boolean isEdpGeneratedCouponCode(String couponCode) {
        // EDP生成的券码通常有特定格式，如特定前缀、长度等
        // 这里需要根据实际的券码生成规则来判断
        if (StringUtils.isBlank(couponCode)) {
            return false;
        }

        // 示例：EDP券码可能以特定前缀开头，或有特定长度
        // 需要根据实际情况调整
        return couponCode.length() >= 10 && !couponCode.startsWith("DY");
    }

    /**
     * 核销三方码
     *
     * @param coupon 券信息
     * @param storeCode 门店编码
     * @param staffId 操作员ID
     * @param verifyParams 核销参数
     * @return 核销结果
     */
    private DouyinCouponVerifyResult verifyThirdPartyCoupon(DouyinCoupon coupon, String storeCode,
                                                           String staffId, Map<String, Object> verifyParams) {
        DouyinCouponVerifyResult result = new DouyinCouponVerifyResult();

        try {
            log.info("开始核销三方码: 券码={}, 抖音订单ID={}", coupon.getCouponCode(), coupon.getDouyinOrderId());

            // 1. 验证券状态
            if (!DouyinCoupon.STATUS_UNUSED.equals(coupon.getCouponStatus())) {
                result.setSuccess(false);
                result.setMessage("券码状态不正确，当前状态: " + coupon.getCouponStatus());
                return result;
            }

            // 2. 构建抖音验券请求
            VerifyCouponRequest request = buildThirdPartyVerifyRequest(coupon, storeCode, verifyParams);

            // 3. 获取门店对应的抖音POI ID
            // 从数据库获取抖音门店ID
            String poiId = daoMapper.findLifePoi(storeCode);
            if (StringUtils.isEmpty(poiId)) {
                log.error("找不到门店对应的抖音POI ID: {}", storeCode);
                result.setSuccess(false);
                result.setMessage("门店配置错误：找不到对应的抖音POI ID");
                return result;
            }

            // 4. 调用抖音验券API
            log.info("调用抖音三方码验券API: verifyToken={}, poiId={}, codes={}",
                    request.getVerifyToken(), poiId, request.getCodes());

            Map<String, Object> apiResult = douyinSyncService.verifyCoupon(request, poiId);

            // 5. 处理API响应
            if (apiResult == null) {
                result.setSuccess(false);
                result.setMessage("抖音验券API调用失败：响应为空");
                return result;
            }

            // 解析DouyinSyncService返回的响应格式：{success: true, verify_success: true, ...}
            boolean apiSuccess = parseSyncServiceResponse(apiResult);
            if (!apiSuccess) {
                String errorMsg = extractSyncServiceErrorMessage(apiResult);
                result.setSuccess(false);
                result.setMessage("抖音验券API调用失败：" + errorMsg);

                // 记录详细的API响应用于调试
                log.error("抖音验券API返回错误: 券码={}, 响应={}", coupon.getCouponCode(), JSON.toJSONString(apiResult));
                return result;
            }

            // 6. 更新本地券状态
            boolean localUpdateSuccess = updateLocalCouponStatus(coupon, storeCode, staffId, apiResult);
            if (!localUpdateSuccess) {
                log.error("更新本地券状态失败: {}", coupon.getCouponCode());
                result.setSuccess(false);
                result.setMessage("本地状态更新失败");
                return result;
            }

            // 7. 构建成功结果
            result.setSuccess(true);
            result.setCouponInfo(coupon);
            result.setMessage("三方码核销成功");

            // 记录核销日志
            recordThirdPartyVerifyLog(coupon, storeCode, staffId, verifyParams, apiResult, true, "核销成功");

            // 提取验券ID（从DouyinSyncService响应中获取）
            String verifyId = extractVerifyIdFromSyncServiceResponse(apiResult);
            log.info("三方码核销成功: 券码={}, 验券ID={}",
                    coupon.getCouponCode(), verifyId);

            return result;

        } catch (Exception e) {
            log.error("三方码核销异常: 券码={}", coupon.getCouponCode(), e);

            // 记录异常日志
            recordThirdPartyVerifyLog(coupon, storeCode, staffId, verifyParams, null, false, "核销异常: " + e.getMessage());

            result.setSuccess(false);
            result.setMessage("三方码核销异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 构建三方码验券请求
     *
     * @param coupon 券信息
     * @param storeCode 门店编码
     * @param verifyParams 验券参数
     * @return 验券请求
     */
    private VerifyCouponRequest buildThirdPartyVerifyRequest(DouyinCoupon coupon, String storeCode,
                                                           Map<String, Object> verifyParams) {
        VerifyCouponRequest request = new VerifyCouponRequest();

        // 生成唯一的验券token（每次核销都必须唯一，避免重复调用问题）
        String uniqueVerifyToken = generateUniqueVerifyToken(coupon, storeCode);
        request.setVerifyToken(uniqueVerifyToken);

        // 设置抖音订单ID
        request.setOrderId(coupon.getDouyinOrderId());

        // 设置券码列表（三方码使用codes字段）
        List<String> codes = new ArrayList<>();
        codes.add(coupon.getCouponCode());
        request.setCodes(codes);


        log.info("构建三方码验券请求: verifyToken={}, orderId={}, codes={}",
                request.getVerifyToken(), request.getOrderId(), request.getCodes());

        return request;
    }

    /**
     * 更新本地券状态
     *
     * @param coupon 券信息
     * @param storeCode 门店编码
     * @param staffId 操作员ID
     * @param verifyParams 验券参数
     * @return 更新是否成功
     */
    private boolean updateLocalCouponStatus(DouyinCoupon coupon, String storeCode, String staffId,
                                           Map<String, Object> apiResult) {
        try {
            // 更新券状态为已使用
            coupon.setCouponStatus(DouyinCoupon.STATUS_USED);
            coupon.setVerificationTime(new Date());
            coupon.setVerifyId(apiResult.get("verify_id").toString());
            coupon.setDouyinCertificateId((String) apiResult.get("certificate_id"));
            coupon.setVerificationStoreCode(storeCode);
            coupon.setVerificationStaffId(staffId);
            coupon.setVerificationSyncStatus(DouyinCoupon.SYNC_STATUS_SUCCESS); // 三方码已同步到抖音
            coupon.setUpdateTime(new Date());

            // 保存更新
            return douyinCouponService.updateById(coupon);

        } catch (Exception e) {
            log.error("更新本地券状态异常: 券码={}", coupon.getCouponCode(), e);
            return false;
        }
    }

    /**
     * 记录三方码核销日志
     *
     * @param coupon 券信息
     * @param storeCode 门店编码
     * @param staffId 操作员ID
     * @param verifyParams 验券参数
     * @param apiResult API响应结果
     * @param success 是否成功
     * @param message 消息
     */
    private void recordThirdPartyVerifyLog(DouyinCoupon coupon, String storeCode, String staffId,
                                         Map<String, Object> verifyParams, Map<String, Object> apiResult,
                                         boolean success, String message) {
        try {
            DouyinCouponLog logRecord = new DouyinCouponLog();
            logRecord.setId(UUID.randomUUID().toString().replace("-", ""));

            // 设置操作信息
            logRecord.setOperationType(success ? "THIRD_PARTY_VERIFY_SUCCESS" : "THIRD_PARTY_VERIFY_FAILED");
            logRecord.setIdempotencyKey(coupon.getCouponCode() + "_VERIFY_" + System.currentTimeMillis());
            logRecord.setRequestSource("EDP_PAY_THIRD_PARTY");

            // 设置请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("coupon_code", coupon.getCouponCode());
            requestData.put("douyin_order_id", coupon.getDouyinOrderId());
            requestData.put("store_code", storeCode);
            requestData.put("staff_id", staffId);
            requestData.put("verify_params", verifyParams);
            logRecord.setRequestPayloadJson(JSON.toJSONString(requestData));

            // 设置响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", success);
            responseData.put("message", message);
            if (apiResult != null) {
                responseData.put("api_result", apiResult);
            }
            logRecord.setResponsePayloadJson(JSON.toJSONString(responseData));

            // 设置操作人和时间
            logRecord.setOperatorId(staffId);
            logRecord.setOperationTime(new Date());

            // 设置结果状态
            logRecord.setResultStatus(success ? DouyinCouponLog.RESULT_SUCCESS : DouyinCouponLog.RESULT_FAILED);

            // 如果失败，设置错误信息
            if (!success) {
                logRecord.setErrorCode("THIRD_PARTY_VERIFY_FAILED");
                logRecord.setErrorMessage(message);
            }

            // 插入日志记录
            douyinCouponLogMapper.insert(logRecord);

        } catch (Exception e) {
            log.error("记录三方码核销日志异常", e);
            // 记录日志失败不影响主流程
        }
    }

    /**
     * 校验顾客电话号码一致性
     * 针对三方码核销，需要校验订单主表的customer_phone和券表的phone是否一致
     *
     * @param couponCode 券码
     * @param paymentParam 支付参数
     * @return 校验失败时返回错误信息，校验通过返回null
     */
    private String validateCustomerPhone(String couponCode, PaymentParam paymentParam) {
        try {
            log.info("开始校验顾客电话号码一致性: 券码={}", couponCode);

            // 1. 获取券信息
            List<DouyinCoupon> coupons = douyinCouponService.findByCouponCode(couponCode);
            if (coupons == null || coupons.isEmpty()) {
                log.warn("券码不存在，跳过电话校验: {}", couponCode);
                return null; // 券码不存在的错误会在后续核销时处理
            }

            DouyinCoupon coupon = coupons.get(0);

            // 2. 判断是否为三方码，只有三方码需要校验电话
            if (!isThirdPartyCoupon(coupon)) {
                log.debug("非三方码，跳过电话校验: 券码={}", couponCode);
                return null; // 提货券不需要校验电话
            }

            // 3. 获取券表中的电话
            String couponPhone = coupon.getPhone();
            if (StringUtils.isBlank(couponPhone)) {
                log.warn("券表中电话为空，跳过电话校验: 券码={}", couponCode);
                return null; // 券表电话为空时不强制校验
            }

            // 4. 获取订单编码列表
            List<String> orderCodes = paymentParam.getOrderCodes();
            if (orderCodes == null || orderCodes.isEmpty()) {
                log.warn("订单编码列表为空，跳过电话校验: 券码={}", couponCode);
                return null;
            }

            // 5. 查询订单主表信息
            List<OrderAndSkuResult> orderResults = shoppingOrderMapper.queryOrderByCodes(orderCodes);
            if (orderResults == null || orderResults.isEmpty()) {
                log.warn("未找到订单信息，跳过电话校验: orderCodes={}", orderCodes);
                return null;
            }

            // 6. 校验电话一致性
            for (OrderAndSkuResult orderResult : orderResults) {
                String orderPhone = orderResult.getCustomerPhone();
                String orderCode = orderResult.getOrderCode();

                if (StringUtils.isBlank(orderPhone)) {
                    log.warn("订单电话为空: orderCode={}", orderCode);
                    continue;
                }

                // 标准化电话号码（去除空格、特殊字符等）
                String normalizedCouponPhone = normalizePhoneNumber(couponPhone);
                String normalizedOrderPhone = normalizePhoneNumber(orderPhone);

                if (!normalizedCouponPhone.equals(normalizedOrderPhone)) {
                    String errorMsg = String.format("顾客电话不一致，券码电话: %s，订单电话: %s，订单号: %s",
                            maskPhoneNumber(couponPhone), maskPhoneNumber(orderPhone), orderCode);
                    log.error("电话校验失败: {}", errorMsg);
                    return errorMsg;
                }

                log.debug("电话校验通过: 券码={}, 订单={}, 电话={}",
                        couponCode, orderCode, maskPhoneNumber(orderPhone));
            }

            log.info("顾客电话号码一致性校验通过: 券码={}", couponCode);
            return null; // 校验通过

        } catch (Exception e) {
            log.error("校验顾客电话号码异常: 券码={}", couponCode, e);
            return "电话校验异常: " + e.getMessage();
        }
    }

    /**
     * 标准化电话号码
     * 去除空格、横线、括号等特殊字符，统一格式便于比较
     *
     * @param phoneNumber 原始电话号码
     * @return 标准化后的电话号码
     */
    private String normalizePhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return "";
        }

        // 去除所有非数字字符
        String normalized = phoneNumber.replaceAll("[^0-9]", "");

        // 如果是11位手机号且以1开头，保持原样
        if (normalized.length() == 11 && normalized.startsWith("1")) {
            return normalized;
        }

        // 如果是13位号码且以86开头（国际格式），去掉86前缀
        if (normalized.length() == 13 && normalized.startsWith("86")) {
            return normalized.substring(2);
        }

        return normalized;
    }

    /**
     * 电话号码脱敏显示
     * 隐藏中间4位数字，保护用户隐私
     *
     * @param phoneNumber 原始电话号码
     * @return 脱敏后的电话号码
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return "空";
        }

        String normalized = normalizePhoneNumber(phoneNumber);
        if (normalized.length() == 11) {
            return normalized.substring(0, 3) + "****" + normalized.substring(7);
        } else if (normalized.length() >= 7) {
            return normalized.substring(0, 3) + "****" + normalized.substring(normalized.length() - 3);
        } else {
            return "***";
        }
    }

    /**
     * 解析抖音API响应，判断是否成功
     * 抖音响应格式：{data: {error_code: 0, description: "success"}, extra: {...}}
     *
     * @param apiResult API响应结果
     * @return true-成功，false-失败
     */
    @SuppressWarnings("unchecked")
    private boolean parseDouyinApiResponse(Map<String, Object> apiResult) {
        try {
            // 检查data字段
            Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
            if (data == null) {
                log.warn("抖音API响应缺少data字段: {}", JSON.toJSONString(apiResult));
                return false;
            }

            // 检查error_code
            Integer errorCode = (Integer) data.get("error_code");
            if (errorCode == null) {
                log.warn("抖音API响应data中缺少error_code字段: {}", JSON.toJSONString(data));
                return false;
            }

            // error_code = 0 表示成功，其他值表示失败
            boolean success = errorCode == 0;

            log.debug("解析抖音API响应: error_code={}, success={}", errorCode, success);
            return success;

        } catch (Exception e) {
            log.error("解析抖音API响应异常: {}", JSON.toJSONString(apiResult), e);
            return false;
        }
    }

    /**
     * 提取抖音API错误信息
     *
     * @param apiResult API响应结果
     * @return 错误信息
     */
    @SuppressWarnings("unchecked")
    private String extractDouyinApiErrorMessage(Map<String, Object> apiResult) {
        try {
            StringBuilder errorMsg = new StringBuilder();

            // 从data字段提取错误信息
            Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
            if (data != null) {
                Integer errorCode = (Integer) data.get("error_code");
                String description = (String) data.get("description");

                if (errorCode != null) {
                    errorMsg.append("错误码: ").append(errorCode);
                }

                if (StringUtils.isNotBlank(description)) {
                    if (errorMsg.length() > 0) {
                        errorMsg.append(", ");
                    }
                    errorMsg.append("错误描述: ").append(description);
                }
            }

            // 从extra字段提取额外错误信息
            Map<String, Object> extra = (Map<String, Object>) apiResult.get("extra");
            if (extra != null) {
                String logid = (String) extra.get("logid");
                if (StringUtils.isNotBlank(logid)) {
                    if (errorMsg.length() > 0) {
                        errorMsg.append(", ");
                    }
                    errorMsg.append("日志ID: ").append(logid);
                }
            }

            // 如果没有提取到任何错误信息，返回默认信息
            if (errorMsg.length() == 0) {
                return "未知错误，响应: " + JSON.toJSONString(apiResult);
            }

            return errorMsg.toString();

        } catch (Exception e) {
            log.error("提取抖音API错误信息异常: {}", JSON.toJSONString(apiResult), e);
            return "解析错误信息失败: " + e.getMessage();
        }
    }

    /**
     * 从抖音API响应中提取验券ID
     *
     * @param apiResult API响应结果
     * @return 验券ID，如果没有则返回"未知"
     */
    @SuppressWarnings("unchecked")
    private String extractVerifyIdFromResponse(Map<String, Object> apiResult) {
        try {
            // 检查data字段中的verify_id
            Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
            if (data != null) {
                Object verifyId = data.get("verify_id");
                if (verifyId != null) {
                    return verifyId.toString();
                }
            }

            // 如果data中没有，检查根级别的verify_id
            Object verifyId = apiResult.get("verify_id");
            if (verifyId != null) {
                return verifyId.toString();
            }

            return "未知";

        } catch (Exception e) {
            log.error("提取验券ID异常: {}", JSON.toJSONString(apiResult), e);
            return "提取失败";
        }
    }

    /**
     * 解析DouyinSyncService返回的响应，判断是否成功
     * DouyinSyncService响应格式：{success: true, verify_success: true, verify_msg: "验券成功", verify_id: "xxx"}
     *
     * @param apiResult DouyinSyncService响应结果
     * @return true-成功，false-失败
     */
    private boolean parseSyncServiceResponse(Map<String, Object> apiResult) {
        try {
            // 检查success字段
            Boolean success = (Boolean) apiResult.get("success");
            if (success == null || !success) {
                log.warn("DouyinSyncService响应success为false: {}", JSON.toJSONString(apiResult));
                return false;
            }

            // 检查verify_success字段
            Boolean verifySuccess = (Boolean) apiResult.get("verify_success");
            if (verifySuccess == null || !verifySuccess) {
                log.warn("DouyinSyncService响应verify_success为false: {}", JSON.toJSONString(apiResult));
                return false;
            }

            log.debug("解析DouyinSyncService响应成功: success={}, verify_success={}", success, verifySuccess);
            return true;

        } catch (Exception e) {
            log.error("解析DouyinSyncService响应异常: {}", JSON.toJSONString(apiResult), e);
            return false;
        }
    }

    /**
     * 提取DouyinSyncService错误信息
     *
     * @param apiResult DouyinSyncService响应结果
     * @return 错误信息
     */
    private String extractSyncServiceErrorMessage(Map<String, Object> apiResult) {
        try {
            StringBuilder errorMsg = new StringBuilder();

            // 检查verify_msg字段
            String verifyMsg = (String) apiResult.get("verify_msg");
            if (StringUtils.isNotBlank(verifyMsg)) {
                errorMsg.append("验券消息: ").append(verifyMsg);
            }

            // 检查error_message字段
            String errorMessage = (String) apiResult.get("error_message");
            if (StringUtils.isNotBlank(errorMessage)) {
                if (errorMsg.length() > 0) {
                    errorMsg.append(", ");
                }
                errorMsg.append("错误信息: ").append(errorMessage);
            }

            // 检查success字段
            Boolean success = (Boolean) apiResult.get("success");
            if (success != null && !success) {
                if (errorMsg.length() > 0) {
                    errorMsg.append(", ");
                }
                errorMsg.append("操作失败");
            }

            // 如果没有提取到任何错误信息，返回默认信息
            if (errorMsg.length() == 0) {
                return "未知错误，响应: " + JSON.toJSONString(apiResult);
            }

            return errorMsg.toString();

        } catch (Exception e) {
            log.error("提取DouyinSyncService错误信息异常: {}", JSON.toJSONString(apiResult), e);
            return "解析错误信息失败: " + e.getMessage();
        }
    }

    /**
     * 从DouyinSyncService响应中提取验券ID
     *
     * @param apiResult DouyinSyncService响应结果
     * @return 验券ID，如果没有则返回"未知"
     */
    private String extractVerifyIdFromSyncServiceResponse(Map<String, Object> apiResult) {
        try {
            // 检查verify_id字段
            Object verifyId = apiResult.get("verify_id");
            if (verifyId != null) {
                return verifyId.toString();
            }

            return "未知";

        } catch (Exception e) {
            log.error("提取DouyinSyncService验券ID异常: {}", JSON.toJSONString(apiResult), e);
            return "提取失败";
        }
    }

    /**
     * 生成唯一的验券token
     * 根据抖音官方要求，verify_token必须每次核销时都保持唯一，不能重复使用
     *
     * @param coupon 券信息
     * @param storeCode 门店编码
     * @return 唯一的验券token
     */
    private String generateUniqueVerifyToken(DouyinCoupon coupon, String storeCode) {
        try {
            // 方案1：使用时间戳 + 券码 + 门店编码 + 随机数
            long timestamp = System.currentTimeMillis();
            String randomSuffix = String.valueOf((int)(Math.random() * 10000));

            // 构建唯一token：VERIFY_{券码后6位}_{门店编码}_{时间戳}_{随机数}
            String couponSuffix = coupon.getCouponCode().length() >= 6 ?
                    coupon.getCouponCode().substring(coupon.getCouponCode().length() - 6) :
                    coupon.getCouponCode();

            String verifyToken = String.format("VERIFY_%s_%s_%d_%s",
                    couponSuffix, storeCode, timestamp, randomSuffix);

            log.info("生成唯一验券token: 券码={}, 门店={}, token={}",
                    coupon.getCouponCode(), storeCode, verifyToken);

            return verifyToken;

        } catch (Exception e) {
            log.error("生成验券token异常: 券码={}, 门店={}", coupon.getCouponCode(), storeCode, e);

            // 异常情况下使用备用方案：时间戳 + UUID后8位
            long timestamp = System.currentTimeMillis();
            String uuid = java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 8);
            String fallbackToken = "VERIFY_" + timestamp + "_" + uuid;

            log.warn("使用备用验券token: {}", fallbackToken);
            return fallbackToken;
        }
    }
}

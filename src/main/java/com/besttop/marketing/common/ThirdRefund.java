package com.besttop.marketing.common;

import java.math.BigDecimal;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.common.prefix.RedisPrefix_ERP;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.pay.RefundParam;
import com.besttop.marketing.model.pay.RefundResult;
import com.besttop.marketing.model.shopping.ShoppingPayThird;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import com.besttop.marketing.service.pay.RefundService;
import com.besttop.marketing.service.shopping.ShoppingPayThirdService;
import com.besttop.marketing.util.EncryptionUtil;
import com.besttop.marketing.util.RedisGetInfoUtil;
import com.besttop.redis.utils.LoginCacheUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * <p>Title: ThirdRefund</p>
 * <p>Description: ThirdRefund 第三方退款</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/5/3 15:22 Create by Sissi
 * </pre>
 */
@Component
@Slf4j
public class ThirdRefund implements Refund {

    @Autowired
    private RefundService refundService;

    @Autowired
    private ShoppingPayThirdService payThirdService;

    @Autowired
    private RedisGetInfoUtil redisGetInfoUtil;

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * 元转分单位
     */
    private static final BigDecimal UNIT = new BigDecimal(100);

    /**
     * @param param
     * @return com.besttop.marketing.model.pay.RefundResult
     * @methodName refund
     * @description 退款
     * <AUTHOR>
     * @date 2020/5/3 15:21
     */
    @Override
    public RefundResult refund(RefundmentParam param) {
        log.info("====[ThirdRefund.refund]4param={}", JSON.toJSONString(param));

        RefundParam refundParam = new RefundParam();
        refundParam.setNonceStr(UUID.randomUUID().toString().replaceAll("-", ""));
        refundParam.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        refundParam.setServiceId(CommonConstant.SERVICEID);
        //查询退款码
        QueryWrapper<ShoppingPayThird> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transaction_voucher", param.getPayVoucher());
        queryWrapper.eq("is_cancel_return", 0);
        if(null != param.getSort() && "erp:pay_config_pay_type:27".equals(param.getPayType())) {
        	queryWrapper.eq("sort", param.getSort());
        }
        ShoppingPayThird payThird = payThirdService.getOne(queryWrapper);
        log.info("====[ThirdRefund.refund]5payThird={}", JSON.toJSONString(payThird));
        if (null == payThird) {
            log.info("====[ThirdRefund.refund]6param={}", JSON.toJSONString(param));
            throw new RuntimeException("支付凭证有误，或重复退款，请核实");
        }
        log.info("====[ThirdRefund.refund]7param={}", JSON.toJSONString(param));
        refundParam.setOrderNo(payThird.getMerchantNumber());
        refundParam.setOrderDesc(loginCacheUtil.getStoreCode() + redisGetInfoUtil.getName(RedisPrefix_ERP.JI_GOU_XIN_XI, loginCacheUtil.getStoreCode()));
        refundParam.setRefundMoney(param.getPayAmount().multiply(UNIT).intValue());
        refundParam.setRefundCode(EncryptionUtil.md5Digest(refundParam.getServiceId()
                + refundParam.getOrderNo() + refundParam.getRefundMoney()));
        refundParam.setRefundReqNo(param.getRefundReqNo());
        refundParam.setReferNo(param.getReferNo());
        refundParam.setTxTerminalSequence(param.getTxTerminalSequence());
        refundParam.setFee(param.getFee());
        return refundService.refund(refundParam);

    }
}

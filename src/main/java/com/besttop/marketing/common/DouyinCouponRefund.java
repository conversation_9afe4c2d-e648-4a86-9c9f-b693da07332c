package com.besttop.marketing.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.besttop.marketing.mapper.common.DaoMapper;
import com.besttop.marketing.model.pay.RefundResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.param.CancelVerificationRequest;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponService;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSyncService;
import com.besttop.pay.common.entity.PayResultEnum;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: ThirdRefund</p>
 * <p>Description: ThirdRefund 第三方退款</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/5/3 15:22 Create by Sissi
 * </pre>
 */
@Component
@Slf4j
public class DouyinCouponRefund implements Refund {

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private DouyinSyncService douyinSyncService;

    @Autowired
    private DouyinCouponService douyinCouponService;

    @Autowired
    private DaoMapper daoMapper;

    /**
     * 元转分单位
     */
    private static final BigDecimal UNIT = new BigDecimal(100);

    /**
     * @param param
     * @return com.besttop.marketing.model.pay.RefundResult
     * @methodName refund
     * @description 退款
     * <AUTHOR>
     * @date 2020/5/3 15:21
     */
    @Override
    public RefundResult refund(RefundmentParam param) {
        String storeCode = loginCacheUtil.getStoreCode();
        LambdaQueryWrapper<DouyinCoupon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DouyinCoupon::getId, param.getPayVoucher());
        queryWrapper.isNotNull(DouyinCoupon::getVerifyId);
        DouyinCoupon douyinCoupon = douyinCouponService.getOne(queryWrapper,false);
        // 从数据库获取抖音门店ID
        String poiId = daoMapper.findLifePoi(storeCode);
        if (StringUtils.isEmpty(poiId)) {
            throw new RuntimeException("未找到门店对应的抖音门店ID");
        }
        CancelVerificationRequest request = new CancelVerificationRequest();
        request.setCertificateId(douyinCoupon.getDouyinCertificateId()); // 抖音支付返回的 certificate_id
        request.setVerifyId(douyinCoupon.getVerifyId()); // 抖音订单ID
        // 调用撤销核销服务（使用完整参数版本）
        Map<String, Object> result = douyinSyncService.cancelVerification(request, poiId);
        RefundResult refundResult = new RefundResult();

        if ((boolean) result.getOrDefault("success", false)) {
            // 更新 DouyinCoupon 为待使用
            LambdaUpdateWrapper<DouyinCoupon> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DouyinCoupon::getId, param.getPayVoucher());
            updateWrapper.set(DouyinCoupon::getCouponStatus, DouyinCoupon.STATUS_UNUSED);
            douyinCouponService.update(null, updateWrapper);
            // 获取撤销结果详情
            List<Map<String, Object>> cancelResults = (List<Map<String, Object>>) result.get("cancel_results");
            if (cancelResults != null && !cancelResults.isEmpty()) {
                log.info("撤销核销成功: 共撤销{}条记录", cancelResults.size());
            } else {
                log.info("撤销核销成功: verifyId={}", request.getVerifyId());
            }
            refundResult.setRefundStatus(PayResultEnum.SUCCESS.getCode());
        } else {
            // 获取错误信息并判断是否可重试
            String errorMsg = (String) result.getOrDefault("error_message", "撤销核销失败");
            Boolean shouldRetry = (Boolean) result.getOrDefault("should_retry", false);
            // 特殊错误处理
            if (errorMsg.contains("超过一个小时")) {
                errorMsg =  "验券超过一个小时不可撤销,请联系管理员";
            } else if (errorMsg.contains("无核销记录")) {
                errorMsg = "无核销记录可撤销";
            } else if (shouldRetry) {
                errorMsg ="撤销核销接口调用失败，建议重试: msg "+ errorMsg;
            } else {
                errorMsg = "撤销核销失败: msg "+errorMsg;
            }
            refundResult.setRefundStatus(PayResultEnum.REFUND_FAILED.getCode());
            refundResult.setThirdRefundMsg(errorMsg);
        }

        return refundResult;

    }
}

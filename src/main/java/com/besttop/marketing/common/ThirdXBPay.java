package com.besttop.marketing.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.prefix.RedisPrefix_ERP;
import com.besttop.marketing.feign.PayFeignClient;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.pay.PayParam;
import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.util.NumberUtils;
import com.besttop.marketing.util.PayUtil;
import com.besttop.marketing.util.RedisGetInfoUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2021/6/28 14:22
 * @desc 新百卡支付
 **/
@Component
@Slf4j
public class ThirdXBPay implements Pay {
    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private RedisGetInfoUtil redisGetInfoUtil;

    @Autowired
    private PayFeignClient payFeignClient;

    public PayResult pay(PaymentParam param) {
        log.info("进入新百支付服务:{}", param);
        PayParam payParam = new PayParam();
        String[] types = param.getPayType().split(":");
        payParam.setPayType(types[2]);
        payParam.setNonceStr(UUID.randomUUID().toString().replaceAll("-", ""));
        payParam.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        payParam.setServiceId(CommonConstant.SERVICEID);
        payParam.setOrderDesc(loginCacheUtil.getStoreCode() + redisGetInfoUtil.getName(RedisPrefix_ERP.JI_GOU_XIN_XI, loginCacheUtil.getStoreCode()));
        payParam.setOrderNo(param.getOrderNo());
        payParam.setPayCode(param.getPayVoucher());
        payParam.setStrategyId(param.getStrategy());
        payParam.setType(param.getType());
        String transactionId = System.currentTimeMillis() + NumberUtils.get6Random();
        payParam.setReferNo(param.getReferNo());
        payParam.setMagneticStripe(param.getValueCardList().get(0).getMagneticStripe());
        payParam.setCardSuffix(param.getValueCardList().get(0).getCardSuffix());
        payParam.setFee(String.valueOf(param.getValueCardList().get(0).getPayAmount()));
        payParam.setType("2");
        payParam.setSign(PayUtil.getSign(payParam));
        log.info("新百支付服务入参 " + JSON.toJSONString(payParam));
        ResultEntity result = this.payFeignClient.xinbaiPay(payParam);
        log.info("新百支付服务返回 " + JSON.toJSONString(result));
        Integer flag = result.getFlag();
        Integer paystatus = JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getInteger("payStatus");

        if (flag == 1) {
            log.info("新百支付通信成功");
            if (paystatus == 1) {
                log.info("新百支付成功");
                log.info("新百扣款成功:{}", JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getJSONObject("payReqBody").getString("txNativeSequence"));
                payParam.setReferNo(JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getJSONObject("payReqBody").getString("txNativeSequence"));
                return new PayResult(1, null, transactionId, payParam.getReferNo());
            } else if (paystatus == 2) {
                log.info("新百支付处理中,进行轮询查询处理");
                boolean resultOrder = false;
                int count = 0;
                while (!resultOrder) {
                    try {
                        Thread.sleep(4 * 1000);

                        payParam.setType("4");
                        ResultEntity resultCheck = this.payFeignClient.xinbaiPay(payParam);
                        Integer flagFind = resultCheck.getFlag();
                        Integer paystatusFind = JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getInteger("payStatus");
                        if (flagFind == 1) {
                            if (paystatusFind == 1) {
                                log.info("新百支付成功");
                                log.info("新百扣款成功:{}", JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getJSONObject("payReqBody").getString("txNativeSequence"));
                                payParam.setReferNo(JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getJSONObject("payReqBody").getString("txNativeSequence"));
                                return new PayResult(1, null, transactionId, payParam.getReferNo());
                            } else if (paystatusFind == 2) {
                                log.info("新百支付处理中,进行轮询查询处理");
                                log.info("查询支付支付中");
                                count++;
                                if (count == 6) {
                                    resultOrder = true;
                                }
                            } else if (paystatusFind == 3) {
                                log.info("新百支付失败");
                                return new PayResult(-1, JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getString("thirdPayMsg"));
                            } else {
                                log.info("新百支付失败,未知异常");
                                return new PayResult(-1, JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getString("thirdPayMsg"));
                            }
                        } else {
                            log.info("新百支付轮询通信失败");
                            return new PayResult(-1, "新百支付通信失败,请联系管理员");
                        }
                    } catch (Exception e) {
                        log.info("失败:", e);
                        return new PayResult(-1, e.getMessage());
                    }
                }
            } else if (paystatus == 3) {
                log.info("新百支付失败");
                return new PayResult(-1, JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getString("thirdPayMsg"));
            } else {
                log.info("新百支付失败,未知异常");
                return new PayResult(-1, JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getString("thirdPayMsg"));
            }
        } else {
            log.info("新百支付通信失败");
            return new PayResult(-1, "新百支付通信失败,请联系管理员");
        }
        return new PayResult(-1, "新百支付未知异常,请联系管理员");
    }
}

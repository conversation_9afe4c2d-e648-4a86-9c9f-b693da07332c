package com.besttop.marketing.common;

import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;

/**
 * <p>Title: Pay</p>
 * <p>Description: Pay 支付接口，所有支付必须实现此接口</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 *       2019/7/24 0024 20:39 Create by coeus
 * </pre>
 */
public interface Pay {

    PayResult pay(PaymentParam param);
}

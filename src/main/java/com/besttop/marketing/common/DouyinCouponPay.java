package com.besttop.marketing.common;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.marketing.mapper.shopping.ShoppingOrderSkuMapper;
import com.besttop.marketing.mapper.shopping.ShoppingPayRecordMapper;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import com.besttop.marketing.model.shopping.result.OrderSkuResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponVerifyResult;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponLog;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.service.thirdparty.douyin.DouyinCouponVerifyService;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinCouponLogMapper;
import com.besttop.marketing.util.CollectionUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description 抖音券支付
 * @date 2025/6/23 15:21
 */
@Component
@Slf4j
public class DouyinCouponPay implements Pay {

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private DouyinCouponVerifyService douyinCouponVerifyService;

    @Autowired
    private DouyinCouponLogMapper douyinCouponLogMapper;
    @Autowired
    private ShoppingOrderSkuMapper shoppingOrderSkuMapper;
    @Autowired
    private ShoppingPayRecordMapper shoppingPayRecordMapper;


    @Override
    public PayResult pay(PaymentParam paymentParam) {

        String user = this.loginCacheUtil.getUserCode() + CommonConstant.MIDDLE_BAR + this.loginCacheUtil.getUserName();
        String store = this.loginCacheUtil.getStoreCode();

        // 获取券码，前端传入抖音券码
        String couponCode = paymentParam.getPayVoucher();
        BigDecimal payAmount = paymentParam.getPayAmount();
        String orderNo = paymentParam.getOrderNo();
        List<String> orderCodes = paymentParam.getOrderCodes();
        // 查询订单是否已经有抖音券支付。有的话不能再次支付
        QueryWrapper<ShoppingPayRecord> qw1 = new QueryWrapper<ShoppingPayRecord>();
        qw1.eq("pay_number", paymentParam.getPayNumber());
        qw1.eq("pay_type", paymentParam.getPayType());
        qw1.eq("status", CommonEnums.PAY_RECORD_STATUS_FINISH.getCode());
        List<ShoppingPayRecord> list = shoppingPayRecordMapper.selectList(qw1);
        if(CollectionUtil.isNotEmpty(list)) {
            return new PayResult(CommonConstant.INTEGER_ZERO, "订单只能核销一张抖音券", null);
        }
        // 初始化返回结果
        PayResult payResult = new PayResult();

        try {
            // 1. 参数校验
            if (StringUtils.isBlank(couponCode)) {
                log.error("抖音券支付失败：券码不能为空");
                return new PayResult(CommonConstant.INTEGER_ZERO, "券码不能为空", null);
            }

            if (payAmount == null || payAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.error("抖音券支付失败：支付金额必须大于0");
                return new PayResult(CommonConstant.INTEGER_ZERO, "支付金额必须大于0", null);
            }

            if (StringUtils.isBlank(store)) {
                log.error("抖音券支付失败：门店编码不能为空");
                return new PayResult(CommonConstant.INTEGER_ZERO, "门店编码不能为空", null);
            }

            // 根据订单号商品对应的品牌品类
            List<OrderSkuResult> orderSkuResults = shoppingOrderSkuMapper.querySkuClassBrandByOrderCode(orderCodes);

            // 2. 准备核销参数
            Map<String, Object> verifyParams = new HashMap<>();
            verifyParams.put("orderAmount", payAmount);
            verifyParams.put("orderNo", orderNo);
            verifyParams.put("payType", paymentParam.getPayType());
            verifyParams.put("payTime", new Date());
            verifyParams.put("orderCodes", orderCodes);
            verifyParams.put("skuCode", orderSkuResults.get(0).getSkuCode());
            verifyParams.put("classCode",orderSkuResults.get(0).getClassCode().substring(0,2));
            verifyParams.put("brandCode", orderSkuResults.get(0).getBrandCode());

            // 3. 调用券核销服务
            log.info("开始核销抖音券，券码: {}, 门店: {}, 支付金额: {}", couponCode, store, payAmount);
            DouyinCouponVerifyResult verifyResult = douyinCouponVerifyService.verifyCoupon(
                    couponCode, store, user, verifyParams);

            // 4. 处理核销结果
            if (!verifyResult.isSuccess()) {
                String errorMsg = "抖音券支付失败：" + verifyResult.getMessage();
                log.error(errorMsg);
                recordPayLog(couponCode, orderNo, user, store, payAmount, false, errorMsg);
                return new PayResult(CommonConstant.INTEGER_ZERO, errorMsg, null);
            }

            // 5. 获取核销券信息
            DouyinCoupon coupon = verifyResult.getCouponInfo();


            payResult.setPayStatus(CommonConstant.INTEGER_ONE); // 支付成功
            payResult.setTransactionId(coupon.getId());
            payResult.setThirdPayMsg("抖音券支付成功");
            payResult.setThirdPartyName("抖音券");
            // 如果有验证类型，添加到支付结果
//            if (StringUtils.isNotEmpty(verifyResult.getVerificationType())) {
//                if ("CASH".equals(verifyResult.getVerificationType())) {
//                    payResult.setThirdPartyName("抖音代金券");
//                } else if ("GIFT".equals(verifyResult.getVerificationType())) {
//                    payResult.setThirdPartyName("抖音提货券");
//                }
//            }

            // 7. 记录支付日志
            log.info("抖音券支付成功，券码: {}, 交易ID: {}, 金额: {}, 订单号: {}",
                    couponCode, couponCode, payAmount, orderNo);

            return payResult;
        } catch (Exception e) {
            String errorMsg = "抖音券支付处理异常: " + e.getMessage();
            log.error("抖音券支付异常", e);
            recordPayLog(couponCode, orderNo, user, store, payAmount, false, errorMsg);
            return new PayResult(CommonConstant.INTEGER_ZERO, errorMsg, null);
        }
    }

    /**
     * 记录支付日志
     *
     * @param couponCode 券码
     * @param orderNo 订单号
     * @param userId 用户ID
     * @param storeCode 门店编码
     * @param payAmount 支付金额
     * @param success 是否成功
     * @param message 消息
     */
    private void recordPayLog(String couponCode, String orderNo, String userId,
                              String storeCode, BigDecimal payAmount, boolean success, String message) {
        try {
            DouyinCouponLog log = new DouyinCouponLog();
            log.setId(UUID.randomUUID().toString().replace("-", ""));

            // 设置操作信息
            log.setOperationType(success ? "PAY_SUCCESS" : "PAY_FAILED");
            log.setIdempotencyKey(orderNo + "_PAY_" + System.currentTimeMillis());
            log.setRequestSource("EDP_PAY");

            // 设置请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("coupon_code", couponCode);
            requestData.put("order_no", orderNo);
            requestData.put("store_code", storeCode);
            requestData.put("user_id", userId);
            requestData.put("pay_amount", payAmount);
            log.setRequestPayloadJson(JSON.toJSONString(requestData));

            // 设置响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", success);
            responseData.put("message", message);
            log.setResponsePayloadJson(JSON.toJSONString(responseData));

            // 设置操作人和时间
            log.setOperatorId(userId);
            log.setOperationTime(new Date());

            // 设置结果状态
            log.setResultStatus(success ? DouyinCouponLog.RESULT_SUCCESS : DouyinCouponLog.RESULT_FAILED);

            // 如果失败，设置错误信息
            if (!success) {
                log.setErrorCode("PAY_FAILED");
                log.setErrorMessage(message);
            }

            // 插入日志记录
            douyinCouponLogMapper.insert(log);

        } catch (Exception e) {
            log.error("记录抖音券支付日志异常", e);
            // 记录日志失败不影响主流程
        }
    }

}

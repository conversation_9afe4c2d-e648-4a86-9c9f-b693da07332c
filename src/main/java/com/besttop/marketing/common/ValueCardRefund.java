package com.besttop.marketing.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.marketing.mapper.card.CardDetailMapper;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardLog;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.CustomerAccountLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.pay.RefundResult;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import com.besttop.marketing.service.card.CardDetailService;
import com.besttop.marketing.service.card.CardLogService;
import com.besttop.marketing.service.customer.CustomerAccountLogService;
import com.besttop.marketing.service.customer.CustomerAccountService;
import com.besttop.marketing.util.CodeUtils;
import com.besttop.marketing.util.Tools;
import com.besttop.mybatis.utils.PrimaryKeyUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: ValueCardRefund</p>
 * <p>Description: ValueCardRefund</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/6/4 14:34 Create by Sissi
 * </pre>
 */
@Component
public class ValueCardRefund implements Refund {

    @Autowired
    private CustomerAccountService accountService;

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private PrimaryKeyUtil primaryKeyUtil;

    @Autowired
    private CodeUtils codeUtils;

    @Autowired
    private CardDetailMapper cardDetailMapper;

    @Autowired
    private CardDetailService cardDetailService;

    @Autowired
    private CustomerAccountLogService logService;

    @Autowired
    private CardLogService cardLogService;

    @Override
    public RefundResult refund(RefundmentParam param) {
        QueryWrapper<CardDetail> qw = new QueryWrapper<>();
        qw.eq("card_no", param.getCardNo());
        CardDetail detail = cardDetailMapper.selectOne(qw);
        if (null == detail) {
            throw new RuntimeException("储值卡信息未找到，请核实");
        }
        String user = loginCacheUtil.getUserCode() + CommonConstant.MIDDLE_BAR + loginCacheUtil.getUserName();
        String store = loginCacheUtil.getStoreCode();
        QueryWrapper<CustomerAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("linked_code", param.getCardNo());
        //queryWrapper.eq("status", "erp:customer_account_status:1");
        CustomerAccount account = accountService.getOne(queryWrapper);
        if (null == account) {
            account = new CustomerAccount();
            account.setCode(primaryKeyUtil.generateKey("customer_account", codeUtils.findLenth(CommonEnums.BILL_CODE_TYPE.getCode(), null)));
            account.setAmount(param.getPayAmount());
            account.setCustomerCode(param.getCustomerCode());
            account.setType(CommonEnums.CUSTOMER_ACCOUNT_TYPE_A.getCode());
            account.setLinkedCode(param.getCardNo());
            account.setStatus(CommonEnums.CUSTOMER_ACCOUNT_STATUS_A.getCode());
            account.setCreateBy(user);
            account.setUpdateBy(user);
            account.setCreateTime(new Date());
            account.setUpdateTime(new Date());
            account.setExpireDate(detail.getExpireTime());
            accountService.save(account);
        } else {
            account.setUpdateBy(user);
            account.setUpdateTime(new Date());
            account.setAmount(account.getAmount().add(param.getPayAmount()));
            accountService.updateById(account);
        }
        String key = Tools.encryptHex(detail.getCardNo()+":"+(null == detail.getAmount() ? BigDecimal.ZERO:detail.getAmount().setScale(2)).toString());
        if(!key.equals(detail.getEncryptKey())){
            throw new RuntimeException("储值卡【"+detail.getCardNo()+"】余额请核实！");
        }

        detail.setAmount(detail.getAmount().add(param.getPayAmount()));
        detail.setEncryptKey(Tools.encryptHex(detail.getCardNo()+":"+detail.getAmount().setScale(2)));
        detail.setUpdateBy(user);
        detail.setUpdateTime(new Date());
        cardDetailService.updateById(detail);
        //记录日志
        CustomerAccountLog log = new CustomerAccountLog();
        log.setCustomerAccountCode(account.getCode());
        log.setCustomerCode(account.getCustomerCode());
        log.setStoreCode(loginCacheUtil.getStoreCode());
        log.setVoucherNumber(param.getRefundNumber());
        log.setOccurAmount(param.getPayAmount());
        log.setOccurTime(new Date());
        log.setBalance(param.getPayAmount());
        log.setType(CommonEnums.CUSTOMER_ACCOUNT_LOG_TYPE_C.getCode());
        log.setCreateBy(user);
        log.setCreateTime(new Date());
        logService.save(log);

        CardLog cardLog = new CardLog();
        cardLog.setTypeCode(detail.getTypeCode());
        cardLog.setCardNo(detail.getCardNo());
        cardLog.setCreateTime(new Date());
        cardLog.setCreateBy(user);
        cardLog.setTypeName(detail.getTypeName());
        cardLog.setPayAmount(param.getPayAmount().negate());
        cardLog.setOperationBy(user);
        cardLog.setOperationStore(store);
        cardLog.setOperationType(CommonEnums.CARD_LOG_OPRATION_E.getCode());
        cardLogService.save(cardLog);
        RefundResult refundResult = new RefundResult();
        refundResult.setRefundStatus(CommonConstant.INTEGER_ONE);
        param.setBalanceAmount(detail.getAmount());
        return refundResult;
    }
}

package com.besttop.marketing.common;

import com.alibaba.fastjson.JSON;
import com.besttop.common.model.ResultEntity;
import com.besttop.common.prefix.RedisPrefix_ERP;
import com.besttop.marketing.feign.PayFeignClient;
import com.besttop.marketing.feign.QuoteFeignClient;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.enums.PayEnums;
import com.besttop.marketing.model.pay.PayParam;
import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.service.pay.PayService;
import com.besttop.marketing.util.NumberUtils;
import com.besttop.marketing.util.PayUtil;
import com.besttop.marketing.util.RedisGetInfoUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;

/**
 * <p>Title: ThirdPay</p>
 * <p>Description: ThirdPay 第三方支付</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 *       2020/4/16 10:31 Create by Sissi
 * </pre>
 */
@Component
@Slf4j
public class ThirdPay implements Pay {

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private PayService payService;

    @Autowired
    private RedisGetInfoUtil redisGetInfoUtil;

    @Autowired
    private PayFeignClient payFeignClient;

    @Autowired
    private QuoteFeignClient quoteFeignClient;

    /**
     * 元转分单位
     */
    private static final BigDecimal UNIT = new BigDecimal(100);

    @Override
    public PayResult pay(PaymentParam param) {
        PayParam payParam = new PayParam();
        String[] types = param.getPayType().split(":");
        payParam.setPayType(types[2]);
        payParam.setNonceStr(UUID.randomUUID().toString().replaceAll("-", ""));
        payParam.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        payParam.setServiceId(CommonConstant.SERVICEID);
        payParam.setOrderDesc(loginCacheUtil.getStoreCode() + redisGetInfoUtil.getName(RedisPrefix_ERP.JI_GOU_XIN_XI, loginCacheUtil.getStoreCode()));
        payParam.setOrderNo(param.getOrderNo());
        payParam.setPayCode(param.getPayVoucher());
        payParam.setPayMoney(param.getPayAmount().multiply(UNIT).intValue());
        payParam.setStrategyId(param.getStrategy());
        payParam.setType(param.getType());
        //银联商务B扫C终端号
        Map<String, String> data = Maps.newHashMap();
        String storeCode = loginCacheUtil.getStoreCode();
        data.put("storeCode", storeCode);
        ResultEntity resultEntity = quoteFeignClient.getPayStoreTerminalByStoreCode(data);
//        data.put("storeCode", storeCode);
        
        if (resultEntity.getFlag() == 1 && StringUtils.isNotBlank(resultEntity.getMessage())) {
            payParam.setTerminal(resultEntity.getMessage());
        }
        
        //多点支付传收款机构
        if(PayEnums.DMALL_PAY.getCode().equals(param.getPayType()) 
        		|| PayEnums.DMALL_CARD_PAY.getCode().equals(param.getPayType())) {
        	payParam.setTerminal(storeCode);
        }
        
        log.info("====pay==isPos:{}, isCBPos:{}", param.getIsPos(), param.getIsCBPos());
        String transactionId = null;
        if (param.getIsPos() == 0) {
            return this.payService.pay(payParam);
        } else if (param.getIsPos() == 1) {
            transactionId = System.currentTimeMillis() + NumberUtils.get6Random();
            if(param.getIsEnterAagain() != null && param.getIsEnterAagain() == 1){
                //建行补录
                if (PayEnums.CB_POS_PAY.getCode().equals(param.getPayType())
                        && StringUtils.isNotBlank(param.getReferNo())
                        && param.getIsCBPos() == 0) {
                    param.setPayVoucher(param.getReferNo());
                    payParam.setPayCode(param.getPayVoucher());
                }
                //黄河补录
                if (PayEnums.YRRCB_PAY.getCode().equals(param.getPayType())
                        && StringUtils.isNotBlank(param.getReferNo())) {
                    param.setPayVoucher(param.getReferNo());
                    payParam.setPayCode(param.getPayVoucher());
                }
                transactionId = param.getPayVoucher();
            } else {
                if (PayEnums.CB_POS_PAY.getCode().equals(param.getPayType())) {
                    if (param.getIsCBPos() == 1) {
                        transactionId = "P" + transactionId;
                    } else {
                        //建行商户号后11位 + （时间戳 + 随机数）
                        transactionId = "71000000642" + transactionId;
                    }
                }
                if (PayEnums.ICBC_PAY.getCode().equals(param.getPayType())) {

                	if(StringUtils.isNotBlank(param.getIcbcNote())) {
                		if(param.getIcbcNote().equals("GF")) {
                			transactionId = "GF" + transactionId;
                		} else if(param.getIcbcNote().equals("GJ")) {
                			transactionId = "GJ" + transactionId;
                		} else if(param.getIcbcNote().equals("G")) {
                			transactionId = "G" + transactionId;
                		}
                	}
                }
                if (PayEnums.YRRCB_PAY.getCode().equals(param.getPayType())) {
                    transactionId = "P" + transactionId;
                }
            }
            log.info("====pay==transactionId:{}, referNo:{}", transactionId, param.getReferNo());
            if (StringUtils.isNotBlank(param.getReferNo())) {
                log.info("====pay==will=do=union=pay");
                payParam.setReferNo(param.getReferNo());
//                log.info("====pay=before=sign=payParam:{}", JSON.toJSONString(payParam));
                payParam.setSign(PayUtil.getSign(payParam));
//                log.info("====pay=after=sign=payParam:{}", JSON.toJSONString(payParam));
                log.info("====pay==支付服务入参 " + JSON.toJSONString(payParam));
                ResultEntity result = this.payFeignClient.unionPay(payParam);
                log.info("====pay==支付服务返回 " + JSON.toJSONString(result));
                if (result.getFlag() != 1) {
//                    return new PayResult(-1, result.getMessage());
                    return new PayResult(-1, result.getMessage(), transactionId);
                } else {
                    return new PayResult(1, null, transactionId);
                }
            } else {
                return new PayResult(1, null, transactionId);
            }
        }
        return new PayResult(1, null, transactionId);
    }

}
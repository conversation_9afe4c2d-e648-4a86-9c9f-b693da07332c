package com.besttop.marketing.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.feign.BigmemberFeignClient;
import com.besttop.marketing.mapper.pushorder.DmallAfterOrderMapper;
import com.besttop.marketing.mapper.pushorder.DmallOrderPushMapper;
import com.besttop.marketing.model.enums.DMallEnums;
import com.besttop.marketing.model.pay.RefundResult;
import com.besttop.marketing.model.pushorder.DmallAfterOrder;
import com.besttop.marketing.model.pushorder.DmallOrderPush;
import com.besttop.marketing.model.pushorder.DmallOrderVo;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import com.besttop.marketing.service.dmall.DmallAfterOrderService;
import com.besttop.marketing.util.CheckUtil;
import com.besttop.pay.common.entity.PayResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class DMallRefund implements Refund {

    @Autowired
    private BigmemberFeignClient bigmemberFeignClient;
    @Autowired
    private DmallAfterOrderMapper dmallAfterOrderMapper;
    @Autowired
    private DmallOrderPushMapper dmallOrderPushMapper;
    @Autowired
    private DmallAfterOrderService dmallAfterOrderService;

    @Override
    public RefundResult refund(RefundmentParam param) {
        log.info("进入多点支付退款接口:{}", param);

        List<DmallAfterOrder> list =dmallAfterOrderMapper.findAfterOrder(param.getPayNumber());

        if(list == null || list.size() == 0){
            throw new RuntimeException("未查询到多点支付订单");
        }

        int count = list.stream().map(DmallAfterOrder::getStatus).filter(status -> null !=status && status == 4).toArray().length;
        if (count == list.size()) {
            RefundResult refundResult = new RefundResult();
            refundResult.setRefundStatus(PayResultEnum.SUCCESS.getCode());
            return refundResult;
        }
        int sum = 0;
        for (DmallAfterOrder dmallAfterOrder : list) {

            if(dmallAfterOrder.getStatus() != null && dmallAfterOrder.getDmallApplyId() != null) {
                if(dmallAfterOrder.getStatus() == 4) {
                    sum++;
                }else {
                    String msg = "";
                    DmallOrderVo dmallOrderVo = new DmallOrderVo();
                    dmallOrderVo.setAsId(dmallAfterOrder.getDmallApplyId());
                    boolean flag = this.pushAfterOrder(dmallAfterOrder, dmallOrderVo,msg);
                    if(flag){
                        sum = this.getAfterPayStatus(dmallAfterOrder, sum);
                    }else {
                        RefundResult refundResult = new RefundResult();
                        refundResult.setRefundStatus(PayResultEnum.FAILED.getCode());
                        refundResult.setThirdRefundMsg(msg);
                        return refundResult;
                    }
                }
            }else {
                DmallOrderPush dmallOrderPush = dmallOrderPushMapper.findDmallOrder(dmallAfterOrder.getOrderId());
                CheckUtil.check(dmallOrderPush.getStatus() == DMallEnums.DM_PAY_PUSH_STATUS_FINISH.getId(), "订单未支付完成，不能发起售后单！");
                // 推送售后单
                DmallOrderVo dmallOrderVo = new DmallOrderVo();
                dmallOrderVo.setDmallOrderPush(dmallOrderPush);
                String msg = "";
                boolean flag = this.pushAfterOrder(dmallAfterOrder, dmallOrderVo,msg);

                if(flag){
                    sum = this.getAfterPayStatus(dmallAfterOrder, sum);
                }else {
                    RefundResult refundResult = new RefundResult();
                    refundResult.setRefundStatus(PayResultEnum.FAILED.getCode());
                    refundResult.setThirdRefundMsg(msg);
                    return refundResult;
                }

            }

        }
        log.info("多点支付退款完成，退款数量：sum={} ,size={}", sum, list.size());
        if(sum == list.size()) {
            LambdaUpdateWrapper<DmallOrderPush> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(DmallOrderPush::getOrderId, list.stream().map(DmallAfterOrder::getOrderId).toArray());
            updateWrapper.set(DmallOrderPush::getStatus, DMallEnums.DM_PAY_PUSH_STATUS_REFUND.getId());
            dmallOrderPushMapper.update(null, updateWrapper);
            RefundResult refundResult = new RefundResult();
            refundResult.setRefundStatus(PayResultEnum.SUCCESS.getCode());
            return refundResult;
        }else {
            RefundResult refundResult = new RefundResult();
            refundResult.setRefundStatus(PayResultEnum.PAYING.getCode());
            return refundResult;
        }

    }

    private int getAfterPayStatus(DmallAfterOrder dmallAfterOrder,int sum) {
            log.info("getAfterSaleDetail 数值 = {}", sum);
            DmallOrderVo orderVo = new DmallOrderVo();
            orderVo.setAsId(dmallAfterOrder.getDmallApplyId());
            ResultEntity<JSONObject> result = bigmemberFeignClient.getAfterSaleDetail(orderVo);
            log.info("getAfterSaleDetail 查询结果:{}", result);
            if(1 == result.getFlag() && result.getData() != null){
                JSONObject data = JSONObject.parseObject(result.getData().toString());
                // asStatus 售后单状态（1:待审批,2:售后驳回,3:待财务审批,4:财务驳回,5:待退款,6:退款完成,7无需退款,10:待审核,15：待三方回写，20:待退货，-99：用户撤销，-10：用户驳回，-20：退货取件失败，-30：删除）
                if(data.get("asStatus") != null && "6".equals(data.get("asStatus").toString())){
                    dmallAfterOrder.setStatus(DMallEnums.DM_AFTER_ORDER_STATUS_FINISH.getId());
                    dmallAfterOrder.setCreateTime(new Date());
                    dmallAfterOrderService.saveOrUpdate(dmallAfterOrder);
                    sum++;
                    log.info("getAfterSaleDetail 数值 0 = {}", sum);
                    return sum;
                }else {
                    boolean resultOrder = false;
                    int requetsCount = 0;
                    while (!resultOrder) {
                        try {
                            Thread.sleep(4 * 1000);
                            log.info("多点支付退款状态处理中");
                            log.info("getAfterSaleDetail0 轮询结果:{}", result);
                            result = bigmemberFeignClient.getAfterSaleDetail(orderVo);

                            log.info("getAfterSaleDetail1 轮询结果:{}", result);
                            if(1 == result.getFlag() && result.getData() != null){
                                JSONObject data1 = JSONObject.parseObject(result.getData().toString());
                                log.info("getAfterSaleDetail2 轮询结果:{}", data1);
                                if (data1.get("asStatus") != null
                                        && "6".equals(data1.get("asStatus").toString())) {
                                    dmallAfterOrder.setStatus(DMallEnums.DM_AFTER_ORDER_STATUS_FINISH.getId());
                                    dmallAfterOrder.setCreateTime(new Date());
                                    dmallAfterOrderService.saveOrUpdate(dmallAfterOrder);
                                    sum++;
                                    log.info("getAfterSaleDetail 数值 1 = {}", sum);
                                    return sum;
                                } else {
                                    requetsCount++;
                                    if (requetsCount == 75) {
                                        resultOrder = true;
                                    }
                                }
                            }else {
                                requetsCount++;
                                if (requetsCount == 75) {
                                    resultOrder = true;
                                }
                            }

                        } catch (Exception e) {
                            dmallAfterOrder.setStatus(DMallEnums.DM_AFTER_ORDER_STATUS_FAIL.getId());
                            dmallAfterOrder.setCreateTime(new Date());
                            dmallAfterOrderService.saveOrUpdate(dmallAfterOrder);
                            log.info("多点支付退款异常,请联系管理员 e={}", JSON.toJSONString(e.getStackTrace()));
                            throw new RuntimeException("多点支付退款异常,请联系管理员");
                        }
                    }
                }
            }
        return sum;
    }

    private boolean pushAfterOrder(DmallAfterOrder dmallAfterOrder, DmallOrderVo dmallOrderVo,String msg) {
        log.info("pushAfterOrder 推送售后单开始，orderId={}", dmallAfterOrder.getOrderId());
        ResultEntity<JSONObject> result = bigmemberFeignClient.pushAfterSaleOrder(dmallOrderVo);
        if(result.isSuccess()){
            JSONObject data = JSONObject.parseObject(result.getData().toString());
            dmallAfterOrder.setDmallApplyId(data.get("dmallApplyId").toString());
            dmallAfterOrder.setOrderId(data.get("orderId").toString());
            dmallAfterOrder.setStatus(DMallEnums.DM_AFTER_ORDER_STATUS_SUCCESS.getId());
            dmallAfterOrder.setCreateTime(new Date());
            dmallAfterOrderService.saveOrUpdate(dmallAfterOrder);
            return true;
        }else {
            dmallAfterOrder.setStatus(DMallEnums.DM_AFTER_ORDER_STATUS_FAIL.getId());
            dmallAfterOrder.setCreateTime(new Date());
            dmallAfterOrderService.saveOrUpdate(dmallAfterOrder);
            msg = result.getMessage();
            return false;
        }
    }
}

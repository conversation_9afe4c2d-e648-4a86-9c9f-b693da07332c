package com.besttop.marketing.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.common.model.ResultEntity;
import com.besttop.marketing.feign.PayFeignClient;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.pay.RefundResult;
import com.besttop.marketing.model.shopping.ShoppingPayThird;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import com.besttop.marketing.service.shopping.ShoppingPayThirdService;
import com.besttop.marketing.util.NumberUtils;
import com.besttop.pay.common.sm2.SM2SignUtil;
import com.besttop.redis.utils.LoginCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
@Slf4j
public class ValueXBCardRefund implements Refund {

    @Autowired
    private LoginCacheUtil loginCacheUtil;
    @Autowired
    private PayFeignClient payFeignClient;
    @Autowired
    private ShoppingPayThirdService payThirdService;

    @Override
    public RefundResult refund(RefundmentParam param) {
        log.info("进入新百退款接口:{}", param);
        String user = loginCacheUtil.getUserCode() + CommonConstant.MIDDLE_BAR + loginCacheUtil.getUserName();
        String store = loginCacheUtil.getStoreCode();
        //查询退款码
        QueryWrapper<ShoppingPayThird> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transaction_voucher", param.getPayVoucher());
        queryWrapper.eq("is_cancel_return", 0);
/*        queryWrapper.eq("pay_account",param.getMagneticStripe());
        queryWrapper.eq("transaction_search_no",param.getCardSuffix());*/
        ShoppingPayThird payThird = payThirdService.getOne(queryWrapper);
        String transactionId = System.currentTimeMillis() + NumberUtils.get6Random();
        param.setServiceId("1000");
        param.setReferNo(param.getReferNo());
        param.setTxNativeSequence(payThird.getReferNo());
        param.setMagneticStripe(param.getMagneticStripe());
        param.setCardSuffix(param.getCardSuffix());
        param.setFee(String.valueOf(param.getPayAmount()));
        param.setType("3");
        param.setOrderNo(transactionId);
        param.setNonceStr(UUID.randomUUID().toString().replaceAll("-", ""));
        param.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        param.setSign(SM2SignUtil.sm2Sign(JSON.parseObject(JSON.toJSONString(param), Map.class), CommonConstant.SIGN_KEY));
        log.info("新百退款服务入参 " + JSON.toJSONString(param));
        ResultEntity result = this.payFeignClient.xinbaiPay(param);
        log.info("新百退款服务返回 " + JSON.toJSONString(result));
        RefundResult refundResult = JSON.parseObject(JSON.toJSONString(result.getData()), RefundResult.class);
        Integer flag = result.getFlag();
        Integer paystatus = JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getInteger("payStatus");
        if (flag == 1) {
            log.info("新百退款通信成功");
            if (paystatus == 1) {
                log.info("新百退款成功");
                log.info("新百退款服务状态 " + JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getInteger("payStatus"));
                refundResult.setRefundStatus(paystatus);
                refundResult.setReferNo(JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getJSONObject("payReqBody").getString("txNativeSequence"));
                return refundResult;
            } else if (paystatus == 2) {
                log.info("新百退款中");
                boolean resultOrder = false;
                int count = 0;
                while (!resultOrder) {
                    try {
                        Thread.sleep(4 * 1000);
                        log.info("新百支付状态处理中");
                        param.setType("4");
                        ResultEntity resultCheck = this.payFeignClient.xinbaiPay(param);
                        log.info("轮询结果:{}", resultCheck);
                        Integer flagCheck = resultCheck.getFlag();
                        Integer paystatusCheck = JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getInteger("payStatus");
                        if (flagCheck == 1) {
                            if (paystatusCheck == 1) {
                                log.info("新百退款成功");
                                refundResult.setRefundStatus(paystatusCheck);
                                refundResult.setReferNo(JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getJSONObject("payReqBody").getString("txNativeSequence"));
                                return refundResult;
                            } else if (paystatusCheck == 2) {
                                log.info("查询支付支付中");
                                count++;
                                if (count == 6) {
                                    resultOrder = true;
                                }
                            } else if (paystatusCheck == 3) {
                                log.info("新百退款失败");
                                throw new RuntimeException(JSONObject.parseObject(JSON.toJSONString(resultCheck)).getJSONObject("data").getString("thirdPayMsg"));
                            } else {
                                throw new RuntimeException("新百退款未知异常");
                            }
                        } else {
                            log.info("新百退款通信失败");
                            throw new RuntimeException("新百支付通信失败,请联系管理员");
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("新百退款异常,请联系管理员");
                    }
                }
                throw new RuntimeException("新百退款异常,请联系管理员");
            } else if (paystatus == 3) {
                log.info("新百退款失败");
                throw new RuntimeException(JSONObject.parseObject(JSON.toJSONString(result)).getJSONObject("data").getString("thirdPayMsg"));
            } else {
                throw new RuntimeException("新百退款未知异常");
            }
        } else {
            log.info("新百退款通信失败");
            throw new RuntimeException("新百支付通信失败,请联系管理员");
        }
    }
}

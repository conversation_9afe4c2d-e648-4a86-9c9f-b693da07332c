package com.besttop.marketing.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.CustomerAccountLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.enums.PayEnums;
import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.service.customer.CustomerAccountLogService;
import com.besttop.marketing.service.customer.CustomerAccountService;
import com.besttop.marketing.util.NumberUtils;
import com.besttop.redis.utils.LoginCacheUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <p>Title: CoinMortgagePay</p>
 * <p>Description: CoinMortgagePay 以币抵物</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/6/9 15:28 Create by Sissi
 * </pre>
 */
@Component
public class CoinMortgagePay implements Pay {

    @Autowired
    private CustomerAccountService accountService;

    @Autowired
    private CustomerAccountLogService logService;

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    /**
     * @param param
     * @return com.besttop.marketing.model.pay.PayResult
     * @methodName pay
     * @description 以币抵物支付
     * <AUTHOR>
     * @date 2020/6/9 15:29
     */
    @Override
    public PayResult pay(PaymentParam param) {
        String user = loginCacheUtil.getUserCode() + CommonConstant.MIDDLE_BAR + loginCacheUtil.getUserName();
        if (CollectionUtils.isEmpty(param.getValueCardList())) {
            throw new RuntimeException("请选择需要支付的电子币");
        }
        List<CustomerAccount> accounts = new ArrayList<>();
        List<CustomerAccountLog> accountLogs = new ArrayList<>();
        List<PaymentParam> paramList = this.getCoinsAmount(param);
        for (PaymentParam paymentParam : paramList) {
            QueryWrapper<CustomerAccount> qw = new QueryWrapper<>();
            qw.eq("code", paymentParam.getCardNo());
            CustomerAccount account = this.accountService.getOne(qw);
            if (null == account) {
                throw new RuntimeException("账户异常，请核实");
            }
            account.setUseAmount(account.getUseAmount().add(paymentParam.getPayAmount()));
            if (account.getAmount().compareTo(account.getUseAmount()) < 0) {
                throw new RuntimeException("余额不足，请核实");
            }
            if (account.getAmount().compareTo(account.getUseAmount()) == 0) {
                account.setStatus(CommonEnums.CUSTOMER_ACCOUNT_STATUS_D.getCode());
            }
            account.setUpdateTime(new Date());
            account.setUpdateBy(user);
            accounts.add(account);

            CustomerAccountLog accountLog = new CustomerAccountLog();
            accountLog.setVoucherNumber(param.getPayNumber());
            accountLog.setBalance(account.getAmount().subtract(account.getUseAmount()));
            accountLog.setType(CommonEnums.CUSTOMER_ACCOUNT_LOG_TYPE_B.getCode());
            accountLog.setCustomerAccountCode(account.getCode());
            accountLog.setCustomerCode(account.getCustomerCode());
            accountLog.setStoreCode(loginCacheUtil.getStoreCode());
            accountLog.setOccurTime(new Date());
            accountLog.setOccurAmount(paymentParam.getPayAmount());
            accountLog.setCreateTime(new Date());
            accountLog.setCreateBy(user);
            accountLogs.add(accountLog);
        }
        accountService.updateBatchById(accounts);
        logService.saveBatch(accountLogs);
        return new PayResult(CommonConstant.INTEGER_ONE, null, System.currentTimeMillis() + NumberUtils.get6Random());
    }


    private List<PaymentParam> getCoinsAmount(PaymentParam param) {
        BigDecimal totalAmount = param.getPayAmount();
        BigDecimal totalAmountA = param.getPayAmount();
        List<PaymentParam> toList = accountService.queryByOrderCode(param.getOrderCodes());
        List<PaymentParam> fromList = param.getValueCardList();
        fromList.forEach(s -> {
            QueryWrapper<CustomerAccount> qw = new QueryWrapper<>();
            qw.eq("code", s.getCardNo());
            CustomerAccount account = accountService.getOne(qw);
            s.setCoinType(account.getLinkedCode());
            s.setPayAmount(account.getAmount().subtract(account.getUseAmount()));
            if (s.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                throw new RuntimeException("余额不足，请核实");
            }
        });

        List<PaymentParam> resultList = new ArrayList<>();
        for (PaymentParam from : fromList) {
            if (from.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            for (PaymentParam to : toList) {
                if (from.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                if (from.getCoinType().equals(to.getCoinType())) {
                    PaymentParam card = new PaymentParam();
                    //匹配最小金额
                    BigDecimal minAmount = getMinAmount(totalAmount, from.getPayAmount(), to.getPayAmount());
                    PaymentParam cardResult = resultList.stream().filter(s -> from.getCardNo().equals(s.getCardNo())).findAny().orElse(null);
                    if (null != cardResult) {
                        card.setPayAmount(card.getPayAmount().add(minAmount));
                    } else {
                        card.setPayAmount(minAmount);
                        card.setCardNo(from.getCardNo());
                        card.setPayType(PayEnums.MORTGAGE.getCode());
                    }
                    resultList.add(card);
                    totalAmount = totalAmount.subtract(minAmount);
                    from.setPayAmount(from.getPayAmount().subtract(minAmount));
                    if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                }
            }
        }
        BigDecimal amount = resultList.stream().map(PaymentParam::getPayAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        if (totalAmountA.compareTo(amount) > 0){
            throw new RuntimeException("余额不足，请核实");
        }
        return resultList;
    }

    private static BigDecimal getMinAmount(BigDecimal a, BigDecimal b, BigDecimal c) {
        BigDecimal min = a.compareTo(b) < 0 ? a : b;
        min = min.compareTo(c) < 0 ? min : c;
        return min;
    }
}

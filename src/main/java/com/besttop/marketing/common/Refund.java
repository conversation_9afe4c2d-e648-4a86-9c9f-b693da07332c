package com.besttop.marketing.common;

import com.besttop.marketing.model.pay.RefundResult;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;

/**
 * <p>Title: Refund</p>
 * <p>Description: Refund</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/5/3 14:59 Create by zx
 * </pre>
 */
public interface Refund {

    /**
     * @param param
     * @return com.besttop.marketing.model.pay.RefundResult
     * @methodName refund
     * @description 退款
     * <AUTHOR>
     * @date 2020/5/3 15:21
     */
    RefundResult refund(RefundmentParam param);
}

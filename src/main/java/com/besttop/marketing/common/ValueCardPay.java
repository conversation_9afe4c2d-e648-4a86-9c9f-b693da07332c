package com.besttop.marketing.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.marketing.mapper.card.CardDetailMapper;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardLog;
import com.besttop.marketing.model.constant.CommonConstant;
import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.CustomerAccountLog;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.model.pay.PayResult;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import com.besttop.marketing.service.card.CardDetailService;
import com.besttop.marketing.service.card.CardLogService;
import com.besttop.marketing.service.customer.CustomerAccountLogService;
import com.besttop.marketing.service.customer.CustomerAccountService;
import com.besttop.marketing.util.CollectionUtil;
import com.besttop.marketing.util.NumberUtils;
import com.besttop.marketing.util.Tools;
import com.besttop.redis.utils.LoginCacheUtil;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: CardValuePay</p>
 * <p>Description: CardValuePay 储值卡支付</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/6/3 16:29 Create by Sissi
 * </pre>
 */
@Component
@Slf4j
public class ValueCardPay implements Pay {

    @Autowired
    private CustomerAccountService accountService;

    @Autowired
    private LoginCacheUtil loginCacheUtil;

    @Autowired
    private CustomerAccountLogService logService;

    @Autowired
    private CardDetailMapper cardDetailMapper;

    @Autowired
    private CardDetailService cardDetailService;

    @Autowired
    private CardLogService cardLogService;

    /**
     * @param paymentParam
     * @return com.besttop.marketing.model.pay.RefundResult
     * @methodName pay
     * @description 储值卡支付
     * <AUTHOR>
     * @date 2020/5/3 15:21
     */
    @Override
    public PayResult pay(PaymentParam paymentParam) {
        List<PaymentParam> valueCardList = paymentParam.getValueCardList();
        String user = this.loginCacheUtil.getUserCode() + CommonConstant.MIDDLE_BAR + this.loginCacheUtil.getUserName();
        String store = this.loginCacheUtil.getStoreCode();
        List<CustomerAccount> accounts = new ArrayList<>();
        List<CardDetail> details = new ArrayList<>();
        List<CardLog> cardLogs = new ArrayList<>();
        List<CustomerAccountLog> logs = new ArrayList<>();
        for (PaymentParam param : valueCardList) {
            QueryWrapper<CustomerAccount> qw = new QueryWrapper<>();
            qw.eq("linked_code", param.getCardNo());
            CustomerAccount account = this.accountService.getOne(qw);

            CardDetail cardDetail = this.cardDetailMapper.findByCardNo(param.getCardNo());

            if (/*null == account ||*/ null == cardDetail) {
                throw new RuntimeException(param.getCardNo() + "储值卡信息未找到，请核实");
            }
            if (cardDetail.getAmount().compareTo(param.getPayAmount()) == -1) {
                throw new RuntimeException(param.getCardNo() + "储值卡余额不足，请重试");
            }
            log.info("储值卡支付扣除余额 ====11" + cardDetail.getAmount());
            String key = Tools.encryptHex(cardDetail.getCardNo()+":"+(null == cardDetail.getAmount() ? BigDecimal.ZERO:cardDetail.getAmount().setScale(2)).toString());
            if(!key.equals(cardDetail.getEncryptKey())){
                throw new RuntimeException("储值卡【"+cardDetail.getCardNo()+"】余额请核实！");
            }
            cardDetail.setAmount(cardDetail.getAmount().subtract(param.getPayAmount()));
            cardDetail.setEncryptKey(Tools.encryptHex(cardDetail.getCardNo()+":"+cardDetail.getAmount().setScale(2)));
            log.info("储值卡支付扣除余额 ====22" + cardDetail.getAmount());
            cardDetail.setStatus(CommonEnums.CARD_DETAIL_STATUS_D.getCode());
            cardDetail.setUpdateTime(new Date());
            cardDetail.setUpdateBy(user);
            details.add(cardDetail);
            //储值卡余额
            param.setBalanceAmount(cardDetail.getAmount());

            CardLog cardLog = new CardLog();
            cardLog.setTypeCode(cardDetail.getTypeCode());
            cardLog.setCardNo(cardDetail.getCardNo());
            cardLog.setCreateTime(new Date());
            cardLog.setCreateBy(user);
            cardLog.setTypeName(cardDetail.getTypeName());
            cardLog.setPayAmount(param.getPayAmount());
            cardLog.setOperationBy(user);
            cardLog.setOperationStore(store);
            cardLog.setOperationType(CommonEnums.CARD_LOG_OPRATION_E.getCode());
            cardLogs.add(cardLog);
//            if (account.getAmount().subtract(account.getUseAmount()).compareTo(param.getPayAmount()) == -1) {
//                throw new RuntimeException(param.getCardNo() + "储值卡余额不足，请重试");
//            }
            if (account != null) {
                log.info("储值卡支付扣除余额 ====33" + account.getUseAmount());
                account.setUseAmount(account.getUseAmount().add(param.getPayAmount()));
                log.info("储值卡支付扣除余额 ====44" + account.getUseAmount());
//            if (account.getUseAmount().compareTo(account.getAmount()) == 0) {
                account.setStatus(CommonEnums.CUSTOMER_ACCOUNT_STATUS_D.getCode());
//            }
                account.setUpdateTime(new Date());
                if (null == account.getExpireDate()) {
                    account.setExpireDate(cardDetail.getExpireTime());
                }
                account.setUpdateBy(user);
                accounts.add(account);
                //储值卡余额
//   fixme  多减了             param.setBalanceAmount(account.getAmount().subtract(account.getUseAmount()));

                CustomerAccountLog log = new CustomerAccountLog();
                log.setBalance(account.getAmount().subtract(account.getUseAmount()));
                log.setCustomerCode(account.getCustomerCode());
                log.setCustomerAccountCode(account.getCode());
                log.setType(CommonEnums.CUSTOMER_ACCOUNT_LOG_TYPE_B.getCode());
                log.setStoreCode(this.loginCacheUtil.getStoreCode());
                log.setCreateBy(user);
                log.setCreateTime(new Date());
                log.setOccurTime(new Date());
                log.setOccurAmount(param.getPayAmount());
                log.setVoucherNumber(paymentParam.getPayNumber());
                logs.add(log);
            }
        }
        this.cardDetailService.updateBatchById(details);
        this.cardLogService.saveBatch(cardLogs);
        if (CollectionUtil.isNotEmpty(accounts)) {
            this.accountService.updateBatchById(accounts);
        }
        if (CollectionUtil.isNotEmpty(logs)) {
            this.logService.saveBatch(logs);
        }
        PayResult payResult = new PayResult();
        payResult.setPayStatus(CommonConstant.INTEGER_ONE);
        payResult.setTransactionId(System.currentTimeMillis() + NumberUtils.get6Random());
        return payResult;
    }
}

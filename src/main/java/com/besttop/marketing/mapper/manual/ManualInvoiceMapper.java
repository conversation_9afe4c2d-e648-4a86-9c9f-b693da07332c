package com.besttop.marketing.mapper.manual;

import com.besttop.marketing.model.manual.ManualInvoice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.model.shopping.result.InvoiceOrder;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import com.besttop.marketing.model.shopping.result.OrdersResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 手工开票 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Component
public interface ManualInvoiceMapper extends BaseMapper<ManualInvoice> {

    /**
     * @param code
     * @return com.besttop.common.model.ResultEntity
     * @methodName addManualInvoice
     * @description 查询手工开票基础信息
     * <AUTHOR>
     * @date 2020/6/18 15:18
     */
    OrderAndSkuResult queryByCode(String code);

    /**
     * @param orderCodes
     * @return int
     * @methodName updateInvoiceNum
     * @description 更新打印发票次数
     * <AUTHOR>
     * @date 2020/6/18 16:06
     */
    int updateInvoiceNum(@Param("orderCodes") List<String> orderCodes);


    /**
     * @param invoice
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryManualInvoice
     * @description 多条件查询手工开票列表
     * <AUTHOR>
     * @date 2020/6/18 17:00
     */
    List<ManualInvoice> queryManualInvoice(ManualInvoice invoice);

    /**
     * @methodName queryInvoiceOrders
     * @description 查询待开票销售单/预售单
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/6/23 14:19
     */
    List<OrdersResult> queryInvoiceOrders(OrderQueryParam param);

    List<String> queryBySourceCode(@Param("sourceCode") String sourceCode);

    List<InvoiceOrder> queryPrintInvoice(ManualInvoice invoice);
}

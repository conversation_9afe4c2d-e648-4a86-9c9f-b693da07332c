package com.besttop.marketing.mapper.manual;

import com.besttop.marketing.model.manual.ManualGiftRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.schedule.param.ScheduleGiftRecordParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 手动礼品发放记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */
@Component
public interface ManualGiftRecordMapper extends BaseMapper<ManualGiftRecord> {

    ManualGiftRecord checkData(ManualGiftRecord param);

    ManualGiftRecord getScheduleRule(ManualGiftRecord param);

    List<ManualGiftRecord> selectOrderInfo(ManualGiftRecord param);

    List<ScheduleGiftRecordParam> selectOrder(@Param("orderCode") String orderCode);

    boolean updatePromtion(String code);

    @Delete("delete from manual_gift_record where id=#{id}")
    int deleteInfoById(@Param("id") String id);

    boolean updateIsRecycle(@Param("codes") List<String> codes);
    
    List<String> queryGrantGiftsOrderCodes(@Param("orderCode") String orderCode);

	String queryGivedGiftsRuleCodesByOrderCodes(List<String> orderCodes);
}



package com.besttop.marketing.mapper.growthvalue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.growthvalue.GrowthValueDefine;
import com.besttop.marketing.model.growthvalue.GrowthValueDefineDetail;
import org.apache.ibatis.annotations.Delete;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 成长值生成规则定义明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
@Component
public interface GrowthValueDefineDetailMapper extends BaseMapper<GrowthValueDefineDetail> {

    @Delete("delete from growth_value_define_detail where define_code = #{defineCode}")
    int deleteByDefineCode(String defineCode);
}

package com.besttop.marketing.mapper.growthvalue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.growthvalue.GrowthValueDefine;
import com.besttop.marketing.model.growthvalue.param.GrowthValueDefineParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成长值生成规则定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
@Component
public interface GrowthValueDefineMapper extends BaseMapper<GrowthValueDefine> {

    List<GrowthValueDefine> findBySelected(GrowthValueDefineParam param);

    List<Map<String, String>> queryBrandClass(@Param("skuCodes") List<String> skuCodes, @Param("len") int len);

    List<GrowthValueDefine> queryGrowthValue(GrowthValueDefineParam param);

}

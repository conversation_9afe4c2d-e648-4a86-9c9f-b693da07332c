package com.besttop.marketing.mapper.storage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.storage.StorageInoutLog;
import com.besttop.marketing.model.storage.StorageInoutParam;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 仓库出入库日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-07
 */
@Component
public interface StorageInoutLogMapper extends BaseMapper<StorageInoutLog> {

    List<StorageInoutLog> findInoutLogToApp(StorageInoutParam storageInoutParam);
}

package com.besttop.marketing.mapper.storage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.storage.StorageInoutDetail;
import com.besttop.marketing.model.storage.StorageInoutDetailResult;
import com.besttop.marketing.model.storage.StorageInoutParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 验收入库单明细表 mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Component
public interface StorageInoutDetailMapper extends BaseMapper<StorageInoutDetail> {

    /**
     * @param storageInoutParam
     * @return java.util.List<com.besttop.logistics.model.result.StorageInoutDetailResult>
     * @methodName findDetailByEntryCode
     * @description TODO   根据单据号查询明细
     * <AUTHOR>
     * @date 2020/2/14 11:44
     */
    List<StorageInoutDetailResult> findDetailByEntryCode(StorageInoutParam storageInoutParam);

    @Delete("delete from storage_inout_detail where storage_inout_code = #{code}")
    int deleteBySourceCode(@Param(value = "code")String code);
}

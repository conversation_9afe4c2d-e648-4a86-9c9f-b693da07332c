package com.besttop.marketing.mapper.storage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.logistics.LogisticsPackageCarryFee;
import com.besttop.marketing.model.storage.StorageInOutToAppResult;
import com.besttop.marketing.model.storage.StorageInout;
import com.besttop.marketing.model.storage.StorageInoutParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 验收入(退)库单 mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Component
public interface StorageInoutMapper extends BaseMapper<StorageInout> {

    /**
     * @param code
     * @return java.lang.String
     * @methodName findMaxCode
     * @description TODO 查最大code
     * <AUTHOR>
     * @date 2020/1/16 14:40
     */
    String findMaxCode(String code);

    List<StorageInout> findBySourceBillCodeOut(List<String> logisticsCodes);

    List<StorageInout> findBySourceSelfRaising(List<String> sourceBillCodes);

    List<String> findStatusBySourceBillCode(String sourceBillCode);

    StorageInOutToAppResult findInOutToAppByCode(StorageInoutParam storageInoutParam);

    /**
     * @return java.util.List<com.besttop.marketing.model.logistics.LogisticsPackageCarryFee>
     * @methodName selectFee
     * @description 查询费用
     * @params [goodsCode]
     * <AUTHOR>
     * @date 2020/5/29 11:16
     */
    List<LogisticsPackageCarryFee> selectFee(@Param("goodsCode") String goodsCode);

    @Delete("delete from storage_inout where source_bill_code = #{code}")
    int deleteBySourceCode(@Param(value = "code")String code);

    List<StorageInout> findBySourceBillCodeOutCancelExit(String logisticsCode);

    List<StorageInout> findOutBySource(String sourceBillCode);

    boolean deleteEntryBySourceCode(String sourceCode);

}

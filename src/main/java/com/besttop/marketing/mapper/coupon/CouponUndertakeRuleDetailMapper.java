package com.besttop.marketing.mapper.coupon;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.coupon.CouponUndertakeRuleDetail;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CouponUndertakeRuleDetailMapper extends BaseMapper<CouponUndertakeRuleDetail> {

    @Select(" select pay_type" + //
                "    from bt_erp_ledger.interface_sap_pay_type" + //
                "    where store_code='通用'" + //
                "      and IFNULL(account_code,'')<>''" + //
                " and flag ='0' " + //
                "      order by sort,note,pay_type,account_code;" + //
                "")
    List<String> findAccountCode();

    @Select(" select sap_pay_type" + //
            "    from bt_erp_ledger.interface_sap_pay_type_comp" + //
            "    where edp_pay_type= #{payTypeName}" + //
            "")
    String findSapPayTypeComp(String payTypeName);
}
package com.besttop.marketing.mapper.coupon;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.coupon.CouponBalance;
import com.besttop.marketing.model.coupon.param.CouponBalanceParam;
import com.besttop.marketing.model.coupon.result.CouponBalanceResult;

/**
 * <p>
 * 机构优惠券余额表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
public interface CouponBalanceMapper extends BaseMapper<CouponBalance> {

    /**
     *查询导购开票中的A账户
     */
    CouponBalanceResult queryA(CouponBalanceParam param);

    /**
     *查询导购开票中的B账户
     */
    CouponBalanceResult queryB(CouponBalanceParam param);
}

package com.besttop.marketing.mapper.logistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.logistics.LogisticsDeliver;
import com.besttop.marketing.model.sku.SkuStockParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 物流配送单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-02
 */
@Component
public interface LogisticsDeliverMapper extends BaseMapper<LogisticsDeliver> {

    List<LogisticsDeliver> findDeliverByExitSource(List<String> logisticsCodes);

    List<LogisticsDeliver> findDeliverByChangeSource(String sourceBillCode);

    List<LogisticsDeliver> findDeliverByChange(String sourceBillCode);

    @Delete("delete from logistics_deliver where source_bill_code = #{code}")
    int delectBySourceBillCode(@Param(value = "code") String code);

    @Delete("delete from logistics_deliver where source_bill_code = #{code}")
    int delectBySourceCode(@Param(value = "code") String code);

    List<LogisticsDeliver> findDeliverByExitSourceCode(String logisticsCode);

    boolean updateReject(@Param("orderCodes") List<String> orderCodes);

    List<LogisticsDeliver> findDeliverByAppSource(String sourceBillCode);

    boolean updateStatus(@Param("orderCodes") List<String> orderCodes);

    @Select("select sd.code FROM shopping_deliver sd left join logistics_deliver ld on sd.code = ld.source_bill_code WHERE sd.source_code =#{sourceCode} and sd.is_oldfornew =1 and ld.status !='erp:logistics_deliver_status:6'")
    List<String> findoldfornewCode(@Param("sourceCode")String sourceCode);

    LogisticsDeliver findDeliverParam(SkuStockParam skuStockParam);

    List<LogisticsDeliver> findStatusBySoure(@Param("orderCodes")List<String> orderCodes);

    List<LogisticsDeliver> findChannelDeliverByChangeSource(String code);

    boolean updateIsReturn(String logisticsCode);

    @Select(" SELECT f.main_id FROM bt_erp_base.dmall_order_edp a LEFT JOIN bt_erp_base.dmall_order_main f ON f.main_uuid = a.main_uuid WHERE a.edp_code = #{code} limit 1")
    String findOrderId(@Param("code") String code);

    String findSkuBarcodeBySkuCode(String skuCode);
}

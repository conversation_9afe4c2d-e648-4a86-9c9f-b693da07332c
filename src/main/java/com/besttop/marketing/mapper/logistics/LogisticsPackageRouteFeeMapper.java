package com.besttop.marketing.mapper.logistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.logistics.LogisticsPackageRouteFee;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * <p>
 * 件型线路费用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-21
 */
@Repository
public interface LogisticsPackageRouteFeeMapper extends BaseMapper<LogisticsPackageRouteFee> {


    @Select("select f.logistics_fee logisticsFee" +
            "      from logistics_package_route_fee f" +
            "      left join logistics_package_goods g on g.package_type_code = f.model_code" +
            "    where f.del_flag =0 and g.del_flag =0" +
            "    and f.route_code = #{routeCode}" +
            "    and g.goods_code = #{goodsCode}" +
            "    group by f.route_code limit 1")
    BigDecimal queryPackageFee(@Param("goodsCode") String goodsCode,@Param("routeCode")String routeCode);

    @Select("select f.layer_fee layerFee" +
            "      from logistics_package_layer_fee f" +
            "      left join logistics_package_goods g on g.package_type_code = f.model_code" +
            "    where f.del_flag =0 and g.del_flag =0" +
            "    and f.layer = #{layer}" +
            "    and g.goods_code = #{goodsCode}" +
            "    group by f.layer limit 1")
    BigDecimal queryLayerFee(@Param("goodsCode") String goodsCode,@Param("layer")String routeCode);

}

package com.besttop.marketing.mapper.logistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.logistics.LogisticsDeliverLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 物流配送日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-02
 */
@Component
public interface LogisticsDeliverLogMapper extends BaseMapper<LogisticsDeliverLog> {

    List<LogisticsDeliverLog> findDeliverLogByOrderCode(String orderCode);

    @Delete("delete from logistics_deliver_log where deliver_code = #{code}")
    int delDeliverLogByOrderCode(@Param(value = "code") String code);
}

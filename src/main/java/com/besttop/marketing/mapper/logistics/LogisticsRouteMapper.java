package com.besttop.marketing.mapper.logistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.logistics.LogisticsRoute;
import com.besttop.marketing.model.logistics.LogisticsRouteDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 配送路线信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-25
 */
@Repository
public interface LogisticsRouteMapper extends BaseMapper<LogisticsRoute> {

    /**
     * @return java.util.List<com.besttop.marketing.model.logistics.LogisticsRouteDetail>
     * @methodName selectRouteByStoreCode
     * @description 根据机构编码获取机构的线路
     * @params [storeCode]
     * <AUTHOR>
     * @date 2020/3/19 15:18
     */
    List<LogisticsRouteDetail> selectRouteByStoreCode(@Param(value = "storeCode") String storeCode);

    /**
     * 根据机构编码获取机构的线路
     *
     * @param storeCode 机构编码
     * @return 机构的线路
     * @auther ray
     * @since 2021-09-08
     */
    List<LogisticsRoute> selectLogisticsRouteByStoreCode(@Param(value = "storeCode") String storeCode);

}

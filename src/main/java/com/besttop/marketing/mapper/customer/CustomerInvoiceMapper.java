package com.besttop.marketing.mapper.customer;

import com.besttop.marketing.model.customer.CustomerInvoice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 顾客税票信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Component
public interface CustomerInvoiceMapper extends BaseMapper<CustomerInvoice> {
    /**
     * @return java.util.List<com.besttop.marketing.model.customer.CustomerInvoice>
     * @methodName selectCustomerNews
     * @description 查询顾客税票信息
     * @params []
     * <AUTHOR>
     * @date 2020/4/9 19:15
     */
    List<CustomerInvoice> selectCustomerNews(@Param("customerCode") String customerCode);
}

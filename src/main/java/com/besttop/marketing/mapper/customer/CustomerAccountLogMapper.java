package com.besttop.marketing.mapper.customer;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.customer.CustomerAccountLog;
import com.besttop.marketing.model.customer.CustomerAccountLogRule;
import com.besttop.marketing.model.customer.result.CustomerAccountLogResult;

/**
 * <p>
 * 顾客账户日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface CustomerAccountLogMapper extends BaseMapper<CustomerAccountLog> {

    /**
     *查询储值卡的消费记录
     */
    List<CustomerAccountLogResult> findLogList(CustomerAccountLog log);

    /**
     * 顾客日志查询
     *
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectCustomerNews
     * @description 查询顾客地址  税票信息  购买金额接口
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/5/13 17:42
     */
    List<CustomerAccountLog> selectCustomerAccountLog(CustomerAccountLog customerAccountLog);
    
    List<String> queryAccountCodeSource(Map<String, List<String>> param);
    
    List<CustomerAccountLogRule> queryReturnCoinInfoByCustomerCode (@Param("customerAccountCode") String customerAccountCode);
    List<Map<String, String>> queryDependentInfoByUseCoinInfo (
    		@Param("customerAccountCodes") List<String> customerAccountCodes, 
    		@Param("occurAmounts") List<BigDecimal> occurAmounts
    		);
    List<String> queryDependentOrderCodesByUseCoinInfo (
    		@Param("customerAccountCodes") List<String> customerAccountCodes, 
    		@Param("occurAmounts") List<BigDecimal> occurAmounts
    		);
    List<String> queryDependentOrderCodes4DeleteOrder(@Param("orderCodes") List<String> orderCodes);
    List<String> queryUseRecordByPromotion(CustomerAccountLog customerAccountLog);
}

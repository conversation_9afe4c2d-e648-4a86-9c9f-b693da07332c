package com.besttop.marketing.mapper.customer;

import com.besttop.marketing.model.customer.CustomerAddress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.customer.param.CustomerAccountParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 顾客收货地址表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-04
 */
@Component
public interface CustomerAddressMapper extends BaseMapper<CustomerAddress> {

    /**
     * @param customerCode
     * @return int
     * @methodName updateDefaultAddressToNon
     * @description 将顾客的默认地址修改为非默认
     * <AUTHOR>
     * @date 2020/2/4 17:20
     */
    int updateDefaultAddressToNon(@Param("customerCode") String customerCode);

    /**
     * @return java.util.List<com.besttop.marketing.model.customer.CustomerAddress>
     * @methodName selectCustomerNews
     * @description 查询客户地址
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/4/9 19:11
     */
    List<CustomerAddress> selectCustomerNews(@Param("customerCode") String customerCode);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName updateCustomerCouponStatus
     * @description 修改促销券状态（status 只接受 转增中，未使用 两种类型的入参）
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/6/8 14:21
     */
    boolean updateCustomerCouponStatus(CustomerAccountParam customerAccount);

}

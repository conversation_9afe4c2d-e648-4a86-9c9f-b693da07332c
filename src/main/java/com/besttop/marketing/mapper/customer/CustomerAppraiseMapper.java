package com.besttop.marketing.mapper.customer;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.customer.CustomerAppraise;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 顾客评价记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Component
public interface CustomerAppraiseMapper extends BaseMapper<CustomerAppraise> {

    List<CustomerAppraise> findCustomerAppraise(CustomerAppraise customerAppraise);
}

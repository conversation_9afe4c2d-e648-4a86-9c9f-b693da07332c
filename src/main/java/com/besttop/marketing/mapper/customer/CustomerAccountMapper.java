package com.besttop.marketing.mapper.customer;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.param.CustomerAccountParam;
import com.besttop.marketing.model.customer.result.*;
import com.besttop.marketing.model.promotion.PromotionPublishGrant;
import com.besttop.marketing.model.promotion.result.PromotionPublishResult;
import com.besttop.marketing.model.scores.ScoresConvert;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 顾客账户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface CustomerAccountMapper extends BaseMapper<CustomerAccount> {

    List<CustomerAccountResult> queryGiftRecord(CustomerAccountParam param);

    @Select("select * from customer_account where type ='erp:customer_account_type:1' and linked_code = #{cardNo} limit 1")
    CustomerAccountResult findCardInfo(@Param("cardNo") String cardNo);

    /**
     * @param codes
     * @return int
     * @methodName updateStatus
     * @description 批量更新状态为已退回
     * <AUTHOR>
     * @date 2020/3/16 16:45
     */
    int updateStatus(@Param("codes") List<String> codes, @Param("updateBy") String updateBy, @Param("status") String status);

    /**
     * 查询顾客促销券账户
     */
    List<CustomerAccountResult> findCoinPromotion(CustomerAccountParam account);

    /**
     * 查询延保
     */
    List<CustomerAccountResult> findAcpp(CustomerAccount account);

    /**
     * 查询订金
     */
    List<CustomerAccountResult> findOrder(CustomerAccount account);

    /**
     * 查询储值卡
     */
    List<CustomerAccountResult> findCard(CustomerAccount account);

    /**
     * 统计各种账户的总计
     */
    List<Map<String, Object>> countByType(CustomerAccountParam param);

    /**
     * 统计积分总计
     */
    List<Map<String, Object>> countByJfType(CustomerAccountParam param);

    /**
     * 统计未使用的券的数量
     */
    List<Map<String, Object>> statisticByType(CustomerAccountParam param);

    /**
     * 查询服务券
     */
    List<CustomerAccountResult> findFwq(CustomerAccount account);

    /**
     * 查询服务券/延保服务
     */
    List<CustomerAccountResult> findYbfw(CustomerAccount account);


    /**
     * 查询服务券/延保服务
     */
    List<CustomerAccountResult> findYbfwOrFwq(CustomerAccount account);

    /**
     * 更新状态和金额
     */
    int updateBatch(List<CustomerAccount> resultList);

    List<CustomerAccountRecoilResult> findCoinsAcppServiceByOrderCodes(List<String> orderCodes);

    /**
     * 查询是否有营销回冲项目
     */
    List<String> selectIsPromotionRecoil(@Param("orderCodes") List<String> orderCodes);

    /**
     * @return java.util.List<com.besttop.marketing.model.customer.result.IntegralResult>
     * @methodName selectIntegralResults
     * @description 有效积分查询
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/5/14 15:49
     */
    List<IntegralResult> selectIntegralResults(@Param("customerCode") String customerCode);

    BigDecimal queryExchangeGiftIntegral(@Param("customerCode") String customerCode);

    BigDecimal queryRecycleGiftIntegral(@Param("customerCode") String customerCode);

    /**
     * @return java.util.List<com.besttop.marketing.model.customer.result.IntegralResult>
     * @methodName selectIntegralResults
     * @description 有效积分查询
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/5/14 15:49
     */
    List<IntegralResult> selectIntegralResultsApp(@Param("customerCode") String customerCode);

    /**
     * @param orderCode
     * @return com.besttop.marketing.model.customer.CustomerAccount
     * @methodName queryScoresByOrderCode
     * @description 根据订单查询积分
     * <AUTHOR>
     * @date 2020/5/13 13:55
     */
    CustomerAccount queryScoresByOrderCode(String orderCode);

    /**
     * @return java.util.List<com.besttop.marketing.model.customer.result.IntegralResult>
     * @methodName selectExpireIntegralResults
     * @description 过期积分
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/5/14 15:49
     */
    List<IntegralResult> selectExpireIntegralResults(@Param("customerCode") String customerCode);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName findByType
     * @description 查询客户购买记录
     * @params [customerAccount]
     * <AUTHOR>
     * @date 2020/5/15 10:03
     */
    List<CustomerTransactionsResult> findCustomerTransactions(@Param("customerCode") String customerCode);

    List<CustomerTransactionsResult> findCustomerTransaction(@Param("customerCode") String customerCode);

    /**
     * @param orderCode
     * @return com.besttop.marketing.model.customer.CustomerAccount
     * @methodName queryCoinAccount
     * @description 查询返币
     * <AUTHOR>
     * @date 2020/5/21 17:42
     */
    List<CustomerAccount> queryCoinAccount(String orderCode);

    List<CustomerAccount> queryCoinAccount1(String orderCode);

    List<CustomerAccount> queryCoinNewAccount(@Param("codes") List<String> codes);


    /**
     * 积分明细
     *
     * @param param
     * @return java.util.List<com.besttop.marketing.model.customer.result.CustomerAccountResult>
     * @methodName findJfmx
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/27 14:13
     */
    List<IntegralResult> findJfmx(CustomerAccountParam param);

    // 查询客户已兑换的电子币数量
    Integer findDzbCount(ScoresConvert param);


    int queryUseQtty(@Param("customerCode") String customerCode, @Param("pulishCode") String pulishCode);

    // 查询客户已兑换的优惠券数量
    Integer findYhqCount(ScoresConvert param);

    Integer findYhqCountWithCoincode(ScoresConvert param);

    String findIsGive(CustomerAccountParam param);

    String findOrderCode(String customerCode);

    //查询已领数量
//    Integer findLimitQuantity(String promotionCode, String customerCode);

    //查询促销卷名称
    String findPromotionName(String promotionCode);

    boolean updateGiveCode(String giveCode);

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryCustomerCoins
     * @description 根据顾客编码和电子币类型查询电子币
     * <AUTHOR>
     * @date 2020/6/8 15:21
     */
    List<CustomerAccountResult> queryCustomerCoins(CustomerAccountParam param);

    /**
     * @methodName findYhqGiveCount
     * @description 查询客户赠送的优惠券数量
     */
    Integer findYhqGiveCount(PromotionPublishResult promotionByCode);


    /**
     * @methodName findTotalUseIntegral
     * @description 查询客户累计使用的积分
     */
    Integer findTotalUseIntegral(String customerCode);


    /**
     * @methodName findTotalUseIntegral
     * @description 查询客户累计使用的积分(未过期)
     */
    Integer findTotalUseIntegralNow(String customerCode);

    List<CustomerAccountParam> findCustomerDzbHistory(CustomerAccountParam customerAccount);

    @Select("select amount from customer_account where type = 'erp:customer_account_type:4' AND is_grant IS NULL and customer_code=#{customerCode}")
    List<BigDecimal> findTotalScores(@Param("customerCode") String customerCode);

    @Select("select * from customer_account where  (type = 'erp:customer_account_type:5' or type = 'erp:customer_account_type:6') and code=#{code} and status ='erp:customer_account_status:1'")
    CustomerAccount findByCode(@Param("code") String code);

    List<String> queryAcppStatus(@Param("orderCodes") List<String> orderCodes);

    boolean updateAcppStatus(@Param("codes") List<String> codes, @Param("user") String user);

    @Select("select * from customer_account where  (type = 'erp:customer_account_type:5' or type = 'erp:customer_account_type:6') and code=#{code}")
    CustomerAccount findYbfwOrFwqByCode(@Param("code") String code);

    List<String> queryGiftCoins(@Param("orderCodes") List<String> orderCodes);

    List<String> queryCoinsStatus(@Param("orderCodes") List<String> orderCodes);
    
    List<String> queryCoinsStatusNew(@Param("orderCodes") List<String> orderCodes);

    List<PaymentParam> queryByOrderCode(@Param("orderCodes") List<String> orderCodes);

    Integer findLimitQuantityPro(@Param("promotionCode") String promotionCode, @Param("customerCode") String customerCode);

    BigDecimal getAmount(String code);

    BigDecimal queryExpireScores(String customerCode);

    BigDecimal queryScores(String customerCode);

    List<CustomerAccount> queryCoupon(@Param("customerCode") String customerCode, @Param("type") String type);

    CustomerAccountResult getByCode(CustomerAccountParam param);

    List<String> queryCoinByOrderCode(List<String> orderCodes);


    List<CustomerAccountResult> findOption(CustomerAccount account);

    /**
     * Description: 获取发放记录下的未使用促销券
     *
     * @param grant:
     * @return: java.util.List<com.besttop.marketing.model.customer.CustomerAccount>
     * @Author: Luxi
     * @Date: 2022/4/20 15:00
     */
    List<CustomerAccount> getPromotionUse(PromotionPublishGrant grant);

    /**
     * Description: 获取回收记录下的已锁定促销券
     *
     * @param grant:
     * @return: java.util.List<com.besttop.marketing.model.customer.CustomerAccount>
     * @Author: Luxi
     * @Date: 2022/4/20 15:00
     */
    List<CustomerAccount> getPromotionLock(PromotionPublishGrant grant);

    List<IntegralResult> selectIntegralResultsOrder(CustomerAccountParam customerCode);

    IntegralAccountResult finInergral(String customerCode);
}

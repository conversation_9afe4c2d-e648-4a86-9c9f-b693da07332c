package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 抖音订单 Mapper 接口
 */
@Mapper
public interface DouyinOrderMapper extends BaseMapper<DouyinOrder> {
    
    /**
     * 根据抖音订单ID查询订单
     * 
     * @param orderId 抖音订单ID
     * @return 抖音订单
     */
    DouyinOrder selectByOrderId(@Param("orderId") String orderId);
    
    /**
     * 根据抖音订单ID查询订单
     * 
     * @param orderId 抖音订单ID
     * @return 抖音订单
     */
    @Select("SELECT * FROM t_douyin_order WHERE order_id = #{orderId} AND del_flag = 0 LIMIT 1")
    DouyinOrder selectByDouyinOrderId(@Param("orderId") String orderId);
    
    /**
     * 根据EDP订单ID查询订单
     * 
     * @param edpOrderId EDP订单ID
     * @return 抖音订单
     */
    DouyinOrder selectByEdpOrderId(@Param("edpOrderId") String edpOrderId);
    
    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param orderStatus 订单状态
     * @return 影响行数
     */
    int updateOrderStatus(@Param("id") String id, @Param("orderStatus") Integer orderStatus);
} 
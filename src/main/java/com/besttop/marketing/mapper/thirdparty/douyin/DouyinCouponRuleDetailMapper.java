package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponRuleDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DouyinCouponRuleDetailMapper extends BaseMapper<DouyinCouponRuleDetail> {
    
    /**
     * 根据规则编码删除明细
     * @param ruleCode 规则编码
     * @return 影响行数
     */
    int deleteByRuleCode(@Param("ruleCode") String ruleCode);

    DouyinCouponRuleDetail selectByCouponCode(String couponCode);
} 
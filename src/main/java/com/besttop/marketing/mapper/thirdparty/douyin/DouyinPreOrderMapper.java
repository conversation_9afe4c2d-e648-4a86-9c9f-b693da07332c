package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinPreOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 抖音预下单Mapper接口
 */
@Mapper
public interface DouyinPreOrderMapper extends BaseMapper<DouyinPreOrder> {
    
    /**
     * 根据预订单ID查询预订单
     */
    @Select("SELECT * FROM t_douyin_pre_order WHERE order_id = #{preOrderId} AND del_flag = 0")
    DouyinPreOrder selectByPreOrderId(@Param("preOrderId") String preOrderId);
}
package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinGiftVerificationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 抖音礼品核销记录Mapper接口
 */
@Mapper
public interface DouyinGiftVerificationRecordMapper extends BaseMapper<DouyinGiftVerificationRecord> {
    
    /**
     * 根据券码查询核销记录
     * @param couponCode 券码
     * @return 核销记录
     */
    @Select("SELECT * FROM t_douyin_gift_verification_record WHERE coupon_code = #{couponCode} LIMIT 1")
    DouyinGiftVerificationRecord selectByCouponCode(@Param("couponCode") String couponCode);

}
package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 抖音券操作日志Mapper接口
 */
public interface DouyinCouponLogMapper extends BaseMapper<DouyinCouponLog> {
    
    /**
     * 根据幂等键查询日志记录
     *
     * @param idempotencyKey 幂等键
     * @return 日志记录
     */
    @Select("SELECT * FROM t_douyin_coupon_log WHERE idempotency_key = #{idempotencyKey} LIMIT 1")
    DouyinCouponLog selectByIdempotencyKey(@Param("idempotencyKey") String idempotencyKey);
    
} 
package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCoupon;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

public interface DouyinCouponMapper extends BaseMapper<DouyinCoupon> {
    

    // 根据券码查询
    DouyinCoupon selectByCouponCode(@Param("couponCode") String couponCode);
    

    /**
     * 根据券状态和核销同步状态查询需要同步核销状态的券
     * 
     * @param couponStatus 券状态
     * @param syncStatus1 同步状态1
     * @param syncStatus2 同步状态2
     * @return 券列表
     */
    List<DouyinCoupon> selectByVerificationSyncStatus(@Param("couponStatus") String couponStatus, 
                                                     @Param("syncStatus1") String syncStatus1,
                                                     @Param("syncStatus2") String syncStatus2);
    
    /**
     * 根据券状态和退款同步状态查询需要同步退款状态的券
     * 
     * @param couponStatus1 券状态1
     * @param couponStatus2 券状态2
     * @param syncStatus1 同步状态1
     * @param syncStatus2 同步状态2
     * @return 券列表
     */
    List<DouyinCoupon> selectByRefundSyncStatus(@Param("couponStatus1") String couponStatus1,
                                              @Param("couponStatus2") String couponStatus2,
                                              @Param("syncStatus1") String syncStatus1,
                                              @Param("syncStatus2") String syncStatus2);

    /**
     * 根据抖音订单ID查询券
     */
    DouyinCoupon findByDouyinOrderId(@Param("douyinOrderId") String douyinOrderId);

    /**
     * 根据抖音订单ID查询券列表
     */
    List<DouyinCoupon> findListByDouyinOrderId(@Param("douyinOrderId") String douyinOrderId);

    /**
     * 根据抖音用户openId和券规则ID统计券数量
     */
    int countByDouyinOpenIdAndRuleId(@Param("douyinOpenId") String douyinOpenId, @Param("ruleId") String ruleId);

    /**
     * 统计用户在某个规则下的券数量(按状态)
     * @param ruleCode 规则编码
     * @param douyinOpenId 抖音用户openId
     * @param statusList 状态列表
     * @return 券数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_douyin_coupon c " +
            "INNER JOIN t_douyin_coupon_rule r ON c.coupon_rule_id = r.id " +
            "WHERE r.code = #{ruleCode} " +
            "AND c.douyin_open_id = #{douyinOpenId} " +
            "AND c.coupon_status IN " +
            "<foreach collection='statusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach>" +
            "</script>")
    Long countUserCoupons(@Param("ruleCode") String ruleCode,
                         @Param("douyinOpenId") String douyinOpenId,
                         @Param("statusList") List<String> statusList);

    /**
     * 根据抖音凭证ID查询券
     * 
     * @param certificateId 抖音凭证ID
     * @return 券实例
     */
    @Select("SELECT * FROM t_douyin_coupon WHERE refund_id = #{orderItemId} LIMIT 1")
    DouyinCoupon selectByRefundId(@Param("orderItemId") String orderItemId);

    /**
     * 根据券码查询券
     *
     * @param code 券码
     * @return 券实例
     */
    @Select("SELECT * FROM t_douyin_coupon WHERE coupon_code = #{code} LIMIT 1")
    DouyinCoupon findByCode(String code);
    
    /**
     * 根据订单ID查询券列表（Pro版专用）
     */
    @Select("SELECT * FROM t_douyin_coupon WHERE douyin_order_id = #{orderId} AND del_flag = 0")
    List<DouyinCoupon> selectByOrderId(@Param("orderId") String orderId);

    List<DouyinCoupon> selectByCouponCodes(List<String> codes);
}
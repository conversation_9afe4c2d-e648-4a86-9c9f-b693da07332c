package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinRefundAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 抖音退款审核Mapper接口
 */
@Mapper
public interface DouyinRefundAuditMapper extends BaseMapper<DouyinRefundAudit> {
    
    /**
     * 根据退款ID查询退款审核记录
     */
    @Select("SELECT * FROM t_douyin_refund_audit WHERE refund_id = #{refundId} AND del_flag = 0")
    DouyinRefundAudit selectByRefundId(@Param("refundId") String refundId);
}
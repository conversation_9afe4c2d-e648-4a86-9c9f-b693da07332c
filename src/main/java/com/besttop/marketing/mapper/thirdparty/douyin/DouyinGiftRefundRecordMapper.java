package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinGiftRefundRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 抖音礼品退款记录Mapper接口
 */
@Mapper
public interface DouyinGiftRefundRecordMapper extends BaseMapper<DouyinGiftRefundRecord> {
    
    /**
     * 根据退款ID查询退款记录
     * @param refundId 退款ID
     * @return 退款记录
     */
    @Select("SELECT * FROM t_douyin_gift_refund_record WHERE refund_id = #{refundId} LIMIT 1")
    DouyinGiftRefundRecord selectByRefundId(@Param("refundId") String refundId);
    
    /**
     * 根据券码查询退款记录
     * @param couponCode 券码
     * @return 退款记录列表
     */
    @Select("SELECT * FROM t_douyin_gift_refund_record WHERE coupon_code = #{couponCode} ORDER BY refund_request_time DESC")
    List<DouyinGiftRefundRecord> selectByCouponCode(@Param("couponCode") String couponCode);
    
    /**
     * 根据券ID查询退款记录
     * @param couponId 券ID
     * @return 退款记录列表
     */
    @Select("SELECT * FROM t_douyin_gift_refund_record WHERE coupon_id = #{couponId} ORDER BY refund_request_time DESC")
    List<DouyinGiftRefundRecord> selectByCouponId(@Param("couponId") String couponId);
    
    /**
     * 根据退款类型查询退款记录
     * @param refundType 退款类型
     * @return 退款记录列表
     */
    @Select("SELECT * FROM t_douyin_gift_refund_record WHERE refund_type = #{refundType} ORDER BY refund_request_time DESC")
    List<DouyinGiftRefundRecord> selectByRefundType(@Param("refundType") String refundType);
}
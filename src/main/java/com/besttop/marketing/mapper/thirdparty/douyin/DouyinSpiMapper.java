package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSpi;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 抖音SPI回调记录Mapper接口
 */
@Mapper
public interface DouyinSpiMapper extends BaseMapper<DouyinSpi> {

    /**
     * 查询指定时间范围内的SPI调用记录
     *
     * @param spiType SPI类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return SPI调用记录列表
     */
    @Select("SELECT * FROM t_douyin_spi WHERE spi_type = #{spiType} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} " +
            "AND del_flag = 0 ORDER BY create_time DESC")
    List<DouyinSpi> findByTimeRange(@Param("spiType") String spiType,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 更新处理状态
     *
     * @param id 记录ID
     * @param processStatus 处理状态
     * @param processResult 处理结果
     * @param errorMessage 错误信息
     * @param processTime 处理耗时
     * @return 更新结果
     */
    @Update("UPDATE t_douyin_spi SET process_status = #{processStatus}, " +
            "process_result = #{processResult}, error_message = #{errorMessage}, " +
            "process_time = #{processTime}, update_time = #{now} " +
            "WHERE id = #{id}")
    int updateProcessStatus(@Param("id") String id,
                          @Param("processStatus") Integer processStatus,
                          @Param("processResult") String processResult,
                          @Param("errorMessage") String errorMessage,
                          @Param("processTime") Long processTime,
                          @Param("now") LocalDateTime now);

    /**
     * 查询业务相关的SPI记录
     *
     * @param businessId 业务ID
     * @param spiType SPI类型
     * @return SPI记录
     */
    @Select("SELECT * FROM t_douyin_spi WHERE business_id = #{businessId} " +
            "AND spi_type = #{spiType} AND del_flag = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    DouyinSpi findByBusinessId(@Param("businessId") String businessId,
                              @Param("spiType") String spiType);

    /**
     * 统计指定时间范围内的SPI调用次数
     *
     * @param spiType SPI类型
     * @param processStatus 处理状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 调用次数
     */
    @Select("SELECT COUNT(*) FROM t_douyin_spi WHERE spi_type = #{spiType} " +
            "AND process_status = #{processStatus} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} " +
            "AND del_flag = 0")
    int countByTimeRange(@Param("spiType") String spiType,
                        @Param("processStatus") Integer processStatus,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);
} 
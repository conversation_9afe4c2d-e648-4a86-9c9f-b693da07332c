package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSpiAnalysisLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 抖音SPI业务分析日志Mapper接口
 */
@Mapper
public interface DouyinSpiAnalysisLogMapper extends BaseMapper<DouyinSpiAnalysisLog> {

    /**
     * 安全地根据ID查询日志记录的请求时间
     * 避免LocalDateTime类型转换问题
     *
     * @param id 日志ID
     * @return 包含请求时间的日志对象
     */
    @Select("SELECT id, request_time FROM t_douyin_spi_analysis_log WHERE id = #{id}")
    DouyinSpiAnalysisLog selectById(@Param("id") String id);

    /**
     * 查询指定时间范围内的接口调用情况
     * 只查询必要的字段，避免LocalDateTime类型问题
     *
     * @param spiType SPI接口类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 接口调用记录列表
     */
    @Select("SELECT id, spi_type, store_code, douyin_order_id, douyin_open_id, " +
            "processing_duration_ms, business_result, business_error_code, business_error_msg " +
            "FROM t_douyin_spi_analysis_log WHERE spi_type = #{spiType} " +
            "AND request_time >= #{startTime} AND request_time <= #{endTime} " +
            "ORDER BY request_time DESC")
    List<DouyinSpiAnalysisLog> findByTimeRange(@Param("spiType") String spiType,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的接口调用结果分布
     *
     * @param spiType SPI接口类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果分布统计
     */
    @Select("SELECT business_result, COUNT(*) as count FROM t_douyin_spi_analysis_log " +
            "WHERE spi_type = #{spiType} AND request_time >= #{startTime} " +
            "AND request_time <= #{endTime} GROUP BY business_result")
    List<Map<String, Object>> countResultDistribution(@Param("spiType") String spiType,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的平均处理耗时
     *
     * @param spiType SPI接口类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均处理耗时(毫秒)
     */
    @Select("SELECT AVG(processing_duration_ms) FROM t_douyin_spi_analysis_log " +
            "WHERE spi_type = #{spiType} AND request_time >= #{startTime} " +
            "AND request_time <= #{endTime}")
    Double calculateAverageProcessingTime(@Param("spiType") String spiType,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的错误分布
     *
     * @param spiType SPI接口类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 错误分布统计
     */
    @Select("SELECT business_error_code, COUNT(*) as count FROM t_douyin_spi_analysis_log " +
            "WHERE spi_type = #{spiType} AND business_result = 'FAIL' " +
            "AND request_time >= #{startTime} AND request_time <= #{endTime} " +
            "GROUP BY business_error_code")
    List<Map<String, Object>> countErrorDistribution(@Param("spiType") String spiType,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);
} 
package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinCouponRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 抖音券规则Mapper接口
 */
@Mapper
public interface DouyinCouponRuleMapper extends BaseMapper<DouyinCouponRule> {
    
    /**
     * 根据抖音商品ID查询券规则
     *
     * @param douyinProductId 抖音商品ID
     * @return 券规则对象
     */
    @Select("SELECT * FROM t_douyin_coupon_rule WHERE douyin_product_id = #{douyinProductId} " +
            "AND del_flag = 0 ORDER BY create_time DESC LIMIT 1")
    DouyinCouponRule findByDouyinProductId(@Param("douyinProductId") String douyinProductId);


    /**
     * 根据券码查询规则
     *
     * @param code 券码
     * @return 券规则对象
     */
    @Select("SELECT * FROM t_douyin_coupon_rule WHERE code = #{code} " +
            "AND del_flag = 0 ORDER BY create_time DESC LIMIT 1")
    DouyinCouponRule findByCode(String code);



    // 更新发行数量
    int updateIssuedCount(@Param("id") String id, @Param("increment") Long increment);
    
    /**
     * 查询全品类规则
     */
    @Select("SELECT r.* FROM t_douyin_coupon_rule r " +
            "INNER JOIN t_douyin_coupon_rule_detail d ON r.code = d.rule_code " +
            "WHERE r.store_code = #{storeCode} " +
            "AND r.groupon_type = #{grouponType} " +
            "AND r.status = #{status} " +
            "AND d.class_code = 'all' " +
            "AND d.brand_code = 'all' " +
            "AND d.sku_code = 'all'")
    List<DouyinCouponRule> selectAllProductRules(@Param("storeCode") String storeCode,
                                                @Param("grouponType") String grouponType,
                                                @Param("status") String status);

    /**
     * 根据抖音商品ID查询规则
     */
    @Select("SELECT * FROM t_douyin_coupon_rule " +
            "WHERE douyin_product_id = #{productId} " +
            "AND store_code = #{storeCode} " +
            "AND groupon_type = #{grouponType} " +
            "AND status = #{status}")
    List<DouyinCouponRule> selectByProductId(@Param("productId") String productId,
                                            @Param("storeCode") String storeCode,
                                            @Param("grouponType") String grouponType,
                                            @Param("status") String status);

    /**
     * 根据EDP商品属性查询规则
     */
    @Select("SELECT DISTINCT r.* FROM t_douyin_coupon_rule r " +
            "INNER JOIN t_douyin_coupon_rule_detail d ON r.code = d.rule_code " +
            "WHERE r.store_code = #{storeCode} " +
            "AND r.groupon_type = #{grouponType} " +
            "AND r.status = #{status} " +
            "AND (d.sku_code = #{skuCode} OR d.brand_code = #{brandCode} OR d.class_code = #{classCode})")
    List<DouyinCouponRule> selectByEdpProduct(@Param("skuCode") String skuCode,
                                             @Param("brandCode") String brandCode,
                                             @Param("classCode") String classCode,
                                             @Param("storeCode") String storeCode,
                                             @Param("grouponType") String grouponType,
                                             @Param("status") String status);
}
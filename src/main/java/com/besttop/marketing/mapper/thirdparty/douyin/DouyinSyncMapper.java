package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSync;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 抖音同步记录Mapper接口
 */
@Mapper
public interface DouyinSyncMapper extends BaseMapper<DouyinSync> {

    /**
     * 查询待同步的记录
     *
     * @param syncType 同步类型
     * @param limit 限制数量
     * @return 待同步记录列表
     */
    @Select("SELECT * FROM t_douyin_sync WHERE sync_status = 0 AND sync_type = #{syncType} " +
            "AND (next_retry_time IS NULL OR next_retry_time <= #{now}) " +
            "AND (retry_count < max_retry_count OR max_retry_count = -1) " +
            "AND del_flag = 0 ORDER BY create_time ASC LIMIT #{limit}")
    List<DouyinSync> findPendingSync(@Param("syncType") Integer syncType, 
                                    @Param("now") LocalDateTime now,
                                    @Param("limit") Integer limit);

    /**
     * 更新同步状态
     *
     * @param id 记录ID
     * @param syncStatus 同步状态
     * @param syncResult 同步结果
     * @param errorMessage 错误信息
     * @param nextRetryTime 下次重试时间
     * @param operator 操作人
     * @return 更新结果
     */
    @Update("UPDATE t_douyin_sync SET sync_status = #{syncStatus}, " +
            "sync_result = #{syncResult}, error_message = #{errorMessage}, " +
            "retry_count = retry_count + 1, next_retry_time = #{nextRetryTime}, " +
            "update_time = #{now}, update_by = #{operator} " +
            "WHERE id = #{id}")
    int updateSyncStatus(@Param("id") String id,
                        @Param("syncStatus") Integer syncStatus,
                        @Param("syncResult") String syncResult,
                        @Param("errorMessage") String errorMessage,
                        @Param("nextRetryTime") LocalDateTime nextRetryTime,
                        @Param("now") LocalDateTime now,
                        @Param("operator") String operator);

    /**
     * 查询同步记录
     *
     * @param businessId 业务ID
     * @param syncType 同步类型
     * @return 同步记录
     */
    @Select("SELECT * FROM t_douyin_sync WHERE business_id = #{businessId} " +
            "AND sync_type = #{syncType} AND del_flag = 0 LIMIT 1")
    DouyinSync findByBusinessId(@Param("businessId") String businessId,
                               @Param("syncType") Integer syncType);
} 
package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinUserMapping;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface DouyinUserMappingMapper extends BaseMapper<DouyinUserMapping> {
    
    // 根据抖音openId查询映射
    DouyinUserMapping selectByDouyinOpenId(@Param("douyinOpenId") String douyinOpenId);
    
    // 根据EDP用户ID查询映射
    DouyinUserMapping selectByEdpUserId(@Param("edpUserId") String edpUserId);

} 
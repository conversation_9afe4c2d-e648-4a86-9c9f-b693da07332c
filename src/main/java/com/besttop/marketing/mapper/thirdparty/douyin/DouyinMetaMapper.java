package com.besttop.marketing.mapper.thirdparty.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinMeta;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 抖音元数据Mapper接口
 */
@Mapper
public interface DouyinMetaMapper extends BaseMapper<DouyinMeta> {

    /**
     * 根据元数据类型和父级编码查询
     *
     * @param metaType 元数据类型
     * @param parentCode 父级编码
     * @return 元数据列表
     */
    @Select("SELECT * FROM t_douyin_meta WHERE meta_type = #{metaType} " +
            "AND parent_code = #{parentCode} AND del_flag = 0 " +
            "AND is_enabled = 1 ORDER BY sort_no ASC")
    List<DouyinMeta> findByTypeAndParent(@Param("metaType") String metaType,
                                        @Param("parentCode") String parentCode);

    /**
     * 根据元数据类型和编码查询
     *
     * @param metaType 元数据类型
     * @param metaCode 元数据编码
     * @return 元数据对象
     */
    @Select("SELECT * FROM t_douyin_meta WHERE meta_type = #{metaType} " +
            "AND meta_code = #{metaCode} AND del_flag = 0 " +
            "AND is_enabled = 1 LIMIT 1")
    DouyinMeta findByTypeAndCode(@Param("metaType") String metaType,
                                @Param("metaCode") String metaCode);

    /**
     * 查询指定类型的所有启用的元数据
     *
     * @param metaType 元数据类型
     * @return 元数据列表
     */
    @Select("SELECT * FROM t_douyin_meta WHERE meta_type = #{metaType} " +
            "AND del_flag = 0 AND is_enabled = 1 ORDER BY sort_no ASC")
    List<DouyinMeta> findAllByType(@Param("metaType") String metaType);
} 
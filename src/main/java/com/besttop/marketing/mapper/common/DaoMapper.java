package com.besttop.marketing.mapper.common;

import com.baomidou.mybatisplus.core.mapper.Mapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.StatementType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: DaoMapper </p >
 * <p>Description: DaoMapper TODO</p >
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy: 提供ERP物理删除
 * 2020-01-20 11:13:27  Create by striver
 *</pre>
 */
@Component
public interface DaoMapper extends Mapper {

    @Delete("delete from ${tableName} where id = #{id}")
    int deleteById(@Param(value = "tableName") String tableName, String id);

    @Delete({"<script> delete from ${tableName} where id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'>#{item}</foreach></script>"})
    int deleteByIds(@Param(value = "tableName") String tableName, List<String> ids);

    @Delete("delete from ${tableName} where code = #{code}")
    int deleteByCode(@Param(value = "tableName") String tableName, String code) ;

    @Delete({"<script> delete from ${tableName} where code in <foreach item='item' index='index' collection='codes' open='(' separator=',' close=')'>#{item}</foreach></script>"})
    int deleteByCodes(@Param(value = "tableName") String tableName, List<String> codes);

    @Delete("delete from ${tableName} where ${column} = #{code}")
    int deleteByColumnCode(@Param(value = "tableName") String tableName,@Param(value = "column")String column, String code);

    @Options(statementType = StatementType.CALLABLE)
    @Select("CALL ${procedureName}")
    void call(String procedureName);

    @Select("SELECT life_poi FROM `bt_erp_base`.`store` WHERE code = #{code}")
    String findLifePoi (String code);

    @Select("SELECT code FROM `bt_erp_base`.`store` WHERE life_poi = #{poiId}")
    String findStoreByLifePoi (String poiId);
}

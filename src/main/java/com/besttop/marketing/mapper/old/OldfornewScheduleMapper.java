package com.besttop.marketing.mapper.old;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.old.OldfornewSchedule;
import com.besttop.marketing.model.old.OldfornewScheduleSkuCoupon;
import com.besttop.marketing.model.old.param.OldfornewScheduleParam;

import java.util.List;

/**
 * <p>
 * 以旧换新推券规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface OldfornewScheduleMapper extends BaseMapper<OldfornewSchedule> {

    List<OldfornewSchedule> find(OldfornewScheduleParam oldfornewScheduleParam);


    List<OldfornewScheduleSkuCoupon> findEffectiveCouponBySku(OldfornewScheduleParam oldfornewScheduleParam);
}

package com.besttop.marketing.mapper.old;

import com.besttop.marketing.model.old.OldfornewSkuDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.old.param.OldfornewSkuDetailParam;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 以旧换新单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface OldfornewSkuDetailMapper extends BaseMapper<OldfornewSkuDetail> {

    List<OldfornewSkuDetail> find(OldfornewSkuDetailParam oldfornewSkuDetailParam);

    OldfornewSkuDetail findOne(OldfornewSkuDetail oldfornewSkuDetail);

    @Update("UPDATE `oldfornew_sku_detail` SET `status` = 'erp:oldfornew_sku_detail_status:4' " +
            "WHERE `status` = 'erp:oldfornew_sku_detail_status:2' AND NOW() > ADDDATE(`audit_time`, INTERVAL #{hour} HOUR);")
    int updateNoConfirm(String hour);
}

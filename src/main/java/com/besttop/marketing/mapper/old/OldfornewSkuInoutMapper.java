package com.besttop.marketing.mapper.old;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.old.OldfornewSkuInout;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 手动礼品发放记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */
@Component
public interface OldfornewSkuInoutMapper extends BaseMapper<OldfornewSkuInout> {

    List<OldfornewSkuInout> findBySourceCode(@Param("sourceCode") String sourceCode, @Param("list") List<String> skuList);
}



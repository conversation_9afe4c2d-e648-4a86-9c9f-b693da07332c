package com.besttop.marketing.mapper.old;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.old.OldfornewOperConfigSku;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 以旧换新sku配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
public interface OldfornewOperConfigSkuMapper extends BaseMapper<OldfornewOperConfigSku> {

    List<OldfornewOperConfigSku> findByApplet(OldfornewOperConfigSku param);

    @Select("SELECT is_enable FROM oldfornew_oper_config_sku LIMIT 1")
    Integer findIsEnable();
}

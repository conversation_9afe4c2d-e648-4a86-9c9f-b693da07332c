package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillPromotionPay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.promotion.PromotionPublish;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 券销售记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@Component
public interface BillPromotionPayMapper extends BaseMapper<BillPromotionPay> {

    int selectPromotionNum(PromotionPublish promotionPublish);
}

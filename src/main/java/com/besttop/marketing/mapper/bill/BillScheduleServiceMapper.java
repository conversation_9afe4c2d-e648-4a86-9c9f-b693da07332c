package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillScheduleService;
import com.besttop.marketing.model.bill.param.BillScheduleParam;
import com.besttop.marketing.model.bill.param.BillScheduleServiceParam;
import com.besttop.marketing.model.bill.result.BillScheduleServiceResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品服务规则单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface BillScheduleServiceMapper extends BaseMapper<BillScheduleService> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.bill.BillScheduleService>
     * @methodName queryService
     * @description 查询服务规则
     * <AUTHOR>
     * @date 2020/3/18 15:49
     */
    List<BillScheduleServiceResult> queryService(BillScheduleServiceParam param);

    List<BillScheduleParam> findCode(BillScheduleParam param);

    /**
     * @param code
     * @return com.besttop.marketing.model.bill.BillScheduleService
     * @methodName queryServiceByCode
     * @description 查询服务分摊金额
     * <AUTHOR>
     * @date 2020/3/24 17:02
     */
    BillScheduleService queryShareAmount(@Param("serviceCodes") List<String> serviceCodes, @Param("code") String code);

    BillScheduleService queryServiceByCode(@Param("code") String code, @Param("skuCode") String skuCode);

}

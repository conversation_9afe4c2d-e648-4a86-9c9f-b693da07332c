package com.besttop.marketing.mapper.bill;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillLottery;
import com.besttop.marketing.model.bill.LotteryNumber;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import org.springframework.stereotype.Component;

/**
 * LotteryNumber的Dao接口
 * 
 * <AUTHOR>
 */
@Component
public interface LotteryNumberMapper extends BaseMapper<LotteryNumber> {

    List<LotteryNumber> find(LotteryNumber lotteryNumber);

    List<LotteryNumber> findCancelChangeByOrderCode(ShoppingOrder shoppingOrder);

    String getMaxCode(String code);

    List<String> queryCode(@Param("orderCodes") List<String> orderCodes);
    
    List<String> querylotteryOrderCodes (@Param("orderCodes") List<String> orderCodes);
    
    List<BillLottery> queryOrderLotteryScheduleInfo(@Param("orderCodes") List<String> orderCodes);
    
    Integer queryCountlotteryByOrderCode(@Param("orderCode") String orderCode);
}
package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillAcpp;
import com.besttop.marketing.model.bill.param.BillAcppParam;
import com.besttop.marketing.model.bill.result.BillAcppResult;

import java.util.List;

/**
 * <p>
 * 延保服务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface BillAcppMapper extends BaseMapper<BillAcpp> {

    int del(BillAcppParam param);

    List<BillAcpp> findBySelected(BillAcppParam param);

    List<BillAcppResult> query(BillAcppResult param);
}

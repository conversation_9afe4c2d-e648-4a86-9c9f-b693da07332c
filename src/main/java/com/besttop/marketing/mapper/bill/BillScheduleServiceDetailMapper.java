package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillScheduleService;
import com.besttop.marketing.model.bill.BillScheduleServiceDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.result.BillScheduleServiceDetailResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品服务规则单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface BillScheduleServiceDetailMapper extends BaseMapper<BillScheduleServiceDetail> {

    /**
     * @param codes
     * @return int
     * @methodName deleteByCodes
     * @description 删除明细
     * <AUTHOR>
     * @date 2020/3/18 17:19
     */
    int deleteByCodes(@Param("codes") List<String> codes);

    /**
     * @param service
     * @return int
     * @methodName deleteByCodes
     * @description 查询明细
     * <AUTHOR>
     * @date 2020/3/18 17:19
     */
    List<BillScheduleServiceDetailResult> findDetail(BillScheduleService service);

}

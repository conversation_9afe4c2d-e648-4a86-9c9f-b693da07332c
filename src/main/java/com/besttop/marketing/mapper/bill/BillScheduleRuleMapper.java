package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillScheduleRule;
import com.besttop.marketing.model.bill.param.BillScheduleParam;
import com.besttop.marketing.model.bill.param.BillScheduleRuleParam;
import com.besttop.marketing.model.bill.result.BillSchedule;
import com.besttop.marketing.model.bill.result.BillScheduleRuleResult;
import com.besttop.marketing.model.coin.CoinPromotionDefineResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 档期规则定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
@Component
public interface BillScheduleRuleMapper extends BaseMapper<BillScheduleRule> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.bill.BillScheduleRule>
     * @methodName queryScheduleRule
     * @description 查询单品满返规则单
     * <AUTHOR>
     * @date 2020/2/12 14:07
     */
    List<BillScheduleRuleResult> queryScheduleRule(BillScheduleRuleParam param);

    /**
     * 查询满足条件的规则单
     */
    List<BillSchedule> find(BillScheduleParam param);

    /**
     * 查询审核时间最大满足条件的满赠规则单
     */
    List<BillSchedule> findCode(BillScheduleParam param);

    /**
     * 查询单据集合的品类，品牌，sku编码集合
     */
    List<BillScheduleParam> findCodeList(BillScheduleParam param);


    List<String> queryIsArbitrageOrder(BillScheduleParam param);

    /**
     * 查询分摊比率
     */
    List<BillScheduleRuleResult> querySingleByCode(BillScheduleRuleParam param);

    /**
     * 查询赠品分摊金额或满免分摊比率
     */
    BillScheduleRuleResult getGiftRateAmount(@Param("code") String code, @Param("giftCode") String giftCode, @Param("giftId") String giftId);

    String findName(String name, String id);

    List<BillScheduleRuleResult> findGuideFreeCoin(BillScheduleRuleParam param);

    List<BillSchedule> findGuideCoin(BillScheduleParam param);
}

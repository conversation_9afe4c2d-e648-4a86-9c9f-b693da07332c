package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillScheduleLimit;
import com.besttop.marketing.model.bill.param.BillScheduleLimitParam;
import com.besttop.marketing.model.bill.param.LimitParam;
import com.besttop.marketing.model.bill.param.ScheduleLimitParam;
import com.besttop.marketing.model.bill.result.BillScheduleLimitResult;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 限量限价销售设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface BillScheduleLimitMapper extends BaseMapper<BillScheduleLimit> {

    int del(BillScheduleLimitParam param);

    List<BillScheduleLimit> findBySelected(ScheduleLimitParam param);

    LimitParam query(LimitParam param);

    BillScheduleLimitResult queryLimitByCode(@Param("code") String code, @Param("skuCode") String skuCode);

    int updateLock(@Param("skuCode") String skuCode, @Param("qtty") int qtty, @Param("code") String code);

    int updateLockAndSale(@Param("skuCode") String skuCode, @Param("qtty") int qtty, @Param("code") String code);

    BigDecimal getUseRate(@Param("code") String code, @Param("skuCode") String skuCode);
}

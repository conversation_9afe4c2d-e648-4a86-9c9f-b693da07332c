package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillGroupDefine;
import com.besttop.marketing.model.bill.param.BillGroupDefineParam;
import com.besttop.marketing.model.bill.result.BillGroupResult;

import java.util.List;

/**
 * <p>
 * 捆绑商品定义单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-24
 */
public interface BillGroupDefineMapper extends BaseMapper<BillGroupDefine> {

    int del(BillGroupDefineParam param);

    List<BillGroupDefine> findBySelected(BillGroupDefineParam param);

    BillGroupResult queryGroups(BillGroupDefineParam param);
}

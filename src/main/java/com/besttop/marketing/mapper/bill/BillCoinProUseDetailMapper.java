package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillCoinProUseDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.param.BillCoinProUseDetailParam;
import com.besttop.marketing.model.bill.result.BillCoinProUseDetailResult;

import java.util.List;

/**
 * <p>
 * 电子币促销券使用规则明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
public interface BillCoinProUseDetailMapper extends BaseMapper<BillCoinProUseDetail> {

    int del(BillCoinProUseDetail detail);

    List<BillCoinProUseDetailResult> findDetailList(BillCoinProUseDetailParam param);

    List<BillCoinProUseDetailResult> findSkuList(BillCoinProUseDetailParam param);
}

package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillScheduleRuleDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.param.BillScheduleParam;
import com.besttop.marketing.model.bill.result.BillSchedule;
import com.besttop.marketing.model.bill.result.BillScheduleRuleDetailResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 档期规则定义单详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
@Component
public interface BillScheduleRuleDetailMapper extends BaseMapper<BillScheduleRuleDetail> {

    /**
     * 通过sku 获取商品单位
     * @param skuCode
     * @return
     */
    String getUnit(String skuCode);

    List<BillScheduleRuleDetailResult> findDetailList(BillScheduleRuleDetail detail);

    /**
     * @methodName deleteByRuleCode
     * @description 根据规则单号删除明细
     * @param codes
     * @return int
     * <AUTHOR>
     * @date 2020/2/13 10:22
     */
    int deleteByRuleCode(@Param("codes") List<String> codes);

    List<BillSchedule> queryGiveSku(BillScheduleParam param);
}

package com.besttop.marketing.mapper.bill;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillLottery;
import com.besttop.marketing.model.bill.param.BillLotteryParam;
import org.springframework.stereotype.Component;

/**
 * BillLottery的Dao接口
 *
 * <AUTHOR>
@Component
public interface BillLotteryMapper extends BaseMapper<BillLottery> {

    int del(BillLotteryParam param);

    List<BillLottery> findBySelected(BillLotteryParam param);

    List<BillLottery> queryRule(@Param("storeCode") String storeCode);
    List<BillLottery> queryRuleNew(@Param("storeCode") String storeCode, @Param("payTime") String payTime);
    
    List<BillLottery> queryRuleByStoreCodes (@Param("storeCodes") Set<String> storeCodes);

    String findAuditCode(BillLotteryParam billLotteryParam);

    String findName(BillLotteryParam billLotteryParam);
    
    Integer queryEeffectRule(BillLotteryParam param);
}
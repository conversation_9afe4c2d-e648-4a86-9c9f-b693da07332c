package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillScheduleServiceRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.result.BillScheduleServiceRuleResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商品服务规则单规则设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Component
public interface BillScheduleServiceRuleMapper extends BaseMapper<BillScheduleServiceRule> {

    /**
     * @param codes
     * @return int
     * @methodName deleteByCodes
     * @description 删除服务
     * <AUTHOR>
     * @date 2020/3/18 17:28
     */
    int deleteByCodes(@Param("codes") List<String> codes);

    /**
     *查询导购促销券的赠服务
     */
    List<BillScheduleServiceRuleResult> query(@Param("code") String code);

    BigDecimal findSkuPrice(String ruleCode,String skuCode);
}

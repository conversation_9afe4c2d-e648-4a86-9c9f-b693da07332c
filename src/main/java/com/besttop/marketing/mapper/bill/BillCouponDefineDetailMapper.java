package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillCouponDefineDetail;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 优惠券定义明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
@Component
public interface BillCouponDefineDetailMapper extends BaseMapper<BillCouponDefineDetail> {

    int del(BillCouponDefineDetail detail);

    List<BillCouponDefineDetail> selectCheckout(BillCouponDefineDetail detail);

    List<BillCouponDefineDetail> queryList(String code);
}

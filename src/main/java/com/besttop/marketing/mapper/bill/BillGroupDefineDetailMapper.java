package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillGroupDefineDetail;

import java.util.List;

/**
 * <p>
 * 捆绑商品定明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-24
 */
public interface BillGroupDefineDetailMapper extends BaseMapper<BillGroupDefineDetail> {
    /**
     *删除
     */
    int del(BillGroupDefineDetail detail);

    List<BillGroupDefineDetail> findDetails(BillGroupDefineDetail billGroupDefineDetail);
}

package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillScheduleRuleGifts;
import com.besttop.marketing.model.bill.result.BillScheduleRuleGiftsResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 档期活动满赠明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-18
 */
@Component
public interface BillScheduleRuleGiftsMapper extends BaseMapper<BillScheduleRuleGifts> {

    List<BillScheduleRuleGiftsResult> findDetailList(BillScheduleRuleGifts gifts);

    /**
     * @methodName deleteByRuleCode
     * @description 通过单号删除赠品
     * @param codes
     * @return int
     * <AUTHOR>
     * @date 2020/2/18 17:19
     */
    int deleteByRuleCode(@Param("codes") List<String> codes);

    List<BillScheduleRuleGifts> queryGiftsList(@Param("code") String code, @Param("standardAmount") BigDecimal standardAmount);


}

package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillCouponDefineBDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * B账户提成设置明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
public interface BillCouponDefineBDetailMapper extends BaseMapper<BillCouponDefineBDetail> {

    int del(BillCouponDefineBDetail detail);

    List<BillCouponDefineBDetail> checkDetail(@Param("skuCodes") List<String> skuCodes,@Param("storeCodes") List<String> storeCodes);

    /**
     *根据编码查询详情
     */
    List<BillCouponDefineBDetail> findDetails(@Param("code") String code);
}

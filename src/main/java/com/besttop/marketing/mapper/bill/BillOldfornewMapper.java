package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillOldfornew;
import com.besttop.marketing.model.bill.param.BillOldfornewParam;
import com.besttop.marketing.model.bill.param.BillScheduleServiceParam;
import com.besttop.marketing.model.bill.result.BillScheduleServiceResult;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface BillOldfornewMapper extends BaseMapper<BillOldfornew> {

    List<BillOldfornew> queryService(BillOldfornewParam param);

    BillOldfornew queryOldfornew(BillOldfornewParam param);

    BillOldfornew queryOldfornewByCode(BillOldfornewParam param);

    BillOldfornew queryOldfornewBySkuCode(BillOldfornewParam param);
}
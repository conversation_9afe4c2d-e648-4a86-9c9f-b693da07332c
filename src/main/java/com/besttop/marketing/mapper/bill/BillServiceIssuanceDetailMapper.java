package com.besttop.marketing.mapper.bill;

import java.util.List;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillServiceIssuanceDetail;
import com.besttop.marketing.model.bill.param.BigMemberVendorCouponParam;
import com.besttop.marketing.model.bill.param.BillServiceIssuanceDetailParam;
import com.besttop.marketing.model.bill.result.BigMemberVendorCouponResult;
import com.besttop.marketing.model.bill.result.BillServiceResult;

@Component
public interface BillServiceIssuanceDetailMapper extends BaseMapper<BillServiceIssuanceDetail> {

    List<BillServiceResult>queryAll(BillServiceIssuanceDetailParam param);
    
    List<BigMemberVendorCouponResult> queryVendorCouponDefine(BigMemberVendorCouponParam param);
    
    List<BigMemberVendorCouponResult> queryCustomerCoupons(BigMemberVendorCouponParam param);

    List<BillServiceIssuanceDetailParam>queryDetail(BillServiceIssuanceDetailParam param);
    
    
}

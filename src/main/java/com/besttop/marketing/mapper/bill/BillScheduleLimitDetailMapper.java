package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillScheduleLimitDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.param.BillScheduleLimitParam;
import com.besttop.marketing.model.bill.param.ScheduleLimitParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 限量限价销售设置明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface BillScheduleLimitDetailMapper extends BaseMapper<BillScheduleLimitDetail> {

    int del(BillScheduleLimitDetail detail);

    List<BillScheduleLimitDetail> findList(BillScheduleLimitParam param);

    /**
     *更新可卖数量
     */
    int updateSaleQtty(@Param("skuCode") String skuCode, @Param("qtty") int qtty, @Param("code") String code);

    /**
     * 根据条件查询单号
     * query by sku/classCodes/brandCodes
     * @param param
     * @return
     */
    List<String> findCodeByCondition(ScheduleLimitParam param);
}

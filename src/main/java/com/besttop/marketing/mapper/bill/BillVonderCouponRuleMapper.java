package com.besttop.marketing.mapper.bill;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillVonderCouponRule;
import com.besttop.marketing.model.bill.BillVonderCouponRuleDetail;
import com.besttop.marketing.model.coupon.param.DMallQueryCouponDefineParam;
import com.besttop.marketing.model.shopping.result.OrderSkuResult;

/**
 * <p>
 * 档期规则定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
@Component
public interface BillVonderCouponRuleMapper extends BaseMapper<BillVonderCouponRule> {
	
	List<BillVonderCouponRule> querySchedule(String code, String name, String effectStore, String status);
	
	List<BillVonderCouponRuleDetail> queryScheduleDetail(String ruleCode);

	List<OrderSkuResult> querySkuClassBrandByOrderCode(DMallQueryCouponDefineParam param);

//	List<BillVonderCouponRule> queryHitRules(Set<String> skuCodes, Set<String> classCodes, Integer classCount ,Set<String> brandCodes,
//			Set<OrderSkuResult> classBrandCodes, String storeCode, BigDecimal amount, 
//			Set<BigDecimal> skuPrices);
	List<BillVonderCouponRule> queryHitRules(Set<String> skuCodes, Set<String> classCodes, Set<String> brandCodes,
			Set<OrderSkuResult> classBrandCodes, String storeCode, int classCount);
//	List<BillVonderCouponRule> queryHitRules1(Set<String> skuCodes, Set<String> classCodes, Integer classCount ,Set<String> brandCodes,
//			Integer flag, String storeCode, BigDecimal amount, 
//			Set<BigDecimal> skuPrices);

	List<BillVonderCouponRule> queryExcludeRules(String storeCode);
}

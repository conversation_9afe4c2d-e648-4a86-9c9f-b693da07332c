package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillCoinProShare;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.param.BillCoinProShareParam;
import com.besttop.marketing.model.bill.result.BillCoinProShareResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 电子币促销券分摊规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@Component
public interface BillCoinProShareMapper extends BaseMapper<BillCoinProShare> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.bill.result.BillCoinProShareResult>
     * @methodName queryShare
     * @description 查询分摊规则单
     * <AUTHOR>
     * @date 2020/2/25 14:37
     */
    List<BillCoinProShareResult> queryShare(BillCoinProShareParam param);

    BillCoinProShareResult querShareRate(@Param("classCode") String classCode, @Param("brandCode") String brandCode, @Param("skuCode") String skuCode, @Param("type") String type,@Param("effectStore")String effectStore);

}

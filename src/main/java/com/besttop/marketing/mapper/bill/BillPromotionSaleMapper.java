package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillPromotionSale;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.param.BillPromotionSaleParam;
import com.besttop.marketing.model.bill.param.BillPromotionSalePayParam;
import com.besttop.marketing.model.bill.result.BillPromotionSaleResult;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 促销券销售表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@Component
public interface BillPromotionSaleMapper extends BaseMapper<BillPromotionSale> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.bill.result.BillPromotionSaleResult>
     * @methodName queryPromotionOrder
     * @description 查询销售订单
     * <AUTHOR>
     * @date 2020/3/12 17:41
     */
    List<BillPromotionSaleResult> queryPromotionOrder(BillPromotionSaleParam param);

    /**
     * @param code
     * @return int
     * @methodName updateStatus
     * @description 修改原销售单的状态
     * <AUTHOR>
     * @date 2020/3/16 16:06
     */
    int updateStatus(@Param("code") String code, @Param("status") String status);

    List<ShoppingPayRecord> queryPromotionSaleRecord(BillPromotionSalePayParam param);

}

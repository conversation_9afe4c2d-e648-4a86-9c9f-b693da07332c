package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillPromotionSaleDetail;
import com.besttop.marketing.model.promotion.result.PromotionPublishResult;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 促销券销售明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@Component
public interface BillPromotionSaleDetailMapper extends BaseMapper<BillPromotionSaleDetail> {

    /**
     * @param detail
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryDetail
     * @description 查询已销售促销券明细
     * <AUTHOR>
     * @date 2020/3/12 16:15
     */
    List<PromotionPublishResult> queryDetail(BillPromotionSaleDetail detail);

}

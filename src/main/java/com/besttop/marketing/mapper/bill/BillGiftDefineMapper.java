package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillGiftDefine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillGiftDefineGifts;
import com.besttop.marketing.model.bill.param.BillGiftDefineParam;
import com.besttop.marketing.model.bill.result.BillGiftDefineGiftsResult;
import com.besttop.marketing.model.bill.result.BillGiftDefineResult;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 礼品规则单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Component
public interface BillGiftDefineMapper extends BaseMapper<BillGiftDefine> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.bill.BillGiftDefine>
     * @methodName queryGift
     * @description 查询礼品规则单
     * <AUTHOR>
     * @date 2020/3/17 17:36
     */
    List<BillGiftDefineResult> queryGift(BillGiftDefineParam param);

    List<BillGiftDefineGiftsResult> queryGifts(BillGiftDefineParam param);

}

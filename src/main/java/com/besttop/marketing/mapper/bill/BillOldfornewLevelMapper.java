package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillOldfornewLevel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

public interface BillOldfornewLevelMapper extends BaseMapper<BillOldfornewLevel> {

    int deleteByCodes(List<String> codes);

    @Select("select * from bill_oldfornew_detail where oldfornew_code = #{code}")
    List<BillOldfornewLevel> queryByCode(@Param("code") String code);


    BillOldfornewLevel selectByCode(@Param("code")String code, @Param("price") BigDecimal price);
}
package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillOldfornewDetail;
import com.besttop.marketing.model.bill.param.BillOldfornewParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BillOldfornewDetailMapper extends BaseMapper<BillOldfornewDetail> {

    int deleteByCodes(List<String> codes);

    @Select("select * from bill_oldfornew_detail where oldfornew_code = #{code}")
    List<BillOldfornewDetail> queryByCode(@Param("code") String code);

    List<BillOldfornewDetail> findDetail(BillOldfornewParam code);
}
package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillProviderFeeInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 供应商费用监控表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-07
 */
@Component
public interface BillProviderFeeInfoMapper extends BaseMapper<BillProviderFeeInfo> {

    @Select("select source_code from bill_provider_fee_info where fee_code = #{code}")
    List<String> findByFeeCode(@Param("code") String code);
}

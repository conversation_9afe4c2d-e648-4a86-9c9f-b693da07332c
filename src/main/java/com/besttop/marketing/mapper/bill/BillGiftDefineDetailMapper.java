package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillGiftDefine;
import com.besttop.marketing.model.bill.BillGiftDefineDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.result.BillGiftDefineDetailResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 礼品规则单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
public interface BillGiftDefineDetailMapper extends BaseMapper<BillGiftDefineDetail> {

    /**
     * @param define
     * @return com.besttop.common.model.ResultEntity
     * @methodName findDetail
     * @description 查询明细
     * <AUTHOR>
     * @date 2020/3/17 18:12
     */
    List<BillGiftDefineDetailResult> findDetail(BillGiftDefine define);

    /**
     * @methodName deleteByDefineCodes
     * @description 删除明细
     * @param defineCodes
     * @return int
     * <AUTHOR>
     * @date 2020/3/18 9:54
     */
    int deleteByDefineCodes(@Param("defineCodes") List<String> defineCodes);

}

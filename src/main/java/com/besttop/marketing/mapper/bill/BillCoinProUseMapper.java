package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillCoinProUse;
import com.besttop.marketing.model.bill.param.BillCoinProUseParam;
import com.besttop.marketing.model.bill.param.CoinProUseParam;
import com.besttop.marketing.model.bill.param.CoinProUseParamNew;
import com.besttop.marketing.model.bill.param.CoinProUseQueryParam;
import com.besttop.marketing.model.bill.result.BillCoinProUseResult;
import com.besttop.marketing.model.bill.result.CoinProUseResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 电子币促销券使用规则定义主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
public interface BillCoinProUseMapper extends BaseMapper<BillCoinProUse> {

    int del(BillCoinProUseParam param);

    List<BillCoinProUseResult> findBySelected(BillCoinProUseParam param);

    List<CoinProUseParamNew> query(CoinProUseParam param);
    List<CoinProUseParam> queryNew(CoinProUseParam param);

    /**
     *查询导购开票中的电子币
     */
    List<CoinProUseParam> queryCoin(CoinProUseParam param);
    
    List<CoinProUseParamNew> queryCoinNew(CoinProUseParam param);

    /**
     *查询规则单
     */
    List<CoinProUseParam> findCoinProUse(CoinProUseParam param);

    /**
     *查询规则单
     */
    List<CoinProUseParam> findCoinUse(CoinProUseParam param);

//    List<CoinProUseParam> findCoinInhose(CoinProUseQueryParam param);

    List<CoinProUseParamNew> queryCustomerInhoseCode(CoinProUseQueryParam param);

    CoinProUseResult queryCustomerCoupons(Map<String, Object> map);
}

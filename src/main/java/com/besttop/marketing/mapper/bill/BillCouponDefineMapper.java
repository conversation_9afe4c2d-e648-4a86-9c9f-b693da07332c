package com.besttop.marketing.mapper.bill;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.BillCouponDefine;
import com.besttop.marketing.model.bill.BillCouponDefineBDetail;
import com.besttop.marketing.model.bill.param.BillCouponDefineParam;
import com.besttop.marketing.model.coupon.param.CouponBalanceParam;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 优惠券定义主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
@Component
public interface BillCouponDefineMapper extends BaseMapper<BillCouponDefine> {

    int del(BillCouponDefineParam param);

    /**
     * 查询导购开票厂家券/商场券
     */
    List<CouponBalanceParam> query(CouponBalanceParam param);

    /**
     * 查询使用比率
     */
    BigDecimal getUseRate(CouponBalanceParam param);

    /**
     * 查询B提成
     */
    BillCouponDefineBDetail queryPushMoney(CouponBalanceParam param);

    Integer checkFeeSet(String code);
    
    Integer querySkuHitBUseDefine (CouponBalanceParam param);
}

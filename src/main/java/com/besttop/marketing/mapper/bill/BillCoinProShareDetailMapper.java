package com.besttop.marketing.mapper.bill;

import com.besttop.marketing.model.bill.BillCoinProShareDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.bill.result.BillCoinProShareDetailResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 电子币促销券分摊规则明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
public interface BillCoinProShareDetailMapper extends BaseMapper<BillCoinProShareDetail> {

    /**
     * @methodName deleteByShareCode
     * @description 删除分摊规则单明细
     * @param shareCodes
     * @return int
     * <AUTHOR>
     * @date 2020/2/25 15:44
     */
    int deleteByShareCode(@Param("shareCodes") List<String> shareCodes);

    /**
     * @methodName deleteByShareCode
     * @description 查询明细
     * @param detail
     * @return int
     * <AUTHOR>
     * @date 2020/2/25 15:44
     */
    List<BillCoinProShareDetailResult> findDetailList(BillCoinProShareDetail detail);

}

package com.besttop.marketing.mapper.sku;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.sku.SkuStockBatch;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 批次记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Component
public interface SkuStockBatchMapper extends BaseMapper<SkuStockBatch> {
    /**
     * @return java.util.List<com.besttop.marketing.model.result.SkuStockBatch>
     * @methodName selectStockBatch
     * @description 根据先进先出原则查询批次
     * @params []
     * <AUTHOR>
     * @date 2020/3/17 13:44
     */
    List<SkuStockBatch> selectStockBatch(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode,
    		@Param("inventoryType") String inventoryType);
    List<SkuStockBatch> selectStockBatchWithNumber(@Param("batchNo") String batchNo,@Param("storeCode") String storeCode,
                                                   @Param("skuCode") String skuCode,
                                                   @Param("inventoryType") String inventoryType);

    /**
     * @return com.besttop.marketing.model.result.SkuStockBatch
     * @methodName selectStockBatchOne
     * @description 查询成本价和税率
     * @params [storeCode, skuCode]
     * <AUTHOR>
     * @date 2020/3/23 16:45
     */
    SkuStockBatch selectStockBatchOne(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode);

    /**
     * @return boolean
     * @methodName updateStockBatchNumber
     * @description 添加批次库存
     * @params [batchNumber, curNumber]
     * <AUTHOR>
     * @date 2020/4/2 15:05
     */
    boolean updateStockBatchNumber(@Param("batchNumber") String batchNumber, @Param("curNumber") BigDecimal curNumber, @Param("skuCode") String skuCode, @Param("storeCode") String storeCode,@Param("inventoryType") String inventoryType);

    SkuStockBatch findByBatchNo(@Param("batchNumber") String batchNumber,@Param("inventoryType") String inventoryType,@Param("storeCode") String storeCode,@Param("skuCode") String skuCode);


    @Select("select * from sku_stock_batch where sku_code = #{skuCode} order by create_time desc limit 1")
    SkuStockBatch findBySkuCode(@Param("skuCode") String skuCode);

    /**
     * 根据商品查询 合同 供应商
     */
    SkuStockBatch findContractSku(@Param("skuCode") String skuCode, @Param("storeCode") String storeCode);

    Integer updateSkuStockBatche(@Param("qtty") BigDecimal skuNum, @Param("id") String id);

    List<SkuStockBatch> findBatch(String storeCode, String skuCode);

    SkuStockBatch findByBatch(String storeCode, String skuCode, String batchNumber);

    String findContractCodeByVirtualProvider(String providerCode, String channelCode, String skuCode, String saleStore);

    List<SkuStockBatch> findJmExitBatch(String storeCode, String skuCode);

    List<SkuStockBatch> findKaBatch(String storeCode, String skuCode);

    SkuStockBatch findByExitBatch(String skuCode, String batchNumber);

    SkuStockBatch findPreExitBatch(String storeCode, String skuCode);

    String findMaxBatchNumber(String batchNumber);

    List<SkuStockBatch> findBatchNumber(String storeCode, String skuCode, Set<String> batchNumbers);

    SkuStockBatch findBatchPriceCost(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode,
                                         @Param("inventoryType") String inventoryType);

    SkuStockBatch findBatchPriceCostByBarcode(@Param("skuCode") String skuCode,
                                     @Param("sourceCode") String sourceCode);
}

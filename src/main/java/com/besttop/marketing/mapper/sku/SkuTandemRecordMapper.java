package com.besttop.marketing.mapper.sku;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.sku.SkuTandemRecord;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 批次记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Component
public interface SkuTandemRecordMapper extends BaseMapper<SkuTandemRecord> {

    List<SkuTandemRecord> selectTandemRecord(SkuTandemRecord param);

	List<SkuTandemRecord> queryTandemRecordByStoreOrderSku(String storeCode, String orderCode, String skuCode,
			String skuType);

	boolean updateBatchNo(String id, String batchNumber);
}

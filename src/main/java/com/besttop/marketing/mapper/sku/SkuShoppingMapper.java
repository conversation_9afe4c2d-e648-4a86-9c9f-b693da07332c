package com.besttop.marketing.mapper.sku;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.BaseClass;
import com.besttop.marketing.model.sku.SkuShopping;
import com.besttop.marketing.model.sku.SkuStockParam;
import com.besttop.marketing.model.sku.param.SkuPriceParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Component
public interface SkuShoppingMapper extends BaseMapper<SkuShopping> {
    /**
     * @return java.util.List<com.besttop.marketing.model.result.SkuShopping>
     * @methodName selectPopularSku
     * @description 查询热门商品专用
     * @params [skuShopping]
     * <AUTHOR>
     * @date 2020/3/11 17:00
     */
    List<SkuShopping> selectPopularSku(SkuShopping skuShopping);

    /**
     * @return java.util.List<com.besttop.marketing.model.result.SkuShopping>
     * @methodName selectPopularSku
     * @description 查询可销售商品
     * @params [skuShopping]
     * <AUTHOR>
     * @date 2020/3/11 17:00
     */
    List<SkuShopping> selectSalableSku(SkuShopping skuShopping);

    /**
     * @return java.util.List<java.lang.String>
     * @methodName selectScheduleLimitSku
     * @description 查询限量限价
     * @params [skuCodes]
     * <AUTHOR>
     * @date 2020/3/25 10:27
     */
    List<String> selectScheduleLimitSku(@Param(value = "skuCodes") List<String> skuCodes, @Param(value = "storeCode") String storeCode);

    /**
     * @param skuCode, storeCode
     * @return java.lang.Integer
     * @methodName queryIsStock
     * @description 查询商品是否管库存
     * <AUTHOR>
     * @date 2020/3/26 15:04
     */
    SkuShopping queryIsStock(@Param("skuCode") String skuCode, @Param("storeCode") String storeCode);

    List<SkuShopping> queryIsStockBySku(@Param("skuCodes") Set<String> skuCodes, @Param("storeCode") String storeCode);

    SkuShopping queryContract(@Param("skuCode") String skuCode, @Param("storeCode") String storeCode);

    int queryIsAdd(String contractCode);

    List<SkuStockParam> findOrderByExitCodes(List<String> exitCodes);

    boolean updateStatusByExitScene(@Param("set")Set<String> codes);

    List<SkuStockParam> findOrderByChangeCodes(List<String> changeCodes);

    SkuShopping findDetail(SkuShopping param);

    String findBusinessType(String sourceCode);

    List<SkuStockParam> findOrderIsChangeByOrderCode(String orderCode);

    String findType(String code);

    List<SkuStockParam> findBind(String code);

    SkuShopping queryIsStockPro(@Param("sourceOrderCode")String sourceOrderCode,@Param("skuCode")String skuCode, @Param("storeCode") String storeCode);

    List<SkuStockParam> findStatusExitCode(String exitCode);

    List<SkuStockParam> findLogisticsStatusExitCode(String exitCode);

    String queryInventoryType(String orderCode);
    List<SkuStockParam> findOrderByChannelExitCodes(List<String> exitCodes);
    /**
     * 查询机构商品零售价格
     * @param param
     * @return
     */
    List<SkuShopping> selectStoreSku(SkuPriceParam param);

    Collection selectSalableSkuMin(SkuShopping skuShopping);

    List<BaseClass> findClassByCodes(Set<String> skuCodes);
}

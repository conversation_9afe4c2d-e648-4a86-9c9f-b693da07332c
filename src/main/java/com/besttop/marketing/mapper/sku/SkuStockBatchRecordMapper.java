package com.besttop.marketing.mapper.sku;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.param.ChannelSaleStockParam;
import com.besttop.marketing.model.scores.param.StorageInoutGiftParam;
import com.besttop.marketing.model.sku.SkuStockBatchRecord;
import com.besttop.marketing.model.sku.result.ShoppingProviderResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 批次记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Component
public interface SkuStockBatchRecordMapper extends BaseMapper<SkuStockBatchRecord> {
    /**
     * @return java.util.List<com.besttop.marketing.model.result.SkuStockBatchRecord>
     * @methodName selectSkuStockBatchRecord
     * @description 退货查询批次日志
     * @params [skuStockParam]
     * <AUTHOR>
     * @date 2020/3/31 15:59
     */
    List<SkuStockBatchRecord> selectSkuStockBatchRecord(@Param("sourceCode") String sourceCode,@Param("skuCode") String skuCode);

    /**
     * @return java.util.List<java.util.Map>
     * @methodName selectShoppingProviderByCode
     * @description 销售后查询销售单的供应商
     * @params [code]
     * <AUTHOR>
     * @date 2020/4/13 14:52
     */
//    List<ShoppingProviderResult> selectShoppingProviderByCode(@Param("codes") List<String> codes);
//    List<ShoppingProviderResult> selectShoppingProviderByCodeNew(@Param("codes") List<String> codes);
    List<ShoppingProviderResult> selectShoppingProviderByCode(@Param("code") String code);
    
    List<ShoppingProviderResult> selectRefundShoppingProviderByCode(@Param("code") String code, @Param("isType9") Integer isType9, 
    		@Param("batchNumbers") List<String> batchNumbers);
    
    List<ShoppingProviderResult> selectShoppingProviderByCodeAndBinding(@Param("code") String code, @Param("bindingSkuCount") Integer bindingSkuCount);
    
    List<ShoppingProviderResult> selectShoppingProviderByCodeAndBindingWithSkuType(@Param("code") String code);
    
    List<ShoppingProviderResult> selectShoppingProviderByCodeAndBindingWithSkuTypePreToSale(@Param("code") String code);
    
    List<ShoppingProviderResult> selectShoppingProviderByCodeBindingSkuTypeBarcode(@Param("code") String code, 
    		@Param("batchNumbers") List<String> batchNumbers);
    @Select("select * from sku_stock_batch_record where batch_no =#{billCode} and sku_code = #{skuCode} and batch_type ='erp:batch_type:16'")
    SkuStockBatchRecord findByBillCode(@Param("billCode") String billCode, @Param("skuCode") String skuCode);
    @Select("select * from sku_stock_batch_record where batch_no =#{billCode} and sku_code = #{skuCode} and inventory_type = #{inventoryType} and batch_type ='erp:batch_type:16'")
    SkuStockBatchRecord findByBillCode(@Param("billCode") String billCode, @Param("skuCode") String skuCode, @Param("inventoryType") String inventoryType);
    /**
     * @return java.util.List<com.besttop.marketing.model.result.SkuStockBatchRecord>
     * @methodName selectSkuStockBatchRecord
     * @description 根据销售单号查询销售批次日志(预售)
     * @params [skuStockParam]
     * <AUTHOR>
     * @date 2020/3/31 11:40
     */
    List<SkuStockBatchRecord> selectSkuStockBatchRecordYS(@Param("sourceCode") String sourceCode,@Param("skuCode") String skuCode);

    List<SkuStockBatchRecord> findSalesBatchRecord(ChannelSaleStockParam channelSaleStockParam);

    List<SkuStockBatchRecord> findSalesBySkuAndBillCode(String billCode, String skuCode, String saleStore);

    String findSourceBillNo(@Param("sourceCode")String sourceCode);

    List<SkuStockBatchRecord> findGiftBatchRecords(StorageInoutGiftParam storageInoutGiftParam);
    
    List<ShoppingProviderResult> findProviderFromSaleRecord(@Param("codes")List<String> codes);
}

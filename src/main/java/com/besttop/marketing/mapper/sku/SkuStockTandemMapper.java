package com.besttop.marketing.mapper.sku;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.sku.SkuStockParam;
import com.besttop.marketing.model.sku.SkuStockTandem;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 批次记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Component
public interface SkuStockTandemMapper extends BaseMapper<SkuStockTandem> {

    @Select("select * from sku_stock_tandem where sku_code =#{skuCode} and return_order_code = #{sourceCode}")
    List<SkuStockTandem> findByOrderCode(SkuStockParam spotPool);

    List<SkuStockTandem> queryTandemList(SkuStockTandem param);

    SkuStockTandem queryTandem(SkuStockTandem param);

    boolean updateBatchNo(String id, String batchNumber, String providerCode, String orderCode);

    boolean lockByIds(List<String> ids);

    boolean updateIsLock(Set<String> orderCodes);

    SkuStockTandem queryTandemDmall(SkuStockTandem query);

//    @Select("select main_uuid from bt_erp_base.dmall_order_edp where edp_code = #{sourceCode} limit 1")
//    String queryMainIdByOrderCode(String sourceCode);
}

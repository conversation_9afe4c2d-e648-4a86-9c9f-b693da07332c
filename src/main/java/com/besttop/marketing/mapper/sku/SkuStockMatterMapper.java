package com.besttop.marketing.mapper.sku;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingThirdDeliverDetail;
import com.besttop.marketing.model.shopping.result.OrderSkuResult;
import com.besttop.marketing.model.sku.SkuStockMatter;
import com.besttop.marketing.model.sku.SkuStockParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 实物库存表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Component
public interface SkuStockMatterMapper extends BaseMapper<SkuStockMatter> {

    /**
     * @return java.lang.String
     * @methodName skuStockReduce
     * @description 现场提货处理实物库存（现场提货使用）
     * @params []
     * <AUTHOR>
     * @date 2020/3/13 9:26
     */
    boolean uptadeSkuMatterReduce(@Param("list")List<SkuStockParam> list,@Param("type")String type);

    SkuStockMatter findBySkuAndStore(@Param("skuCode")String skuCode,@Param("storeCode")String storeCode, @Param("inventoryType")String inventoryType);

    List<SkuStockMatter> findListBySkuAndStroe(@Param("storeCode")String storeCode, @Param("skuCode")List<String> list);

    // 预售转销实物库存操作
    boolean uptadeSkuMatterReduceA(List<SkuStockParam> skuStockParams);

    Integer uptadeSkuMatterReducePro(@Param("skuNum")BigDecimal skuNum, @Param("id")String id,@Param("type")String type);

    Integer updateSceneAndGiftByExit(@Param("qtty") BigDecimal entryQtty, @Param("id")String id);

    BigDecimal queryMatterStock(@Param("skuCode")String skuCode,@Param("storeCode") String storeCode);

    boolean uptadeSkuMatterByThird(@Param("storeCode")String storeCode, @Param("list")List<ShoppingThirdDeliverDetail> list);

    boolean uptadeSkuMatterByThirdCancel(@Param("storeCode")String storeCode, @Param("list")List<ShoppingThirdDeliverDetail> list);

    boolean uptadeSkuMatterByThirdCancelA(@Param("storeCode")String storeCode, @Param("list")List<ShoppingThirdDeliverDetail> list);

    boolean uptadeSkuMatterByThirdA(@Param("storeCode")String storeCode, @Param("list")List<ShoppingThirdDeliverDetail> list);

    BigDecimal queryChannelMatterStock(@Param("skuCode")String skuCode,@Param("storeCode") String storeCode);

    boolean uptadeSkuMatterByChannelCancel(@Param("storeCode")String storeCode, @Param("list")List<OrderSkuResult> list);

    @Update("UPDATE sku_stock_matter SET ${column} = ${column} ${symbol} #{qtty},update_time = #{updateTime} WHERE id = #{id}")
    boolean updateNumberTime(@Param("id") String id, @Param("qtty") BigDecimal qtty, @Param("column") String column,
                             @Param("symbol") String symbol, @Param("updateTime") Date updateTime);

    @Update("UPDATE sku_stock_matter SET ${columnA} = ${columnA} ${symbol} #{qtty},${columnB} = ${columnB} ${symbol} #{qtty},update_time = #{updateTime} WHERE id = #{id}")
    boolean updateNumbersTime(@Param("id") String id, @Param("qtty") BigDecimal qtty, @Param("columnA") String columnA,
                              @Param("columnB") String columnB,
                              @Param("symbol") String symbol, @Param("updateTime") Date updateTime);

    @Update("UPDATE sku_stock_matter SET ${columnA} = ${columnA} ${symbol} #{qtty},${columnB} = ${columnB} ${symbol} #{qtty},${columnC} = ${columnC} ${symbol} #{qtty},update_time = #{updateTime} WHERE id = #{id}")
    boolean updateNumbersProTime(@Param("id") String id, @Param("qtty") BigDecimal qtty, @Param("columnA") String columnA,
                              @Param("columnB") String columnB,
                              @Param("columnC") String columnC,
                              @Param("symbol") String symbol, @Param("updateTime") Date updateTime);

    @Update("UPDATE sku_stock_matter SET ${column} = ${column} ${symbol} #{qtty},update_time = SYSDATE() WHERE id = #{id}")
    boolean updateNumber(@Param("id") String id, @Param("qtty") BigDecimal qtty, @Param("column") String column, @Param("symbol") String symbol);

}

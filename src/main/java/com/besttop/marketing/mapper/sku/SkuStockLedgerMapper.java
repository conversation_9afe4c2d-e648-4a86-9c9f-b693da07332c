package com.besttop.marketing.mapper.sku;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingThirdDeliverDetail;
import com.besttop.marketing.model.sku.SkuStockLedger;
import com.besttop.marketing.model.sku.SkuStockParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 财务库存表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Component
public interface SkuStockLedgerMapper extends BaseMapper<SkuStockLedger> {
    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName addCustomerAddress
     * @description SKU可销售数量查询
     * @params [address]
     * <AUTHOR>
     * @date 2020/2/11 15:26
     */
    SkuStockLedger selectSaleableQuantity(SkuStockLedger skuStockLedger);

    /**
     * @return boolean
     * @methodName addStorageLock
     * @description 添加库存锁
     * @params []
     * <AUTHOR>
     * @date 2020/2/12 11:00
     */
    boolean addStorageLock(SkuStockLedger skuStockLedger);

    /**
     * @return boolean
     * @methodName deleteStorageLock
     * @description 删除库存锁
     * @params []
     * <AUTHOR>
     * @date 2020/2/12 11:00
     */
    boolean deleteStorageLock(SkuStockLedger skuStockLedger);

    /**
     * @return java.lang.String
     * @methodName skuStockReduce
     * @description 减库存减锁定数量（现场提货使用）
     * @params []
     * <AUTHOR>
     * @date 2020/3/13 9:26
     */
    boolean uptadeSkuStockReduce(List<SkuStockParam> list);

    /**
     * @return boolean
     * @methodName updateAdvanceSaleSku
     * @description 预售操作库存
     * @params [list]
     * <AUTHOR>
     * @date 2020/3/20 11:00
     */
    boolean updateAdvanceSaleSku(List<SkuStockParam> list);
    
    boolean updateAdvanceSaleSkuMall(List<SkuStockParam> list);

    /**
     * @return java.lang.String
     * @methodName sceneUpReverseSkuLy
     * @description 现场提货联营管库存商品处理负向库存业务加正品库存
     * @params []
     * <AUTHOR>
     * @date 2020/3/30 15:53
     */
    boolean sceneUpReverseLedgerSkuLy(List<SkuStockParam> skuStockParams);

    /**
     * @return boolean
     * @methodName updateCostAmount
     * @description修改成本金额（未税）和库存量
     * @params [storeCode（机构编码）, skuCode（商品编码）, costAmount（增加的金额）, qtty（增加的数量）]
     * <AUTHOR>
     * @date 2020/4/2 15:38
     */
    boolean updateCostAmount(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode, @Param("costAmount") BigDecimal costAmount, @Param("qtty") BigDecimal qtty,@Param("inventoryType") String inventoryType);

    /**
     * @return void
     * @methodName updatePresaleNumber
     * @description 预售退货修改账务库存
     * @params [storeCode, skuCode, skuNum]
     * <AUTHOR>
     * @date 2020/4/7 18:30
     */
    boolean updatePresaleNumber(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode, @Param("skuNum") BigDecimal skuNum);

    // 预售转销财务库存操作   现场提货
    boolean uptadeSkuStockReduceA(List<SkuStockParam> skuStockParams);


    SkuStockLedger findByStoreAndSku(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode);

    SkuStockLedger findByStoreAndSkuAndType(@Param("storeCode") String storeCode, @Param("skuCode") String skuCode, @Param("inventoryType") String inventoryType);

    List<SkuStockLedger> findListBySkuAndStroe(@Param("storeCode")String storeCode, @Param("skuCode")List<String> list);

    boolean uptadeSkuLedgerByThirdCancel(@Param("storeCode")String storeCode, @Param("list")List<ShoppingThirdDeliverDetail> list);

    boolean uptadeSkuLedgerByThirdCancelA(@Param("storeCode")String storeCode, @Param("list")List<ShoppingThirdDeliverDetail> list);

    SkuStockLedger findBySkuAndStore(@Param("skuCode") String skuCode, @Param("storeCode") String storeCode, @Param("inventoryType") String inventoryType);

    @Update("UPDATE sku_stock_ledger SET cur_number = cur_number ${symbol} #{qtty},cost_amount = cost_amount ${symbol} #{amount},update_time = SYSDATE() WHERE id = #{id}")
    boolean updateCurNumberCostAmount(@Param("id") String id, @Param("qtty") BigDecimal qtty,@Param("amount") BigDecimal amount, @Param("symbol") String symbol);

    @Update("UPDATE sku_stock_ledger SET cur_number = cur_number ${symbol} #{qtty},cost_amount = cost_amount ${symbol} #{amount},update_time = #{updateTime} WHERE id = #{id}")
    boolean updateCurNumberCostAmountTime(@Param("id") String id,
                                          @Param("qtty") BigDecimal qtty,
                                          @Param("amount") BigDecimal amount,
                                          @Param("symbol") String symbol,
                                          @Param("updateTime") Date updateTime);
    @Update("UPDATE sku_stock_ledger SET ${column} = ${column} ${symbol} #{qtty},update_time = SYSDATE() WHERE id = #{id}")
    boolean updateNumber(@Param("id") String id, @Param("qtty") BigDecimal qtty, @Param("column") String column,
                         @Param("symbol") String symbol);

    @Update("UPDATE sku_stock_ledger SET ${column} = ${column} ${symbol} #{qtty},update_time = #{updateTime} WHERE id = #{id}")
    boolean updateNumberTime(@Param("id") String id, @Param("qtty") BigDecimal qtty, @Param("column") String column,
                             @Param("symbol") String symbol,
                             @Param("updateTime") Date updateTime);
}

package com.besttop.marketing.mapper.channel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.ChannelPolicyConfirm;
import com.besttop.marketing.model.channel.param.ChannelPolicyConfirmParam;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 渠道政策确认主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Component
public interface ChannelPolicyConfirmMapper extends BaseMapper<ChannelPolicyConfirm> {
    List<ChannelPolicyConfirm>findList(ChannelPolicyConfirmParam channelPolicyConfirmParam);
}

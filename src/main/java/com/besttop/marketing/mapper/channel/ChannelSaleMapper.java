package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.Store;
import com.besttop.marketing.model.channel.ChannelOrder;
import com.besttop.marketing.model.channel.ChannelSale;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.param.ChannelSaleParam;
import com.besttop.marketing.model.channel.result.ChannelSaleIsStockResult;
import com.besttop.marketing.model.channel.result.ChannelSaleResult;
import com.besttop.marketing.model.pmall.PmallLog;
import com.besttop.marketing.model.sku.SkuShopping;
import com.besttop.marketing.model.sku.SkuTandemRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 渠道开单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Component
public interface ChannelSaleMapper extends BaseMapper<ChannelSale> {

    boolean delByIds(List<String> ids);

    List<ChannelSaleResult> find(ChannelSaleParam channelSaleParam);

    ChannelSaleIsStockResult queryIsStockChannel(@Param("skuCode") String skuCode, @Param("storeCode") String storeCode);

    boolean updateSpecialPriceApply(ChannelSale channelSale);

    List<String> findSkuCodes(List<String> skuCodes);

    @Select("SELECT `code`,`direct_code` FROM `bt_erp_base`.`store` WHERE `business_type` ='erp:store_business_type:1' AND `pmall_code` =#{pmallCode} limit 1")
    Store queryStoreCodeByPmallCode(String pmallCode);

    @Select("SELECT `code` FROM `bt_erp_base`.`store` WHERE `business_type` ='erp:store_business_type:2' AND `pmall_code` =#{pmallCode} limit 1")
    String queryChannelStoreCodeByPmallCode(String pmallCode);

    @Select("SELECT `code` FROM `bt_erp_manufacture`.`base_sku` WHERE `pmall_code` =#{pmallCode} limit 1")
    String querySkuCodeByPmallCode(String pmallCode);

    @Select("SELECT a.`main_id`,b.`log` FROM `pmall_order_main` a LEFT JOIN `pmall_log` b ON a.`main_id`= b.`main_id` WHERE a.`order_code` =#{code} limit 1 ")
    PmallLog queryPmallLog(String code);

    @Select("SELECT a.`sku_code` skuCode,a.`barcode` barcode,b.`pmall_code` pmallCode,c.`channel_code` channelCode,c.`sale_store` saleStore FROM `sku_tandem_record` a LEFT JOIN `base_sku` b ON a.`sku_code` = b.`code` LEFT JOIN `channel_sale` c ON a.`source_code` = c.`code` WHERE a.`source_code` =#{code} ")
    List<SkuTandemRecord> queryPmallBarcode(String code);

    @Select("SELECT `pmall_code` pmallCode FROM `bt_erp_base`.`store` WHERE `business_type` ='erp:store_business_type:2' AND `code` = #{channelCode} limit 1")
    String queryPmallCodeByChannelCode(String channelCode);

    @Select("SELECT `pmall_code` FROM `bt_erp_base`.`store` WHERE `business_type` ='erp:store_business_type:1' AND `code` =#{code} limit 1")
    String queryPmallCodeByStoreCode(String pmallCode);
}

package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.param.ChannelSaleParam;
import com.besttop.marketing.model.channel.param.ChannelSaleParam;
import com.besttop.marketing.model.channel.result.ChannelSaleDetailResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 渠道开单明细单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Component
public interface ChannelSaleDetailMapper extends BaseMapper<ChannelSaleDetail> {

    boolean deleteByIds(List<String> ids);

    boolean deleteBySaleCodes(List<String> orderCode);

    List<ChannelSaleDetailResult> findBySaleCode(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findSkuByChannel(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findSkuByChannelContract(ChannelSaleParam channelSaleParam);

    ChannelPolicyDetailSpecial findSpecial(ChannelSaleParam channelSaleParam);

    ChannelPolicyDetailTime findTime(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetail> findPolicyDetail(ChannelSaleParam channelSaleParam);

    ChannelPolicyDetailRoutine findRoutine(ChannelSaleParam channelSaleParam);

    ChannelPolicyDetailSample findSample(ChannelSaleParam channelSaleParam);

    BigDecimal findSaleQttyBySample(ChannelSaleParam channelSaleParam);

    BigDecimal findSampleQttyBySample(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findOrder(ChannelSaleParam channelSaleParam);

    List<ChannelOrderDetail> findOrderDetail(String orderCode);

    List<ChannelSaleDetailResult> findSpecialPriceApply(ChannelSaleParam channelSaleParam);

    boolean updateSpecialPriceApplyDetail(String applyCode);

    @Select("SELECT Sum(sale_qtty) from channel_sale_detail WHERE sale_code IN(SELECT code from channel_sale WHERE channel_code=#{code} AND `status` !='erp:channel_sale_status:1') AND sku_code=#{skuCode}")
    BigDecimal querySkuNumber(@Param("code") String code, @Param("skuCode") String skuCode);

    List<ChannelSaleDetail> findSaleDetail(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetail> findChannelOrder(ChannelSaleParam channelSaleParam);

    BigDecimal findOldPolicy(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findSkuByChannelControl(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findSku(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findChannelContractSku(ChannelSaleParam channelSaleParam);

    List<ChannelSaleDetailResult> findChannelControlSku(ChannelSaleParam channelSaleParam);
}

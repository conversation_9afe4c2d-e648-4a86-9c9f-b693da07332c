package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.ChannelOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.ChannelSale;
import com.besttop.marketing.model.channel.param.ChannelOrderParam;
import com.besttop.marketing.model.channel.result.ChannelOrderResult;

import java.util.List;

/**
 * <p>
 * 渠道订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
public interface ChannelOrderMapper extends BaseMapper<ChannelOrder> {

    boolean delByIds(List<String> ids);

    List<ChannelOrderResult> find(ChannelOrderParam channelOrderParam);

    List<ChannelOrderResult> findTodo(ChannelOrderParam channelOrderParam);

    ChannelOrder findByCode(String code);

    ChannelOrder findByCOPolicyCode(String policyCode);

    ChannelSale findByCSPolicyCode(String policyCode);
}

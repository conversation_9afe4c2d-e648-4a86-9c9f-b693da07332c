package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.param.ChannelOrderParam;
import com.besttop.marketing.model.channel.result.ChannelContractResult;
import com.besttop.marketing.model.channel.result.ChannelOrderDetailResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 渠道订单明细单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
public interface ChannelOrderDetailMapper extends BaseMapper<ChannelOrderDetail> {

    boolean deleteByIds(List<String> ids);

    boolean deleteByOrderCodes(List<String> orderCode);

    List<ChannelOrderDetailResult> findByOrderCode(ChannelOrderParam channelOrderParam);

    List<ChannelOrderDetailResult> findSkuByChannel(ChannelOrderParam channelOrderParam);

    List<ChannelOrderDetailResult> findSkuByChannelContract(ChannelOrderParam channelOrderParam);

    ChannelPolicyDetailSpecial findSpecial(ChannelOrderParam channelOrderParam);

    ChannelPolicyDetailTime findTime(ChannelOrderParam channelOrderParam);

    List<ChannelOrderDetail> findPolicyDetail(ChannelOrderParam channelOrderParam);

    ChannelPolicyDetailRoutine findRoutine(ChannelOrderParam channelOrderParam);

    ChannelPolicyDetailSample findSample(ChannelOrderParam channelOrderParam);

    BigDecimal findSaleQttyBySample(ChannelOrderParam channelOrderParam);

    BigDecimal findSampleQttyBySample(ChannelOrderParam channelOrderParam);

    List<ChannelOrderDetail> findSaleDetail(ChannelOrderParam channelOrderParam);

    ChannelContractResult findSignStore(@Param("skuCode") String skuCode, @Param("storeCode") String storeCode);

    List<ChannelOrderDetailResult> findSkuByChannelControl(ChannelOrderParam channelOrderParam);
}

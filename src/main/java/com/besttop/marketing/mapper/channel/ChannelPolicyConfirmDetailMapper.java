package com.besttop.marketing.mapper.channel;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.ChannelPolicyConfirmDetail;
import com.besttop.marketing.model.channel.param.ChannelPolicyConfirmDetailParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 渠道政策确认明细表-规模奖励/提货台返/众筹台返/台阶直降 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Component
public interface ChannelPolicyConfirmDetailMapper extends BaseMapper<ChannelPolicyConfirmDetail> {
    @Delete("delete from channel_policy_confirm_detail where policy_confirm_code = #{code,jdbcType=VARCHAR}")
    void deleteByCode(@Param("code") String code);

    List<ChannelPolicyConfirmDetail> find(ChannelPolicyConfirmDetailParam channelPolicyConfirmDetailParam);
}

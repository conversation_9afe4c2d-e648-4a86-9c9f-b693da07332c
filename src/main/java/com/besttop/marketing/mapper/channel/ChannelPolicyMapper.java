package com.besttop.marketing.mapper.channel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.ChannelPolicy;
import com.besttop.marketing.model.channel.param.ChannelPolicyParam;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 渠道政策单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Component
public interface ChannelPolicyMapper extends BaseMapper<ChannelPolicy> {
    List<ChannelPolicy> queryChannelPolicyList(ChannelPolicyParam policyParam);
    List<ChannelPolicyParam> qttyPrice(ChannelPolicyParam channelPolicyParam);
}

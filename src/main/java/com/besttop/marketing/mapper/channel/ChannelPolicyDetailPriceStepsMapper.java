package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.ChannelPolicyDetailPriceSteps;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 渠道政策表-台阶直降 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Component
public interface ChannelPolicyDetailPriceStepsMapper extends BaseMapper<ChannelPolicyDetailPriceSteps> {
    @Delete("delete from channel_policy_detail_price_steps where policy_code = #{code,jdbcType=VARCHAR}")
    void deleteByCode(@Param("code") String code);
}

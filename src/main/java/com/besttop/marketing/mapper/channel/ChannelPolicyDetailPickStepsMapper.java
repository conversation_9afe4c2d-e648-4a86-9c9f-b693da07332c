package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.ChannelPolicyDetailPickSteps;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 渠道政策表-提货台返 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Component
public interface ChannelPolicyDetailPickStepsMapper extends BaseMapper<ChannelPolicyDetailPickSteps> {
    @Delete("delete from channel_policy_detail_pick_steps where policy_code = #{code,jdbcType=VARCHAR}")
    void deleteByCode(@Param("code") String code);
}

package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.ChannelPolicyDetailSpecial;
import com.besttop.marketing.model.channel.ChannelSpecialPriceApplyDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.param.ChannelOrderParam;
import com.besttop.marketing.model.channel.param.ChannelSpecialPriceParam;
import com.besttop.marketing.model.channel.result.ChannelOrderDetailResult;
import com.besttop.marketing.model.channel.result.ChannelSpecialPriceDetailResult;

import java.util.List;

/**
 * <p>
 * 渠道特价申请明细单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
public interface ChannelSpecialPriceApplyDetailMapper extends BaseMapper<ChannelSpecialPriceApplyDetail> {

    boolean deleteByApplyCodes(List<String> applyCode);

    boolean deleteByIds(List<String> ids);

    List<ChannelSpecialPriceDetailResult> findByApplyCode(ChannelSpecialPriceParam channelSpecialPriceParam);
}

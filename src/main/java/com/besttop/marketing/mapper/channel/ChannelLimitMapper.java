package com.besttop.marketing.mapper.channel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.ChannelLimit;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 渠道客户授信额度表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
@Component
public interface ChannelLimitMapper extends BaseMapper<ChannelLimit> {

    ChannelLimit findByStoreChannel(String storeCode, String channelCode);
}

package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.ChannelSpecialPriceApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.channel.param.ChannelOrderParam;
import com.besttop.marketing.model.channel.param.ChannelSpecialPriceParam;
import com.besttop.marketing.model.channel.result.ChannelOrderResult;
import com.besttop.marketing.model.channel.result.ChannelSpecialPriceResult;

import java.util.List;

/**
 * <p>
 * 渠道特价申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-30
 */
public interface ChannelSpecialPriceApplyMapper extends BaseMapper<ChannelSpecialPriceApply> {
    boolean delByIds(List<String> ids);

    List<ChannelSpecialPriceResult> find(ChannelSpecialPriceParam channelSpecialPriceParam);

    List<ChannelSpecialPriceResult> findTodo(ChannelSpecialPriceParam channelSpecialPriceParam);

}

package com.besttop.marketing.mapper.channel;

import com.besttop.marketing.model.channel.ChannelPolicyDetailRoutine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 渠道政策表-常规机政策 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-22
 */
@Component
public interface ChannelPolicyDetailRoutineMapper extends BaseMapper<ChannelPolicyDetailRoutine> {
    @Delete("delete from channel_policy_detail_routine where policy_code = #{code,jdbcType=VARCHAR}")
    void deleteByCode(@Param("code") String code);
}

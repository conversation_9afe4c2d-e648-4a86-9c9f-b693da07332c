package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderAcpp;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import com.besttop.marketing.model.shopping.result.OrdersResult;

import java.util.List;

/**
 * <p>
 * 销售延保服务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
public interface ShoppingOrderAcppMapper extends BaseMapper<ShoppingOrderAcpp> {

    Integer findIsAccpSku(String skuCode);

    Integer findIsBindingAccp(String orderAccpCode);

    List<OrdersResult> findBinding(ShoppingOrder shoppingOrder);

    String findSn(String orderCode, String skuCode);

    String findLastBinding(String orderAcppCode);

    String findLastBindingSku(String orderCode);

}

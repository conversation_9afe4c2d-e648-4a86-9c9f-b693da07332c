package com.besttop.marketing.mapper.shopping;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.besttop.marketing.model.logistics.LogisticsDeliver;
import com.besttop.marketing.model.pushorder.ApplyOrder;
import com.besttop.marketing.model.pushorder.ApplyOrderWare;
import com.besttop.marketing.model.pushorder.DmallOrder;
import com.besttop.marketing.model.pushorder.OfflineCoupon;
import com.besttop.marketing.model.pushorder.OrderSaleRefundRelation;
import com.besttop.marketing.model.pushorder.PayDetail;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.BasicOption;
import com.besttop.marketing.model.customer.param.CustomerOrderAppletParam;
import com.besttop.marketing.model.customer.result.CustomerOrderAppletDetailResult;
import com.besttop.marketing.model.customer.result.CustomerOrderAppletResult;
import com.besttop.marketing.model.shopping.ShoppingCustomerRecord;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderExclusivePromotion;
import com.besttop.marketing.model.shopping.ShoppingOrderPromotion;
import com.besttop.marketing.model.shopping.ShoppingOrderSettlement;
import com.besttop.marketing.model.shopping.ShoppingOrderSku;
import com.besttop.marketing.model.shopping.ShouldReceiptOnRefund;
import com.besttop.marketing.model.shopping.param.OrderExitChangeParam;
import com.besttop.marketing.model.shopping.param.OrderLogisticsParam;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.model.shopping.param.SpecialPriceOrderParam;
import com.besttop.marketing.model.shopping.param.TroubleApply;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import com.besttop.marketing.model.shopping.result.OrdersResult;
import com.besttop.marketing.model.shopping.result.ShoppingOrderExitAppResult;
import com.besttop.marketing.model.shopping.result.ShoppingOrderExitChangeAppResult;
import com.besttop.marketing.model.storage.StorageInoutDetail;

/**
 * <p>
 * 销售订单主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingOrderMapper extends BaseMapper<ShoppingOrder> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.BasicOption>
     * @methodName queryContractProvider
     * @description 查询合同下的供应商
     * <AUTHOR>
     * @date 2020/3/23 17:13
     */
    BasicOption queryContractProvider(OrderQueryParam param);

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.shopping.result.OrdersResult>
     * @methodName queryOrders
     * @description 查询订单
     * <AUTHOR>
     * @date 2020/3/28 16:43
     */
    List<OrdersResult> queryOrders(OrderQueryParam param);

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.shopping.result.PayOrderResult>
     * @methodName queryPayOrder
     * @description 查询顾客待处理数据
     * <AUTHOR>
     * @date 2020/4/2 18:09
     */
    List<OrderAndSkuResult> queryPayOrder(OrderQueryParam param);

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPendingOrder
     * @description 查询待处理订单列表
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    List<OrderAndSkuResult> queryPendingOrder(OrderQueryParam param);

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryReplaces
     * @description 查询代收代付集合
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    List<OrderAndSkuResult> queryReplaces(OrderQueryParam param);

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPendingOrder
     * @description 查询待处理订单列表
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    List<OrderAndSkuResult> queryPendingOrderToApp(OrderQueryParam param);

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPendingOrder
     * @description 查询待处理订单列表
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    List<OrderAndSkuResult> queryProcessedOrder(OrderQueryParam param);

    /**
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryProcessedOrderToApp
     * @description 查询待处理订单列表
     * <AUTHOR>
     * @date 2020/4/7 18:23
     */
    List<OrderAndSkuResult> queryProcessedOrderToApp(OrderQueryParam param);

    /**
     * @param codes, status
     * @return int
     * @methodName updateOrderStatus
     * @description 添加更新订单状态
     * <AUTHOR>
     * @date 2020/4/7 17:20
     */
    int updateOrderStatus(@Param("codes") List<String> codes, @Param("status") String status);

    /**
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingOrder>
     * @methodName selectCustomerNews
     * @description 查询购买信息
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/4/9 19:23
     */
    List<ShoppingOrder> selectCustomerNews(@Param("customerCode") String customerCode);

    /**
     * @return java.math.BigDecimal
     * @methodName selectCustomerNews
     * @description 查询购买金额
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/4/9 19:23
     */
    BigDecimal selectCustomerNewNums(@Param("customerCode") String customerCode);

    /**
     * 根据销售类型统计订单数量
     */
    List<Map<String, Object>> countByType(OrderQueryParam param);

    /**
     * @return com.besttop.marketing.model.customer.result.CustomerNums
     * @methodName selectCustomer
     * @description 查询当天  新增顾客开单数量
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/4/10 17:34
     */
    int selectcustomerSalesDate(@Param("user") String user);

    /**
     * @return com.besttop.marketing.model.customer.result.CustomerNums
     * @methodName selectCustomer
     * @description 查询当周 新增顾客开单数量
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/4/10 17:34
     */
    int selectcustomerSalesWeek(@Param("user") String user);

    /**
     * @return com.besttop.marketing.model.customer.result.CustomerNums
     * @methodName selectCustomer
     * @description 查询当月 新增顾客开单数量
     * @params [customerCode]
     * <AUTHOR>
     * @date 2020/4/10 17:34
     */
    int selectcustomerSalesMeek(@Param("user") String user);

    /**
     * @param orderCode
     * @return com.besttop.marketing.model.shopping.result.OrderAndSkuResult
     * @methodName queryOrderByCode
     * @description 查询订单信息
     * <AUTHOR>
     * @date 2020/4/20 17:49
     */
    OrderAndSkuResult queryOrderByCode(@Param("orderCode") String orderCode);

    /**
     * @param orderCodes
     * @return com.besttop.marketing.model.shopping.result.OrderAndSkuResult
     * @methodName queryOrderByCodes
     * @description 查询订单信息
     * <AUTHOR>
     * @date 2020/4/20 17:49
     */
    List<OrderAndSkuResult> queryOrderByCodes(@Param("orderCodes") List<String> orderCodes);

    List<OrderAndSkuResult> findOrderByCustomerCode(ShoppingOrder param);

    /**
     * @param orderCodes
     * @return java.math.BigDecimal
     * @methodName getDopositAmount
     * @description 获取订金额度
     * <AUTHOR>
     * @date 2020/4/23 18:05
     */
    BigDecimal getDopositAmount(@Param("orderCodes") List<String> orderCodes);

    /**
     * @param orderCodes
     * @return java.math.BigDecimal
     * @methodName getDopositAmount
     * @description 获取原销售单号
     * <AUTHOR>
     * @date 2020/4/23 18:05
     */
    List<String> getSourceCodes(@Param("orderCodes")List<String> orderCodes);


    List<ShoppingCustomerRecord> findCustomerShopping(ShoppingCustomerRecord shoppingCustomerRecord);

    String getOrdersByOrderCode(String orderCode);


    List<ShoppingOrderSku> findBySkuAndStore(@Param("list")List<StorageInoutDetail> list,@Param("applyStoreCode")String applyStoreCode);

    List<ShoppingOrderSku> findByOrderCode(List<ShoppingOrderSku> list);

    Integer updateShoppingType(List<ShoppingOrderSku> list);

    BigDecimal findArrears(String orderCode);

    List<TroubleApply> queryOrderList(OrderQueryParam param);

    List<TroubleApply> queryOldOrderList(OrderQueryParam param);

    List<TroubleApply> moneyCompensation(OrderQueryParam param);

    String findIsRecoil(List<String> orderCodes);

    List<String> findExitCodes(String orderCode);

    ShoppingOrderExitAppResult findOrderExitApp(String orderCode);

    List<ShoppingOrderExitChangeAppResult> findOrderExitChangeApp(OrderExitChangeParam orderExitChangeParam);

    String findType(String sourceCode);

    String findOutType(String sourceCode);


    List<String> checkIsRefund(OrderQueryParam param);

    List<CustomerOrderAppletResult> findOrderApplet(CustomerOrderAppletParam customerOrderAppletParam);

    CustomerOrderAppletDetailResult findOrderDetailApplet(CustomerOrderAppletParam customerOrderAppletParam);

    List<OrderLogisticsParam> selectLogisticsDeliver(OrderLogisticsParam param);

    /**
     * @methodName queryAuditOrder
     * @description 查询待审核，已审核的订单（小程序）
     * @param param
     * @return com.besttop.common.model.ResultEntity
     * <AUTHOR>
     * @date 2020/7/9 9:37
     */
    List<OrderAndSkuResult> queryAuditOrder(OrderQueryParam param);

    List<CustomerOrderAppletResult> findCustomerOrderApplet(CustomerOrderAppletParam customerOrderAppletParam);

    CustomerOrderAppletDetailResult findCustomerOrderDetailApplet(CustomerOrderAppletParam customerOrderAppletParam);

    List<ShoppingOrderPromotion> findCustomerOrderDetailPromotionApplet(CustomerOrderAppletParam customerOrderAppletParam);

    BigDecimal findIntegral(String code);

    List<String> getLinkedCodes(String code);

    List<String> queryDelOrderCodes(long expireTime);
    
    String queryDependentOrderCodes(@Param("orderCodes") List<String> orderCodes);
    
    List<ShoppingOrder> queryOrderByDependentOrderCodes(@Param("orderCodes") List<String> orderCodes, @Param("status") String status);

    List<Map<String,String>> queryAppraiseOrderCodes(long expireTime);

    List<ShoppingOrder> queryLinkedRefundOrder(String orderCode);

    List<String> queryLinkedSize(@Param("orderCodes") List<String> orderCodes);

    boolean updateReject(@Param("orderCodes") List<String> orderCodes);

    Integer updateShoppingSaleType(@Param("code") String code);

    String queryChannleByCode(String code);

    List<ShoppingOrderPromotion> queryGrantCodes(@Param("orderCodes") List<String> orderCodes);
    
    List<String> querySourceOrderCodesByRefundOrderCode (@Param("orderCode") String orderCode);

    boolean updateOrderStatusByChannel(String sourceCode);

    boolean updateOrderStatusComPleteByChannel(String sourceCode);
    
    List<ShoppingOrderExclusivePromotion> queryExclusivePromotionByOrder (@Param("orderCodes") List<String> orderCodes);
    
    List<OrdersResult> querySpecialPriceOrders(SpecialPriceOrderParam param);
    
    Integer countCustomerSuccessSpecialPriceOrders (Map<String, String> checkParam);
    
    List<ShouldReceiptOnRefund> queryShouldReceiptButDidNotOnRefoundSuccess (@Param("sourceCodes") List<String> sourceCodes);
    
    List<String> queryDependentedOrders(@Param("orderCode") String orderCode);

    @Select("SELECT code from shopping_order where code = #{maxCode}")
    String findByCode(String maxCode);

    List<OrderLogisticsParam> selectOldForNewLogisticsDeliver(OrderLogisticsParam param);

    List<ShoppingOrderPromotion> queryPromotion(List<String> orderCodes);

    List<ShoppingOrderSku> queryOrderSkuByOrderCode(@Param("orderCode") String orderCode);

    List<String> findRefundOrder(OrderLogisticsParam param);

    List<String> findRefundOnSiteOrder(OrderLogisticsParam param);

    int findIsStock(@Param("sourceCodes")List<String> sourceCodes);
    
    List<DmallOrder> queryShouldPushOrder(Set<String> codes);

	List<ShoppingOrderSettlement> queryPayNumbersByOrderCodes(Set<String> codes);

	List<PayDetail> queryPayRecordsByPayNumbers(Set<String> payNumbers);
	
	List<ApplyOrder> queryShouldPushRefundOrder(Set<String> codes);
	
	List<ApplyOrderWare> queryShouldPushRefundSku(Set<String> codes);
	
	List<String> queryShouldPushRefundOrderCodes(String payNumber);

	List<OfflineCoupon> queryShouldPushCoupons(Set<String> orderCodes);

	List<PayDetail> queryDepositPayRecordsBySaleOrderCode(String orderCode);

	List<String> querySaleOrderCodesByOrderCodes(List<String> orderCodes);

	List<String> querySalePayNumbersByOrderCodes(List<String> orderCodes);
	
	List<String> queryNonFinshedPayNumbers(Set<String> payNumbers);
	
	List<String> queryShouldPushOrderCodes();
	
	List<String> queryShouldPushPreToSaleOrderCodes(List<String> orderCodes);
	
	List<OrderSaleRefundRelation> queryShouldPushedOrdersSaleAndRefundPaynumber(Set<String> codes);
	
	List<PayDetail> queryPayRecordsByRefundPayNumbers(List<String> payNumbers);
	
	ShoppingOrder findSoureCodeMap(String code);

	Integer countSpecialPriceOrders(List<String> orderCodes);

    Integer updateBatchReturnReason(List<ShoppingOrder> list);

    List<String> queryCustomerPayOrder(OrderQueryParam param);

    List<OrderAndSkuResult> queryReplacesByCustomer(OrderQueryParam param);

    Integer queryPendingOrderTotal(OrderQueryParam param);

    @Update("UPDATE shopping_order SET ${column} = ${param}  WHERE code = #{code}")
    boolean baseUpdateTemplate(@Param("code") String code, @Param("column") String column, @Param("param") String param);

    @Update("UPDATE shopping_order SET ${column} = ${param},is_dmall_pay = 1 WHERE code = #{code}")
    boolean baseUpdateTemplateNum(@Param("code") String code, @Param("column") String column, @Param("param") BigDecimal param);

    List<PayDetail> queryDmallPayRecordsByPayNumbers(Set<String> payNumbers);

    /**
     * 根据订单号查询销售机构
     */
    String selectStoreCodeByOrderCode(String orderCode);

    String getSelfOperatedDiscountSku(String classCode,String brandCode);
    
    ShoppingOrder queryNonsaleReturnCode(OrderQueryParam param);

}

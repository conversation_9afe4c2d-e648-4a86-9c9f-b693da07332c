package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.shopping.ShoppingOrderAcppThird;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 销售延保服务表三方表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
public interface ShoppingOrderAcppThirdMapper extends BaseMapper<ShoppingOrderAcppThird> {

    String findThirdId(String orderAcppCode, String orderCode);
}

package com.besttop.marketing.mapper.shopping;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingSaleReceipt;
import com.besttop.marketing.model.shopping.param.ShoppingSaleReceiptParam;
import com.besttop.marketing.model.shopping.result.ShoppingSaleReceiptResult;

/**
 * <p>
 * 订单厂家送货回执表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
public interface ShoppingSaleReceiptMapper extends BaseMapper<ShoppingSaleReceipt> {
	Integer countSumedBill(Map<String, String> param);

	List<ShoppingSaleReceiptResult> findReceipt(ShoppingSaleReceiptParam shoppingSaleReceiptParam);
}

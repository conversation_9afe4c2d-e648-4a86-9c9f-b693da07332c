package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingOrderSettlement;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.model.shopping.param.ShoppingPayRecordParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单结算表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Component
public interface ShoppingOrderSettlementMapper extends BaseMapper<ShoppingOrderSettlement> {

    /**
     * @param payNumber
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     * @methodName getAmount
     * @description 通过流水获取支付金额，结算金额，尾款金额
     * <AUTHOR>
     * @date 2020/4/3 14:25
     */
    BigDecimal getAmount(@Param("payNumber") String payNumber);

    List<ShoppingOrderSettlement> getPayNumbersByOrderCode(String orderCode);

    /**
     * orderCode
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     * @methodName getPayNumberByRefundCode
     * @description 通过退款单号获取支付流水
     * <AUTHOR>
     * @date 2020/4/3 14:25
     */
    String getPayNumberByRefundCode(String orderCode);

    List<String> getPayNumbersByOrderCodes(List<String> orderCodes);

    List<ShoppingOrderSettlement> findList(ShoppingPayRecordParam param);

    List<String> findCodesByPayNumberOrderCode(String payNumber);

    String findPayNumber(String orderCode);

    /**
     * 根据流水号查询关联订单号
     * @param deposit
     * @return
     */
    List<ShoppingOrderSettlement> getOrdersByPyNumbers(List<String> payNumber);
}

package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.shopping.ShoppingPayThird;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Map;

/**
 * <p>
 * 结算支付第三方日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
public interface ShoppingPayThirdMapper extends BaseMapper<ShoppingPayThird> {

    /**
     * @param data
     * @return int
     * @methodName updatePayThird
     * @description 更新第三方支付记录状态
     * <AUTHOR>
     * @date 2020/3/16 17:47
     */
    int updatePayThird(Map<String, Object> data);

}

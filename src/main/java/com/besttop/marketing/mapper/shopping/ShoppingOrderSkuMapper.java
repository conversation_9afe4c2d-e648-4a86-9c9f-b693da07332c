package com.besttop.marketing.mapper.shopping;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.coupon.VonderCouponRecord;
import com.besttop.marketing.model.coupon.param.DMallQueryCouponDefineParam;
import com.besttop.marketing.model.pushorder.ItemDetail;
import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderSku;
import com.besttop.marketing.model.shopping.param.OrderQueryParam;
import com.besttop.marketing.model.shopping.param.ServiceParam;
import com.besttop.marketing.model.shopping.result.OrderSkuResult;
import com.besttop.marketing.model.sku.result.ShoppingProviderResult;

/**
 * <p>
 * 销售开单SKU表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingOrderSkuMapper extends BaseMapper<ShoppingOrderSku> {

    List<OrderSkuResult> getOrderSku(@Param("orderCode") String orderCode, @Param("len") int len);

    /**
     * 查询待发货/待评价/已发货/已完成订单
     */
    List<OrderSkuResult> findByType(OrderQueryParam param);

    /**
     * 修改sku表的折后实售价
     */
    void updateDiscountPrice(@Param("orderCode") String orderCode, @Param("actualPrice") BigDecimal actualPrice);

    List<ServiceParam> selectChangeOrReturn(ServiceParam param);

    List<ShoppingOrder> queryDeposit(OrderQueryParam param);

    String getRevenueCode(String skuCode);

    @Select("select sku_code skuCode from shopping_order_sku where order_code = #{orderCode} and sku_type= 'erp:shopping_order_sku_sku_type:1' ")
    List<String> findByOrderCode(@Param("orderCode") String orderCode);
    
//    List<ShoppingProviderResult> queryProvidersByOrderCodes(
//    		@Param("orderCodes") List<String> orderCodes,
//    		@Param("storeCode") String storeCode,
//    		@Param("effectDate") Date effectDate,
//    		@Param("expiryDate") Date expiryDate
//    		);
    List<ShoppingProviderResult> queryProvidersByOrderCodes(
    		@Param("orderCode") String orderCode,
    		@Param("storeCode") String storeCode,
    		@Param("effectDate") Date effectDate,
    		@Param("expiryDate") Date expiryDate,
    		@Param("oldfornewCount") Integer oldfornewCount
    		);

    
    List<ShoppingProviderResult> queryProvidersByOrderCodesAndBinding1(
    		@Param("orderCode") String orderCode,
    		@Param("storeCode") String storeCode,
    		@Param("effectDate") Date effectDate,
    		@Param("expiryDate") Date expiryDate,
    		@Param("bindingSkuCount") Integer bindingSkuCount);
    
    List<ShoppingProviderResult> queryProvidersByOrderCodesAndBinding(
    		@Param("orderCode") String orderCode,
    		@Param("storeCode") String storeCode,
    		@Param("effectDate") Date effectDate,
    		@Param("expiryDate") Date expiryDate,
    		@Param("bindingSkuCount") Integer bindingSkuCount,
    		@Param("oldfornewCount") Integer oldfornewCount
    		);
    List<OrderSkuResult> countBindingSkuByOrderCode(@Param("orderCodes") List<String> orderCodes);

	List<ItemDetail> queryItemDetailsByOrderCodes(Set<String> orderCodes);
	
	@Insert("INSERT INTO `bt_erp_ledger`.`vonder_coupon_record`("
			+ "`id`, `pay_number`, `store`, `batch_id`, `batch_name`, "
			+ "`vonder_name`, `qtty`, `create_by`, `create_time`"
			+ ") VALUES ("
			+ "#{vcr.id}, #{vcr.payNumber}, #{vcr.store}, #{vcr.batchId}, #{vcr.batchName}, "
			+ "#{vcr.vonderName}, #{vcr.qtty}, #{vcr.createBy}, #{vcr.createTime}"
			+ ")")
	boolean insertVonderCouponRecord(@Param("vcr") VonderCouponRecord vcr);

	List<OrderSkuResult> querySkuClassBrandByOrderCode(List<String> orderCodes);
	
	
	
	
}

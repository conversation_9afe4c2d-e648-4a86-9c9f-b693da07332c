package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingOrderPromotion;
import com.besttop.marketing.model.shopping.ShoppingProviderShare;
import com.besttop.marketing.model.shopping.param.ProviderShareParam;
import com.besttop.marketing.model.shopping.result.BillProviderFeeDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 供应商促销分摊表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingProviderShareMapper extends BaseMapper<ShoppingProviderShare> {
    /**
     * @return java.util.List<com.besttop.marketing.model.shopping.result.BillProviderFeeDetail>
     * @methodName selectProviderShare
     * @description 查询费用监控单添加的明细
     * @params []
     * <AUTHOR>
     * @date 2020/4/17 15:10
     */
    List<BillProviderFeeDetail> selectProviderShare(ProviderShareParam providerShareParam);

    /**
     * @return java.util.List<com.besttop.marketing.model.shopping.result.BillProviderFeeDetail>
     * @methodName selectProviderShare
     * @description 查询费用监控单添加的明细(固定费用)
     * @params []
     * <AUTHOR>
     * @date 2020/4/17 15:10
     */
    List<BillProviderFeeDetail> selectProviderShareByProvider(ProviderShareParam providerShareParam);

    List<BillProviderFeeDetail> findProviderShareByProvider(ProviderShareParam providerShareParam);
    List<BillProviderFeeDetail> selectcontractByStoreCode(ProviderShareParam providerShareParam);

    List<String> findStore(ProviderShareParam providerShareParam);
    /**
     * @return java.math.BigDecimal
     * @methodName selectShareFee
     * @description 查询应分摊金额
     * @params [contractCodes]
     * <AUTHOR>
     * @date 2020/4/18 9:47
     */
    BigDecimal selectShareFee(@Param("feeSetCode")String feeSetCode,@Param("setContracts")Set<String> setContracts,@Param("setStores")Set<String> setStores,@Param("setClass")Set<String> setClass,@Param("setBrands")Set<String> setBrands, @Param("startDate")Date startDate, @Param("endDate")Date endDate);


    List<ShoppingProviderShare> queryShareByCodes(@Param("codes")List<String> codes);

    List<ShoppingProviderShare> queryShareByCodesNew(@Param("codes")List<String> codes);

    List<ShoppingProviderShare> queryShareByCodes(@Param("codes")Set<String> codes);

    List<ShoppingProviderShare> queryProviderShare(@Param("codes") Set<String> codes);

    List<String> selectUseShareFee(ProviderShareParam providerShareParam);

    BigDecimal findFee(ProviderShareParam providerShareParam);

    BigDecimal findFeePro(ProviderShareParam providerShareParam);
    
    String queryReturnCoinOrderCodesByUseCoinOrderCode(@Param("orderCode") String orderCode);
    
    List<ShoppingProviderShare> queryReturnShareByReturnOrderCodes (@Param("orderCodes") String[] orderCodes);
    Integer queryNegativeExist(ShoppingProviderShare s);
    List<ShoppingProviderShare> queryNeedNegativeGiftsShares(@Param("promotions") List<ShoppingOrderPromotion> promotions);
}

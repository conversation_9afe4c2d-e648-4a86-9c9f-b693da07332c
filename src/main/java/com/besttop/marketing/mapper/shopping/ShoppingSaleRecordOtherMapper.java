package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingSaleRecord;
import com.besttop.marketing.model.shopping.ShoppingSaleRecordOther;
import com.besttop.marketing.model.shopping.param.PayOrderParam;
import com.besttop.marketing.model.shopping.param.ProviderShareParam;
import com.besttop.marketing.model.shopping.param.ShoppingPayRecordParam;
import com.besttop.marketing.model.shopping.result.BillProviderFeeDetail;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 销售记录其他表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Component
public interface ShoppingSaleRecordOtherMapper extends BaseMapper<ShoppingSaleRecordOther> {

}

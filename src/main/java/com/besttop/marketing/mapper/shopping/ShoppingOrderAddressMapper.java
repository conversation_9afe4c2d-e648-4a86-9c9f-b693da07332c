package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.pushorder.Consignee;
import com.besttop.marketing.model.pushorder.DmallOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderAddress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 销售订单送货地址表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingOrderAddressMapper extends BaseMapper<ShoppingOrderAddress> {

    /**
     * @methodName querySendSatus
     * @description  根据订单号查询物流送货单状态
     * @params [orderCode]
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/4/22 17:49
     */
    List<String> querySendSatus(String orderCode);

     /**
     * @methodName updateAddress
     * @description  修改配送地址
     * @params [orderCode]
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/4/22 17:49
     */
    int updateAddress(ShoppingOrderAddress address);

    int updateDeliver(ShoppingOrderAddress address);

    int updateInOut(ShoppingOrderAddress address);

    int updateSendTime(ShoppingOrderAddress address);

    ShoppingOrderAddress findInfoByOrderCode(String orderCode);

    @Select("SELECT order_code from shopping_order_address where order_code = #{maxCode}")
    String findByCode(String maxCode);

	List<Consignee> queryConsigneesByCustomerCodes(Set<String> orderCodes);
}

package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingOrderSku;
import com.besttop.marketing.model.shopping.ShoppingPayRecord;
import com.besttop.marketing.model.shopping.param.ShoppingPayRecordParam;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 结算支付记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@Component
public interface ShoppingPayRecordMapper extends BaseMapper<ShoppingPayRecord> {

    /**
     * @param data
     * @return boolean
     * @methodName updatePayRecord
     * @description 更新原支付记录的状态
     * <AUTHOR>
     * @date 2020/3/6 11:25
     */
    boolean updatePayRecord(Map<String, Object> data);

    boolean updateRecord(Map<String, Object> data);

    /**
     * @param orderCode
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingPayRecord>
     * @methodName queryPayRecordByOrder
     * @description 根据订单号查询支付流水
     * <AUTHOR>
     * @date 2020/4/17 17:00
     */
    List<ShoppingPayRecord> queryPayRecordByOrder(String orderCode);

    /**
     * @param orderCode
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingPayRecord>
     * @methodName queryPayRecordByOrder
     * @description 根据订单号查询支付流水
     * <AUTHOR>
     * @date 2020/4/17 17:00
     */
    List<ShoppingPayRecord> queryPayRecordByOrderInvoice(String orderCode);

    /**
     * @param orderCode
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingPayRecord>
     * @methodName queryPayRecordByCardSaleOrder
     * @description 根据卡销售订单号查询支付流水
     * <AUTHOR>
     * @date 2020/4/17 17:00
     */
    List<ShoppingPayRecord> queryPayRecordByCardSaleOrder(String orderCode);

    /**
     * @param payNumber
     * @return int
     * @methodName getMaxSort
     * @description 获取最大序号
     * <AUTHOR>
     * @date 2020/4/18 15:44
     */
    Integer getMaxSort(@Param("payNumber") String payNumber);


    /**
     * @param payNumber
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingPayRecord>
     * @methodName queryPayRecordByOrder
     * @description 根据支付流水号查询支付流水
     * <AUTHOR>
     * @date 2020/4/17 17:00
     */
    List<ShoppingPayRecord> queryPayRecordByPayNumber(String payNumber);
    List<ShoppingPayRecord> queryPayRecordByPayNumberXB(String payNumber,List<String>sorts,String xbNote);
    /**
     * @param orderCode
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryPayRecordByCode
     * @description 根据订单号集合查询支付记录
     * <AUTHOR>
     * @date 2020/4/24 14:12
     */
    List<ShoppingPayRecord> queryPayRecordByCode(@Param("orderCode") String orderCode, @Param("type") String type);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryRefundPayRecordByCode
     * @description 退款查询支付记录
     * @params [param]
     * <AUTHOR>
     * @date 2020/5/3 17:13
     */
    List<ShoppingPayRecord> queryRefundPayRecordByCode(ShoppingOrderSku sku);

    List<ShoppingPayRecord> queryRefundPayRecord(ShoppingOrderSku sku);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryRefundPayRecordByCode
     * @description 退款查询支付记录
     * @params [param]
     * <AUTHOR>
     * @date 2020/5/3 17:13
     */
    List<ShoppingPayRecord> queryRefundRecordByCode(ShoppingPayRecord shoppingPayRecord);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName queryRefundPayRecordByCode
     * @description 退款查询支付记录
     * @params [param]
     * <AUTHOR>
     * @date 2020/5/3 17:13
     */
    List<ShoppingPayRecord> queryRefundRecord(String code);

    /**
     * 查询不同支付类型的支付金额
     */
    List<ShoppingPayRecord> queryShoppingPayRecords(ShoppingPayRecordParam param);

    /**
     * 通过退款单号查询支付流水
     */
    String queryPayNumber(String orderCode);


    /**
     * 通过已退款的流水
     */
    List<ShoppingPayRecord> queryRefundRecordByCardSaleOrder(String orderCode);

    /**
     * @param payNumber
     * @return java.math.BigDecimal
     * @methodName queryRefundAmount
     * @description 查询已退金额
     * <AUTHOR>
     * @date 2020/5/28 15:07
     */
    BigDecimal queryRefundAmount(String payNumber);

    /**
     * @param code
     * @return java.math.BigDecimal
     * @methodName queryArreasAmount
     * @description 查询欠款金额
     * <AUTHOR>
     * @date 2020/6/29 18:44
     */
    BigDecimal queryArreasAmount(String code);

    /**
     * @param orderCode
     * @return java.util.List<com.besttop.marketing.common.ValueCardRefund>
     * @methodName queryValueCardsRecord
     * @description 查询未原路返回选择的储值卡退款记录
     * <AUTHOR>
     * @date 2020/7/10 10:47
     */
    List<ShoppingPayRecord> queryValueCardsRecord(String orderCode);

    /**
     * @param orderCodes
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingPayRecord>
     * @methodName queryDepositRecords
     * @description 查询订金支付记录
     * <AUTHOR>
     * @date 2020/7/11 10:10
     */
    List<ShoppingPayRecord> queryDepositRecords(@Param("orderCodes") List<String> orderCodes);

    List<ShoppingPayRecord> queryRecordsDeposit(@Param("orderCodes") List<String> orderCodes);

    int updateDepositPayRecord(@Param("orderCodes") List<String> sourceCodes);

    int updateArreasPayRecord(String payNumber);

    BigDecimal queryArreaseRefundAmount(String payNumber);

    BigDecimal queryDepositRefundAmount(@Param("orderCodes") List<String> orderCodes);

    ShoppingPayRecord getPayRecordThird(ShoppingPayRecord record);

    List<ShoppingPayRecord> queryArrearsRecord(ShoppingPayRecord record);

    List<ShoppingPayRecord> queryArrears(ShoppingPayRecord record);

    BigDecimal queryByCode(String code);

    /**
     * 根据原单号查询订金支付记录
     * @param linkCodes
     * @return
     */
    List<ShoppingPayRecord> queryDepositRecordsBySourceCodes(List<String> orderCodes);
    
    List<ShoppingPayRecord> queryLastPayRecordsByPayNumbers (Set<String> payNumbers);
    
    List<Date> queryPayTimeByOrder(@Param("orderCode") String orderCode);
    List<String> queryPaidOrNotByOrders(@Param("orderCodes") List<String> orderCodes);

    /**
     * 获取支付凭证
     *
     * @param orderCode
     * @return
     */
    ShoppingPayRecord getVoucherByPayOrderCode(@Param("orderCode") String orderCode);

    List<ShoppingPayRecord> selectExclusionPayType(Set<String> payNumbers);

    List<ShoppingPayRecord> getDmallPayRecord(String payNumber);

    Set<String> getSaleSourceCodes(@Param("orderCodes") List<String> sourceCodes);

    Set<String> getPayNumbers(@Param("orderCodes") Set<String> sourceCodes);

    int updatePayDepositRecord(@Param("orderCodes") Set<String> sourceCodes);
}

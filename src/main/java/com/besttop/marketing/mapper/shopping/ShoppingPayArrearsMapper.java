package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingPayArrears;
import com.besttop.marketing.model.thirdparty.param.RefundmentParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <p>
 * 结算支付欠款表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Component
public interface ShoppingPayArrearsMapper extends BaseMapper<ShoppingPayArrears> {

    /**
     * @param orderCode, returnBy
     * @return int
     * @methodName updateArrears
     * @description 更新欠款信息
     * <AUTHOR>
     * @date 2020/5/5 15:35
     */
    int updateArrears(@Param("orderCode") String orderCode, @Param("returnBy") String returnBy,
                      @Param("payNumber") String payNumber, @Param("isFinish") Integer isFinish, @Param("returnAmount") BigDecimal returnAmount);

    /**
     * @methodName updateArreas
     * @description 修改欠款记录为已退款
     * @param param
     * @return void
     * <AUTHOR>
     * @date 2020/7/3 16:48
     */
    int updateArrearsFinish(@Param("payNumber") String payNumber,@Param("returnBy") String returnBy);


    /**
     * @methodName queryByCode
     * @description 根据订单号查询欠款信息
     * @param orderCode
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/6/24 11:40
     */
    ShoppingPayArrears queryByCode(String orderCode);


}

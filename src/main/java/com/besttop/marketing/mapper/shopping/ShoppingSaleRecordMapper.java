package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.shopping.ShoppingSaleRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.param.PayOrderParam;
import com.besttop.marketing.model.shopping.param.ProviderShareParam;
import com.besttop.marketing.model.shopping.param.ShoppingPayRecordParam;
import com.besttop.marketing.model.shopping.result.BillProviderFeeDetail;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 销售记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingSaleRecordMapper extends BaseMapper<ShoppingSaleRecord> {

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.shopping.result.OrderAndSkuResult>
     * @methodName queryOrders
     * @description 查询销售记录信息
     * <AUTHOR>
     * @date 2020/4/20 15:08
     */
    List<OrderAndSkuResult> querySaleOrders(PayOrderParam param);

    BigDecimal getDepositAmount(@Param("codes") List<String> codes);

    String selectBusinessType(@Param("code") String code);

    List<ShoppingSaleRecord> queryShoppingSaleRecords(ShoppingPayRecordParam param);

    List<OrderAndSkuResult> queryReplace(@Param("orderCodes") List<String> orderCodes);

    List<OrderAndSkuResult> queryRecoil(@Param("orderCodes") List<String> orderCodes);

    List<BillProviderFeeDetail> selectProviderShare(ProviderShareParam param);

    List<String> queryExistNegativeIds(@Param("ids") List<String> ids);
    
    List<ShoppingSaleRecord> queryByOrderCodes(List<String> orderCodes);
}

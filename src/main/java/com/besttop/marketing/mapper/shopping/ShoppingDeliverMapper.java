package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.shopping.ShoppingDeliver;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 临时送货单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Component
public interface ShoppingDeliverMapper extends BaseMapper<ShoppingDeliver> {

    boolean updateStatus(@Param("sourceCode") String sourceCode);

}

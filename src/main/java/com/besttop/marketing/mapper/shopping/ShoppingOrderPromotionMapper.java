package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.customer.CustomerAccount;
import com.besttop.marketing.model.customer.result.CustomerAccountRecoilResult;
import com.besttop.marketing.model.old.OldfornewSkuDetail;
import com.besttop.marketing.model.pushorder.PromotionDetail;
import com.besttop.marketing.model.shopping.ShoppingOrderPromotion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.result.PromotionResult;
import com.besttop.marketing.model.shopping.result.ShoppingOrderPromotionResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 销售订单促销活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingOrderPromotionMapper extends BaseMapper<ShoppingOrderPromotion> {

    /**
     * @param orderCode
     * @return java.util.List<com.besttop.marketing.model.shopping.result.PromotionResult>
     * @methodName getPromotion
     * @description 根据订单号查询活动
     * <AUTHOR>
     * @date 2020/4/3 16:26
     */
    List<PromotionResult> getPromotion(@Param("orderCode") String orderCode);


    /**
     * @param orderCodes
     * @return java.util.List<com.besttop.marketing.model.shopping.result.PromotionResult>
     * @methodName getPromotions
     * @description 根据订单号集合查询活动
     * <AUTHOR>
     * @date 2020/4/3 16:26
     */
//    List<PromotionResult> getPromotions(@Param("orderCodes") List<String> orderCodes,@Param("flag") Integer flag);
    
    List<PromotionResult> getAllPromotions(@Param("orderCodes") List<String> orderCodes);

    /**
     * @methodName getArbitrage
     * @description 获取套购活动
     * @param orderCodes
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingOrderPromotion>
     * <AUTHOR>
     * @date 2020/5/3 15:57
     */
    List<ShoppingOrderPromotion> getArbitrage(@Param("orderCodes") List<String> orderCodes);

    /**
     * @return com.besttop.common.model.ResultEntity
     * @methodName selectIsPromotionRecoil
     * @description 查询是否需要营销回冲
     * @params [shoppingOrderPromotion]
     * <AUTHOR>
     * @date 2020/5/4 15:22
     */
    List<String> selectIsPromotionRecoil(@Param("orderCodes") List<String> orderCodes,@Param("flag") Integer flag);

    List<String> selectGiftsRecoil(@Param("orderCodes") List<String> orderCodes);

    List<ShoppingOrderPromotionResult> finGiftRecoil(@Param("orderCodes") List<String> orderCodes);

    String findGiftExitStatus(@Param("orderCodes") List<String> orderCodes);

    /**
     * @methodName recoilGifts
     * @description 回冲赠品
     * @param orderCodes
     * @return java.util.List<com.besttop.marketing.model.shopping.ShoppingOrderPromotion>
     * <AUTHOR>
     * @date 2020/5/29 9:28
     */
    int recoilGifts(@Param("orderCodes") Set<String> orderCodes);

    List<CustomerAccount> queryRecoilCoins(@Param("orderCodes") Set<String> orderCodes);

    List<CustomerAccountRecoilResult> findGiftCoinsRecoil(@Param("orderCodes") List<String> orderCodes);

    Integer querySingleCoins(String orderCode);

    Integer queryArbitrageCoins(String orderCode);

    @Select("select type,sku_code skuCode from shopping_order_promotion where order_code = #{orderCode} and type='erp:shopping_order_promotion_type:21' limit 1")
    ShoppingOrderPromotion findByOrderCode(@Param("orderCode") String orderCode);

    @Select("select sum(actual_amount) from shopping_order_promotion where order_code = #{orderCode} and type='erp:shopping_order_promotion_type:21'")
    BigDecimal  getOldAmount(@Param("orderCode") String orderCode);

    List<PromotionResult> findOldfornewPromotion(String code);
    
    List<String> queryNonNegativeGiftsPromotions (@Param("orderCode") String orderCode);

	List<PromotionDetail> queryShouldPushPromotion(Set<String> orderCodes);

	OldfornewSkuDetail findOldfornewByCode(@Param("orderCode") String orderCode);
    
	String queryTypesByOrderCodes(List<String> orderCodes);
}

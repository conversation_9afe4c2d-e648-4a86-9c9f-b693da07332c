package com.besttop.marketing.mapper.shopping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingDeliverDetail;
import com.besttop.marketing.model.shopping.ShoppingThirdDeliverDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 临时送货单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Component
public interface ShoppingThirdDeliverDetailMapper extends BaseMapper<ShoppingThirdDeliverDetail> {

    @Delete("delete from ${tableName} where shopping_third_deliver_code = #{code}")
    int deleteByCode(@Param(value = "tableName") String tableName, String code) ;
}

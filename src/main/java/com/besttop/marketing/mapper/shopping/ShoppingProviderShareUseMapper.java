package com.besttop.marketing.mapper.shopping;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.ShoppingProviderShareUse;

@Component
public interface ShoppingProviderShareUseMapper extends BaseMapper<ShoppingProviderShareUse> {
	List<ShoppingProviderShareUse> queryShareByOrderCodes(@Param("orderCode") String orderCode);
	
	Integer queryShareByOrderCodesCount(@Param("orderCode") String orderCode);
}
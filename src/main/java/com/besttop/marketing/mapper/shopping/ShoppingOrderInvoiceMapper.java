package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.shopping.ShoppingOrder;
import com.besttop.marketing.model.shopping.ShoppingOrderInvoice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.shopping.result.OrderAndSkuResult;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 订单税票信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Component
public interface ShoppingOrderInvoiceMapper extends BaseMapper<ShoppingOrderInvoice> {

    Integer getMaxSort(String orderCode);

    ShoppingOrderInvoice getInvoice(String code);

    List<ShoppingOrderInvoice> queryInvoiceRecord(ShoppingOrderInvoice invoice);

    ShoppingOrderInvoice queryOne(String code);

    List<OrderAndSkuResult> queryCustomerInvoice(ShoppingOrder order);

    String queryLogis(String orderCode);

    String queryInout(String orderCode);

    List<ShoppingOrderInvoice> queryInvoiceInfo();

}

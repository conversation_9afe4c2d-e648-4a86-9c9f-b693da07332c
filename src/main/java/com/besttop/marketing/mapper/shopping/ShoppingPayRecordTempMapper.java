package com.besttop.marketing.mapper.shopping;

import com.besttop.marketing.model.shopping.ShoppingPayRecordTemp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 小贝收银退款临时表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
public interface ShoppingPayRecordTempMapper extends BaseMapper<ShoppingPayRecordTemp> {

    List<ShoppingPayRecordTemp> queryTempRefundRecords(String orderCode);

}

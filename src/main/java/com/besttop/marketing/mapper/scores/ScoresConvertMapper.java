package com.besttop.marketing.mapper.scores;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.scores.ScoresConvert;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 营销管理-积分兑换规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Repository
public interface ScoresConvertMapper extends BaseMapper<ScoresConvert> {

    int del(ScoresConvert scoresConvert);

    List<ScoresConvert> findBySelected(ScoresConvert scoresConvert);

    /**
     * 查询客户积分超值兑换
     *
     * @param [param]
     * @return java.util.List<com.besttop.marketing.model.scores.ScoresConvert>
     * @methodName findJfczd
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/28 15:38
     */
    List<ScoresConvert> findJfczd();


    /**
     * 查询积分兑换电子币
     *
     * @param [code]
     * @return com.besttop.marketing.model.scores.ScoresConvert
     * @methodName findJfByCode
     * @description TODO
     * <AUTHOR>
     * @date 2020/5/29 9:29
     */
    ScoresConvert findJfByCode(String code);

    /**
     * @return java.util.List<java.lang.String>
     * @methodName selectCheckScoresConvert
     * @description 查询已有的电子币兑换规则
     * @params [param]
     * <AUTHOR>
     * @date 2020/7/27 15:36
     */
    List<String> selectCheckScoresConvert(ScoresConvert param);
}

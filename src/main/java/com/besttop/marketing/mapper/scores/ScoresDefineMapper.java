package com.besttop.marketing.mapper.scores;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.scores.ScoresDefine;
import com.besttop.marketing.model.scores.param.ScoresDefineParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 营销管理-积分生成规则定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ScoresDefineMapper extends BaseMapper<ScoresDefine> {

    int del(ScoresDefineParam param);

    List<ScoresDefine> findBySelected(ScoresDefineParam param);

    List<Map<String,String>> queryBrandClass(@Param("skuCodes") List<String> skuCodes, @Param("len") int len);

    List<ScoresDefine> queryScores(ScoresDefineParam param);

}


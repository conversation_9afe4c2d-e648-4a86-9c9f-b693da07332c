package com.besttop.marketing.mapper.scores;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.scores.BaseSkuInfo;
import com.besttop.marketing.model.scores.ScoresConvertGift;
import com.besttop.marketing.model.scores.param.ScoresConvertGiftParam;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 积分兑换礼品规则(ScoresConvertGift)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-08-18 15:58:24
 */
@Component
public interface ScoresConvertGiftMapper extends BaseMapper<ScoresConvertGift> {

    List<ScoresConvertGift> findBySelected(ScoresConvertGiftParam param);

    List<String> findRepetition(ScoresConvertGiftParam param);

    List<ScoresConvertGift> queryRule();

    Integer queryYetConvertCount(ScoresConvertGiftParam param);

    List<BaseSkuInfo> queryContractBasesku(BaseSkuInfo info);
}


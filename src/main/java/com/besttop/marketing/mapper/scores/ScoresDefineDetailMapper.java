package com.besttop.marketing.mapper.scores;

import com.besttop.marketing.model.scores.ScoresDefineDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 营销管理-积分生成规则定义明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
public interface ScoresDefineDetailMapper extends BaseMapper<ScoresDefineDetail> {

    int delByDefineCode(@Param("defineCode") String defineCode);
}

package com.besttop.marketing.mapper.promotion;

import com.besttop.marketing.model.bill.param.BigMemberVendorCouponParam;
import com.besttop.marketing.model.bill.result.BigMemberVendorCouponResult;
import com.besttop.marketing.model.promotion.PromotionPublish;
import com.besttop.marketing.model.promotion.PromotionPublishGrant;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.promotion.param.PromotionPublishGrantParam;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 促销券发放详情管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-03
 */
@Component
public interface PromotionPublishGrantMapper extends BaseMapper<PromotionPublishGrant> {

    List<PromotionPublishGrantParam> selectGrantInfo(PromotionPublishGrantParam promotionPublish);
    List<PromotionPublishGrantParam> selectGrantInfoNew(PromotionPublishGrantParam promotionPublish);

    Integer getGrantSuccess(PromotionPublish promotion);

    Integer getRecycleSuccess(PromotionPublish promotion);
    
    List<BigMemberVendorCouponResult> queryCustomerCoupons(BigMemberVendorCouponParam param);
}

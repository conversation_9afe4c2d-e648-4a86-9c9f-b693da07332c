package com.besttop.marketing.mapper.promotion;

import com.besttop.marketing.model.bill.BillPromotionSaleDetail;
import com.besttop.marketing.model.bill.param.BigMemberVendorCouponParam;
import com.besttop.marketing.model.bill.result.BigMemberVendorCouponResult;
import com.besttop.marketing.model.customer.param.CustomerAccountParam;
import com.besttop.marketing.model.promotion.PromotionPublish;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.promotion.param.PromotionPublishParam;
import com.besttop.marketing.model.promotion.result.PromotionPublishResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 促销券投放规则定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-26
 */
@Repository
public interface PromotionPublishMapper extends BaseMapper<PromotionPublish> {

    int del(PromotionPublishParam param);

    List<PromotionPublishResult> findBySelected(PromotionPublishParam param);

    /**
     * @param param
     * @return java.util.List<com.besttop.marketing.model.promotion.result.PromotionPublishResult>
     * @methodName queryPromotion
     * @description 查询促销券--促销券销售
     * <AUTHOR>
     * @date 2020/3/2 17:06
     */
    List<PromotionPublishResult> queryPromotion(PromotionPublishParam param);

    /**
     * 根据编号查询促销券
     * @methodName findPromotionByCode
     * @description TODO
     * @param [param]
     * @return java.util.List<com.besttop.marketing.model.promotion.result.PromotionPublishResult>
     * <AUTHOR>
     * @date 2020/6/2 17:30
     */
    PromotionPublishResult findPromotionByCode(@Param("code") String code, @Param("userType")String userType);

    /**
     * @param detail
     * @return boolean
     * @methodName updatePublishQuantity
     * @description 更新投放规则的已投数量
     * <AUTHOR>
     * @date 2020/3/3 15:55
     */
    int updatePublishQuantity(BillPromotionSaleDetail detail);

    PromotionPublish findPublishOne(String promotionCode);

    /**
     * 根据顾客账号表code查找优惠券投放规则
     * @methodName findByCustomerAccountCode
     * @description TODO
     * @param [code]
     * @return com.besttop.marketing.model.promotion.result.PromotionPublishResult
     * <AUTHOR>
     * @date 2020/6/18 16:03
     */
    PromotionPublishResult findByCustomerAccountCode(String code);

    /**
     * 查询可领取的优惠券
     * @methodName findCoupon
     * @description TODO
     * @param [code]
     * @return com.besttop.marketing.model.promotion.result.PromotionPublishResult
     * <AUTHOR>
     * @date 2020/6/18 16:03
     */
    List<PromotionPublishResult> findCoupon(CustomerAccountParam param);
    
    
    List<BigMemberVendorCouponResult> queryVendorCouponDefine(BigMemberVendorCouponParam param);

    PromotionPublishResult findOldfornewPromotionCode(@Param("promotionCode") String promotionCode);
}

package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardSale;
import com.besttop.marketing.model.card.CardSaleDetail;
import lombok.experimental.PackagePrivate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 卡销售记录明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Repository
public interface CardSaleDetailMapper extends BaseMapper<CardSaleDetail> {

    List<CardSaleDetail> findByCode(@Param("code") String code);

    CardSaleDetail findByCardNo(CardSaleDetail entity);
}

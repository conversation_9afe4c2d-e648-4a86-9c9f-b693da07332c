package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardDefine;
import com.besttop.marketing.model.card.CardDetail;
import com.besttop.marketing.model.card.CardDistributeDetail;
import com.besttop.marketing.model.card.CardLog;
import com.besttop.marketing.model.thirdparty.param.PaymentMallParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 卡详细信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Repository
public interface CardDetailMapper extends BaseMapper<CardDetail> {

    List<CardDetail> findOption(CardDetail param);

    List<CardDetail> findAll(CardDetail param);

    int startOrStop(CardDetail param);

    @Update("update card_detail set magnetic_stripe = #{magneticStripe},create_time =now(),create_by=#{createBy} where card_no = #{cardNo}")
    int madeCard(CardDetail param);

    @Select("select card_no,type_code,type_name,create_time,create_by,status,is_enabled,expire_time,IFNULL(amount,0)amount,note,customer_code from card_detail where del_flag =0 and magnetic_stripe = #{magneticStripe}")
    CardDetail readCard(@Param("magneticStripe") String magneticStripe);

    int updateStoreCode(@Param("updateBy") String updateBy, @Param("useStore") String useStore, @Param("detailList") List<CardDistributeDetail> detailList);

    CardDetail findCard(CardDetail param);

    int updateEvaluateFlag(@Param("updateBy") String updateBy, @Param("list") List<CardLog> list);

    int stopEvaluateFlag(@Param("updateBy") String updateBy, @Param("cardNo") String cardNo);

    List<String> findCards(List<String> list);

    List<String> findMadeIn(CardDefine param);

    @Select("select * from card_detail where del_flag =0 and card_no = #{cardNo}")
    CardDetail findByCardNo(@Param("cardNo") String cardNo);

    boolean cardTopup(List<CardDetail> detailList);

    List<CardDetail> findNotUseCard(CardDetail param);

    @Select("select * from card_detail where del_flag =0 and card_no = #{cardNo}")
    CardDetail findCustomerByCardNo(@Param("cardNo")String cardNo);

    @Select("select * from card_detail where encrypt_key is null or encrypt_key=''")
    List<CardDetail> queryInit();

    @Select("select amount from card_detail where del_flag =0 and card_no = #{cardNo}")
    BigDecimal findCardAmount(@Param("cardNo")String linkedCode);

    CardDetail queryCard(PaymentMallParam param);

    CardDetail queryCardRelation(CardDetail entity);
}

package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardDistribute;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 卡领用单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Repository
public interface CardDistributeMapper extends BaseMapper<CardDistribute> {

    List<CardDistribute> findAll(CardDistribute param);

    CardDistribute findDetail(CardDistribute param);
}

package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardTypeDefine;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 卡类型定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-02
 */
@Repository
public interface CardTypeDefineMapper extends BaseMapper<CardTypeDefine> {

    List<CardTypeDefine> findAll(CardTypeDefine param);

    List<CardTypeDefine> findByName(String name);

    List<CardTypeDefine> findOption(CardTypeDefine param);

    @Update("update card_type_define set is_set=1 where code = #{typeCode}")
    int updateByCode(@Param("typeCode") String typeCode);
}

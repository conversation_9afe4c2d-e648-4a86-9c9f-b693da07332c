package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 卡操作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface CardLogMapper extends BaseMapper<CardLog> {

    @Select("select card_no,operation_type from card_log where operation_type ='erp:card_log_operation_type:12' and card_no = #{cardNo} limit 1")
    CardLog findByCardNo(@Param("cardNo") String cardNo);
}

package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardDefine;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 卡制作 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-02
 */
@Repository
public interface CardDefineMapper extends BaseMapper<CardDefine> {
    List<CardDefine> findAll(CardDefine param);

    @Select("select * from card_define where begin_card_no = #{beginCardNo} AND end_card_no =#{endCardNo} AND del_flag =0")
    CardDefine findByBeginAndEndCard(@Param("beginCardNo") String beginCardNo, @Param("endCardNo") String endCardNo);

    List<String> findMadeIn(CardDefine param);
}

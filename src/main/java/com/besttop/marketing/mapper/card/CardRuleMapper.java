package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardRule;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 储值规则定义 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Repository
public interface CardRuleMapper extends BaseMapper<CardRule> {

    List<CardRule> findAll(CardRule param);

    List<CardRule> findByName(String name);

    CardRule findByCard(String cardNo);
}

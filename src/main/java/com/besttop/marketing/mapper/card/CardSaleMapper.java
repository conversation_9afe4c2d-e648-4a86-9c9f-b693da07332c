package com.besttop.marketing.mapper.card;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.card.CardSale;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 卡销售 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Repository
public interface CardSaleMapper extends BaseMapper<CardSale> {

    List<CardSale> findAll(CardSale param);

    @Update("update card_sale set type =1 where code =(select card_sale_code from card_sale_detail where card_no =#{cardNo})")
    int back(@Param("cardNo") String cardNo);

    @Select("select * from card_sale where code = #{code}")
    CardSale findByCode(@Param("code") String code);

    @Update("update card_sale set status ='erp:card_sale_status:2',pay_amount=0 where code =#{code}")
    int updateByCode(@Param("code") String code);

    @Update("update card_sale set type =1, status =#{status} where code =#{code}")
    int updateStatus(@Param("code")String code,@Param("status")String status);
}
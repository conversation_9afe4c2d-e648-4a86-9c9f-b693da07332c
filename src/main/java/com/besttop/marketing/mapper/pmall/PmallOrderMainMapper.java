package com.besttop.marketing.mapper.pmall;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.pmall.PmallOrderMain;
import com.besttop.marketing.model.pmall.PmallOrderMainQuery;
import com.besttop.marketing.model.pmall.PmallOrderMainResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * pmall&EDP订单关联关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Mapper
public interface PmallOrderMainMapper extends BaseMapper<PmallOrderMain> {

    List<PmallOrderMainResult> queryList(PmallOrderMainQuery param);
}

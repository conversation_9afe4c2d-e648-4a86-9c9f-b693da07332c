package com.besttop.marketing.mapper.schedule;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.schedule.BillScheduleAward;
import org.apache.ibatis.annotations.Param;

/**
 * 顾客激烈配置表(BillScheduleAward)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-12-21 13:57:37
 */
public interface BillScheduleAwardMapper extends BaseMapper<BillScheduleAward> {
    /**
     * Description:
     *
     * <AUTHOR>
     * @date: 2020-12-21 14:23
     * @param:id
     * @return:int
     */
    int deleteById(@Param("id") String id);
}
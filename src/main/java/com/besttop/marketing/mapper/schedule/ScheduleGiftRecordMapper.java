package com.besttop.marketing.mapper.schedule;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.schedule.ScheduleGiftRecord;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 档期赠品/礼品发放记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Component
public interface ScheduleGiftRecordMapper extends BaseMapper<ScheduleGiftRecord> {
	List<String> queryGrantGiftsOrderCodes(@Param("orderCode") String orderCode);
}

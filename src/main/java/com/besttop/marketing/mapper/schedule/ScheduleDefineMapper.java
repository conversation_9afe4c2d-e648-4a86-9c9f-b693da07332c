package com.besttop.marketing.mapper.schedule;

import com.besttop.marketing.model.BasicOption;
import com.besttop.marketing.model.schedule.ScheduleDefine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 档期活动定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
public interface ScheduleDefineMapper extends BaseMapper<ScheduleDefine> {

    int del(ScheduleDefine define);

    List<BasicOption> findCodeByProvide(ScheduleDefine define);

    List<String> findCode(@Param("skuTypes") List<String> skuTypes, @Param("gift")int gift);
}

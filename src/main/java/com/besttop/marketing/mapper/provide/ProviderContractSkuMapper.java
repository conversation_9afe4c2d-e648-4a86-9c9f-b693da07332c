package com.besttop.marketing.mapper.provide;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.model.provide.ProviderContractSku;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <p>
 * 供应商合同商品信息 mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-14
 */
@Component
public interface ProviderContractSkuMapper extends BaseMapper<ProviderContractSku> {

    @Select("select rebate_normal rebateNormal,price_in priceIn from provider_contract_sku where contract_code = #{contractCode} and sku_code = #{skuCode}")
    ProviderContractSku findRabateNormal(@Param("contractCode") String contractCode,@Param("skuCode") String skuCode);

    @Select("select IFNULL(price_wholesale,0) from provider_contract_sku where contract_code = #{contractCode} and sku_code = #{skuCode}")
    BigDecimal findPrice(@Param("contractCode") String contractCode,@Param("skuCode") String skuCode);

    ProviderContractSku findContractSkuByCode(@Param("contractCode") String contractCode, @Param("skuCode") String skuCode, @Param("storeCode") String storeCode);
}

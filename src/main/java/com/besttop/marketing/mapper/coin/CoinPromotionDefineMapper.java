package com.besttop.marketing.mapper.coin;

import com.besttop.marketing.model.BasicOption;
import com.besttop.marketing.model.coin.CoinPromotionDefine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 电子币促销券名称定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@Component
public interface CoinPromotionDefineMapper extends BaseMapper<CoinPromotionDefine> {

    List<BasicOption> findOption(CoinPromotionDefine define);

    CoinPromotionDefine findName(String code);
}

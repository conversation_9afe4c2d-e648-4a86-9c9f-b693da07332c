package com.besttop.marketing.mapper.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.entity.douyin.DouyinCertificateCancel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 抖音撤销核销记录 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Mapper
public interface DouyinCertificateCancelMapper extends BaseMapper<DouyinCertificateCancel> {

    /**
     * 根据撤销幂等标识查询记录
     * 
     * @param cancelToken 撤销幂等标识
     * @return 撤销记录
     */
    @Select("SELECT * FROM t_douyin_certificate_cancel WHERE cancel_token = #{cancelToken} LIMIT 1")
    DouyinCertificateCancel selectByCancelToken(@Param("cancelToken") String cancelToken);

    /**
     * 根据原验券ID查询记录
     * 
     * @param verifyId 原验券ID
     * @return 撤销记录列表
     */
    @Select("SELECT * FROM t_douyin_certificate_cancel WHERE verify_id = #{verifyId} ORDER BY create_time DESC")
    List<DouyinCertificateCancel> selectByVerifyId(@Param("verifyId") String verifyId);

    /**
     * 根据券码标识查询记录
     * 
     * @param certificateId 券码标识
     * @return 撤销记录列表
     */
    @Select("SELECT * FROM t_douyin_certificate_cancel WHERE certificate_id = #{certificateId} ORDER BY create_time DESC")
    List<DouyinCertificateCancel> selectByCertificateId(@Param("certificateId") String certificateId);

    /**
     * 根据订单ID查询记录
     * 
     * @param orderId 订单ID
     * @return 撤销记录列表
     */
    @Select("SELECT * FROM t_douyin_certificate_cancel WHERE order_id = #{orderId} ORDER BY create_time DESC")
    List<DouyinCertificateCancel> selectByOrderId(@Param("orderId") String orderId);

    /**
     * 根据门店ID和状态查询记录
     * 
     * @param poiId 门店ID
     * @param status 状态
     * @return 撤销记录列表
     */
    @Select("SELECT * FROM t_douyin_certificate_cancel WHERE poi_id = #{poiId} AND status = #{status} ORDER BY create_time DESC")
    List<DouyinCertificateCancel> selectByPoiIdAndStatus(@Param("poiId") String poiId, @Param("status") String status);

    /**
     * 统计门店撤销数量
     * 
     * @param poiId 门店ID
     * @param status 状态
     * @return 撤销数量
     */
    @Select("SELECT COUNT(*) FROM t_douyin_certificate_cancel WHERE poi_id = #{poiId} AND status = #{status}")
    Integer countByPoiIdAndStatus(@Param("poiId") String poiId, @Param("status") String status);
}
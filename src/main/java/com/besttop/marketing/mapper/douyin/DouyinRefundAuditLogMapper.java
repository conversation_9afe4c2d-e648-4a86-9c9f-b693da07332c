package com.besttop.marketing.mapper.douyin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.besttop.marketing.entity.douyin.DouyinRefundAuditLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 抖音退款审核记录 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Mapper
public interface DouyinRefundAuditLogMapper extends BaseMapper<DouyinRefundAuditLog> {

    /**
     * 根据售后ID查询记录
     * 
     * @param afterSaleId 售后ID
     * @return 审核记录
     */
    @Select("SELECT * FROM t_douyin_refund_audit_log WHERE after_sale_id = #{afterSaleId} ORDER BY create_time DESC LIMIT 1")
    DouyinRefundAuditLog selectByAfterSaleId(@Param("afterSaleId") String afterSaleId);

    /**
     * 根据券码标识查询记录
     * 
     * @param certificateId 券码标识
     * @return 审核记录列表
     */
    @Select("SELECT * FROM t_douyin_refund_audit_log WHERE certificate_id = #{certificateId} ORDER BY create_time DESC")
    List<DouyinRefundAuditLog> selectByCertificateId(@Param("certificateId") String certificateId);

    /**
     * 根据审核结果查询记录
     * 
     * @param auditResult 审核结果
     * @return 审核记录列表
     */
    @Select("SELECT * FROM t_douyin_refund_audit_log WHERE audit_result = #{auditResult} ORDER BY create_time DESC")
    List<DouyinRefundAuditLog> selectByAuditResult(@Param("auditResult") Integer auditResult);

    /**
     * 根据状态查询记录
     * 
     * @param status 状态
     * @return 审核记录列表
     */
    @Select("SELECT * FROM t_douyin_refund_audit_log WHERE status = #{status} ORDER BY create_time DESC")
    List<DouyinRefundAuditLog> selectByStatus(@Param("status") String status);

    /**
     * 统计审核结果数量
     * 
     * @param auditResult 审核结果
     * @return 审核数量
     */
    @Select("SELECT COUNT(*) FROM t_douyin_refund_audit_log WHERE audit_result = #{auditResult}")
    Integer countByAuditResult(@Param("auditResult") Integer auditResult);

    /**
     * 根据时间范围查询记录
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 审核记录列表
     */
    @Select("SELECT * FROM t_douyin_refund_audit_log WHERE audit_time >= #{startTime} AND audit_time <= #{endTime} ORDER BY create_time DESC")
    List<DouyinRefundAuditLog> selectByTimeRange(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
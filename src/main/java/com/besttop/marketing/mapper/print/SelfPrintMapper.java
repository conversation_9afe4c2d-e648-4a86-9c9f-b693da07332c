package com.besttop.marketing.mapper.print;

import java.util.List;

import org.springframework.stereotype.Component;

import com.besttop.marketing.model.print.param.SelfPrintQueryParam;
import com.besttop.marketing.model.print.result.SelfPrintResult;
import com.besttop.marketing.model.shopping.ShoppingOrderInvoice;
import com.besttop.marketing.model.thirdparty.nuonuo.param.NuoNuoElecronicInvoiceInfo;

import feign.Param;

/**
 * <p>Title: SelfPrintMapper</p>
 * <p>Description: SelfPrintMapper</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018</p>
 * <p>自助打印 Mapper</p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */


@Component
public interface SelfPrintMapper {

    /**
     * @param com.besttop.marketing.model.print.param.SelfPrintQueryParam param
     * @return java.util.List<com.besttop.marketing.model.print.result.SelfPrintResult>
     * @methodName queryOrderInfo
     * @description 顾客客户手机号/流水号 查询已完成订单及自助开票相关信息
     * <AUTHOR>
     * @date 2021/03/30 09:41
     */
	List<SelfPrintResult> queryOrderInfo(SelfPrintQueryParam param);
	
	/**
     * @param com.besttop.marketing.model.thirdparty.nuonuo.param.NuoNuoElecronicInvoiceInfo param
     * @return List<com.besttop.marketing.model.shopping.ShoppingOrderInvoice>
     * @methodName queryInvoiceResult
     * @description 发票序列号查询发票信息
     * <AUTHOR>
     * @date 2021/04/09 18:24
     */
	List<ShoppingOrderInvoice> queryInvoiceResult(NuoNuoElecronicInvoiceInfo invoice);
	/**
     * @param java.lang.String param
     * @return java.lang.String
     * @methodName queryStoreCodeByOrderCode
     * @description 订单号查机构号
     * <AUTHOR>
     * @date 2021/04/09 18:24
     */
	String queryStoreCodeByOrderCode(@Param("orderCode") String orderCode);
}

package com.besttop.marketing.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 抖音SPI调用记录注解
 * <p>
 * 用于标记需要被切面拦截并记录到t_douyin_spi表的SPI接口方法
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DouyinSpiRecord {

    /**
     * SPI接口类型
     *
     * @return spi类型
     */
    String spiType();

    /**
     * 业务ID在请求体中的key
     * <p>
     * 用于从请求体Map中提取业务ID，例如"order_id"
     *
     * @return 业务id的key
     */
    String businessIdKey() default "order_id";
} 
package com.besttop.marketing.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.besttop.marketing.service.shopping.ShoppingOrderService;
import com.besttop.marketing.util.GetRedisUtil;
import com.besttop.rabbitmq.common.RabbitMessageListener;
import com.besttop.rabbitmq.prefix.RabbitLogisticsExchange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>Title: LedgerListener</p >
 * <p>Description: LedgerListener TODO</p >
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 * 2020-03-26 10:57:02  Create by striver
 * </pre>
 */

@Component
@Slf4j
public class MarkertingListener implements RabbitMessageListener {

    @Autowired
    private ShoppingOrderService shoppingOrderService;
    @Autowired
    private GetRedisUtil getRedisUtil;



    /**
     * @return void
     * @methodName onMessage
     * @description 预售转销监听
     * @params [message]
     * <AUTHOR>
     * @date 2020/3/26 10:58
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "erp.marketing", durable = "true"),
            exchange = @Exchange(value = RabbitLogisticsExchange.LOGISTICS_BOOK_EXCHANGE,
                    type = RabbitLogisticsExchange.LOGISTICS_BOOK_EXCHANGE_TYPE)
    ))
    @Override
    public void onMessage(Message message) {
        String body = new String(message.getBody());

        log.info("监听到消息 body : {}", body);
        JSONObject obj = JSONObject.parseObject(body);
        try {
            // 预售转销前等待 发送消息的实物提交后再执行
            Thread.sleep(3 * 1000);
            log.info("监听到消息 预售转销 开始消费");
            // 判断系统参数是否开启自动转销，没有开启 不执行转销
           if(!getRedisUtil.getPresaleToSale()){
               log.info("预售转销 自动开关关闭");
               return;
           }
            shoppingOrderService.preSaleToSale(obj);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("预售转销回冲失败----" + JSON.toJSON(e.getStackTrace()));
            log.info("预售转销回冲失败----" + e.getMessage());
        }
    }
}

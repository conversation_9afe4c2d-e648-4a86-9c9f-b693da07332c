package com.besttop.marketing.listener;

//import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
//import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

//@Component
//@Slf4j
public class PushOrderToDmallConfig {
//	@Autowired
//	private AmqpAdmin amqpAdmin;

//	public static final String QUEUE_SALE_ORDER = "sale.order";
//	public static final String QUEUE_REFUND_ORDER = "refund.order";
//	
//	public static final String ROUTING_KEY_SALE = "sale.order";
//	public static final String ROUTING_KEY_REFUND = "refund.order";
//	
//	public static final String EXCHANGE_PUSH_ORDER = "PushOrderToDmall";
//
//	@Bean
//	public TopicExchange pushOrderToDmall() {
//		TopicExchange topicExchange = new TopicExchange(EXCHANGE_PUSH_ORDER, true, false);
//		log.info("====PushOrderToDmallConfig==EXCHANGE=={} INIT DONE!", EXCHANGE_PUSH_ORDER);
//		return topicExchange;
//	}
//	
//	@Bean
//	public Queue saleOrderQueue() {
//		Queue saleOrderQueue = new Queue(QUEUE_SALE_ORDER, true, true, false);
//		log.info("====PushOrderToDmallConfig==QUEUE=={} INIT DONE!", QUEUE_SALE_ORDER);
//		return saleOrderQueue;
//	}
//	
//	@Bean
//	public Queue refundOrderQueue() {
//		Queue refundOrderQueue = new Queue(QUEUE_REFUND_ORDER, true, true, false);
//		log.info("====PushOrderToDmallConfig==QUEUE=={} INIT DONE!", QUEUE_REFUND_ORDER);
//		return refundOrderQueue;
//	}
//	
//	@Bean
//	public Binding saleOrderBinding() {
//		Binding saleOrderBinding = new Binding(QUEUE_SALE_ORDER, Binding.DestinationType.QUEUE, 
//				EXCHANGE_PUSH_ORDER, ROUTING_KEY_SALE , null);
//		log.info("====PushOrderToDmallConfig==BINDING==QUEUE({})--ROUTING({})--EXCHANGE({}) INIT DONE!", 
//				QUEUE_SALE_ORDER, ROUTING_KEY_SALE, EXCHANGE_PUSH_ORDER);
//		return saleOrderBinding;
//	}
//	
//	@Bean
//	public Binding refundOrderBinding() {
//		Binding refundOrderBinding = new Binding(QUEUE_REFUND_ORDER, Binding.DestinationType.QUEUE, 
//				EXCHANGE_PUSH_ORDER, ROUTING_KEY_REFUND, null);
//		log.info("====PushOrderToDmallConfig==BINDING==QUEUE({})--ROUTING({})--EXCHANGE({}) INIT DONE", QUEUE_REFUND_ORDER, 
//				ROUTING_KEY_REFUND, EXCHANGE_PUSH_ORDER);
//		return refundOrderBinding;
//	}
}

package com.besttop.marketing.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.besttop.marketing.config.DouyinSpiQueueConfig;
import com.besttop.marketing.mapper.thirdparty.douyin.DouyinSpiMapper;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSpi;
import com.besttop.marketing.model.thirdparty.douyin.DouyinSpiAnalysisLog;
import com.besttop.marketing.service.thirdparty.douyin.DouyinSpiAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 抖音SPI消息监听器
 * 处理抖音SPI日志和记录的异步消息队列
 */
@Component
@Slf4j
public class DouyinSpiMessageListener {

    @Autowired
    private DouyinSpiAnalysisService analysisService;
    
    @Autowired
    private DouyinSpiMapper douyinSpiMapper;

    /**
     * 处理日志队列消息
     */
    @RabbitListener(queues = DouyinSpiQueueConfig.QUEUE_SPI_LOG)
    public void handleLogMessage(Message message) {
        try {
            String body = new String(message.getBody());
            log.debug("收到抖音SPI日志消息: {}", body);
            
            // 修复JSON解析 - 处理可能存在的重复序列化问题
            Map<String, Object> messageData;
            try {
                // 尝试直接解析
                messageData = JSON.parseObject(body, Map.class);
            } catch (Exception e) {
                // 如果失败，可能是JSON字符串被重复序列化，先解码一次
                log.debug("直接解析失败，尝试处理重复序列化: {}", e.getMessage());
                body = body.trim();
                // 处理引号转义和特殊情况
                if (body.startsWith("\"") && body.endsWith("\"")) {
                    // 去除首尾引号并解码JSON转义字符
                    body = body.substring(1, body.length() - 1);
                    body = body.replace("\\\"", "\"")
                               .replace("\\\\", "\\")
                               .replace("\\/", "/");
                }
                messageData = JSON.parseObject(body, Map.class);
            }
            
            String messageId = (String) messageData.get("messageId");
            String messageType = (String) messageData.get("type");
            
            if ("CREATE".equals(messageType)) {
                // 处理创建日志消息
                DouyinSpiAnalysisLog logEntity = JSON.parseObject(
                    JSON.toJSONString(messageData.get("data")), 
                    DouyinSpiAnalysisLog.class
                );
                analysisService.recordAnalysisLog(logEntity);
                log.info("异步记录抖音SPI日志成功, messageId: {}", messageId);
            } else if ("UPDATE".equals(messageType)) {
                // 处理更新日志消息
                @SuppressWarnings("unchecked")
                Map<String, Object> updateParams = (Map<String, Object>) messageData.get("data");
                
                String id = (String) updateParams.get("id");
                Date responseTime = new Date((Long) updateParams.get("responseTime"));
                String businessResult = (String) updateParams.get("businessResult");
                String businessErrorCode = (String) updateParams.get("businessErrorCode");
                String businessErrorMsg = (String) updateParams.get("businessErrorMsg");
                String businessDataJson = (String) updateParams.get("businessDataJson");
                
                analysisService.updateAnalysisLog(
                    id, responseTime, businessResult, 
                    businessErrorCode, businessErrorMsg, businessDataJson
                );
                log.info("异步更新抖音SPI日志成功, messageId: {}", messageId);
            }
        } catch (Exception e) {
            log.error("处理抖音SPI日志消息失败", e);
        }
    }

    /**
     * 处理记录队列消息
     */
    @RabbitListener(queues = DouyinSpiQueueConfig.QUEUE_SPI_RECORD)
    public void handleRecordMessage(Message message) {
        try {
            String body = new String(message.getBody());
            log.debug("收到抖音SPI记录消息: {}", body);
            
            // 修复JSON解析 - 处理可能存在的重复序列化问题
            Map<String, Object> messageData;
            try {
                // 尝试直接解析
                messageData = JSON.parseObject(body, Map.class);
            } catch (Exception e) {
                // 如果失败，可能是JSON字符串被重复序列化，先解码一次
                log.debug("直接解析失败，尝试处理重复序列化: {}", e.getMessage());
                body = body.trim();
                // 处理引号转义和特殊情况
                if (body.startsWith("\"") && body.endsWith("\"")) {
                    // 去除首尾引号并解码JSON转义字符
                    body = body.substring(1, body.length() - 1);
                    body = body.replace("\\\"", "\"")
                               .replace("\\\\", "\\")
                               .replace("\\/", "/");
                }
                messageData = JSON.parseObject(body, Map.class);
            }
            
            String messageId = (String) messageData.get("messageId");
            String messageType = (String) messageData.get("type");
            
            if ("CREATE".equals(messageType)) {
                // 处理创建记录消息
                @SuppressWarnings("unchecked")
                Map<String, Object> recordData = (Map<String, Object>) messageData.get("data");
                
                DouyinSpi spiLog = new DouyinSpi();
                spiLog.setSpiType((String) recordData.get("spiType"));
                spiLog.setCreateTime(LocalDateTime.now());
                spiLog.setRequestIp((String) recordData.get("requestIp"));
                spiLog.setRequestHeaders((String) recordData.get("requestHeaders"));
                spiLog.setRequestParams((String) recordData.get("requestParams"));
                spiLog.setBusinessId((String) recordData.get("businessId"));
                spiLog.setProcessStatus(0); // 0: 处理中
                
                douyinSpiMapper.insert(spiLog);
                log.info("异步记录抖音SPI数据成功, messageId: {}, id: {}", messageId, spiLog.getId());
                
                // 返回创建的记录ID，方便后续更新
                recordData.put("id", spiLog.getId());
            } else if ("UPDATE".equals(messageType)) {
                // 处理更新记录消息
                @SuppressWarnings("unchecked")
                Map<String, Object> updateData = (Map<String, Object>) messageData.get("data");
                
                String id = updateData.get("id").toString();
                DouyinSpi spiLog = douyinSpiMapper.selectById(id);
                if (spiLog != null) {
                    Integer processStatus = (Integer) updateData.get("processStatus");
                    spiLog.setProcessStatus(processStatus);
                    spiLog.setProcessResult((String) updateData.get("processResult"));
                    spiLog.setErrorMessage((String) updateData.get("errorMessage"));
                    spiLog.setProcessTime((Long) updateData.get("processTime"));
                    spiLog.setUpdateTime(LocalDateTime.now());
                    
                    douyinSpiMapper.updateById(spiLog);
                    log.info("异步更新抖音SPI记录成功, messageId: {}, id: {}", messageId, id);
                } else {
                    log.warn("更新的抖音SPI记录不存在, id: {}", id);
                }
            }
        } catch (Exception e) {
            log.error("处理抖音SPI记录消息失败", e);
        }
    }
} 
package com.besttop.marketing.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.besttop.marketing.model.channel.ChannelSpecialPriceApply;
import com.besttop.marketing.model.enums.CommonEnums;
import com.besttop.marketing.service.channel.ChannelSpecialPriceApplyService;
import com.besttop.rabbitmq.common.RabbitMessageListener;
import com.besttop.rabbitmq.prefix.RabbitApprovalExchange;
import com.besttop.rabbitmq.prefix.RabbitMarketingExchange;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class ChannelSpecialListener implements RabbitMessageListener {

    @Autowired
    ChannelSpecialPriceApplyService channelSpecialPriceApplyService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "erp.marketings", durable = "true"),
            exchange = @Exchange(value = RabbitMarketingExchange.APPROVAL_EXCHANGE,
                    type = RabbitMarketingExchange.APPROVAL_EXCHANGE_TYPE)
    ))
    @Override
    public void onMessage(Message message) {
        String body = new String(message.getBody());
        Gson gson = new Gson();
        Map map = gson.fromJson(body, Map.class);
        log.info("消息接收成功，正在处理中>>>>>>>>>" + map);
        if (map.get("type").toString().equals(SpecialPriceTopicParam.SPECIAL_PRICE_APPLY)) {
            updateSpecialPriceStatus(map);
        }
    }

    private void updateSpecialPriceStatus(Map map) {
        QueryWrapper<ChannelSpecialPriceApply> query = new QueryWrapper<>();
        query.eq("code", map.get("billCode"));
        ChannelSpecialPriceApply one = channelSpecialPriceApplyService.getOne(query);
        if (map.get("flag").toString().equals("1")) {
            one.setApprovalStatus(CommonEnums.BILL_APPROPRIATION_APPROVAL_STATUS_C.getCode());
            one.setAuditTime(new Date());
        } else if (map.get("flag").toString().equals("0")) {
            one.setApprovalStatus(CommonEnums.BILL_APPROPRIATION_APPROVAL_STATUS_D.getCode());
        }
        channelSpecialPriceApplyService.updateById(one);
    }
}

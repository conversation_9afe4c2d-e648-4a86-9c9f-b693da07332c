package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillScheduleLimit;
import com.besttop.marketing.model.bill.BillScheduleLimitDetail;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BillSchedule</p>
 * <p>Description: BillSchedule</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/13 18:04 Create by mn773
 * </pre>
 */
@Data
public class BillScheduleLimitParam extends BillScheduleLimit {

    @RedisCacheRecursive
    @Valid
    private List<BillScheduleLimitDetail> details;

    /**
     *是否审核 0.不审核 1.审核
     */
    private int flag;

    /**
     *sku编码
     */
    private String skuCode;

    /**
     *规则编码结合
     */
    private List<String> codes;

    private String orderByField;  
}

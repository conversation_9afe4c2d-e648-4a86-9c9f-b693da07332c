package com.besttop.marketing.model.bill.result;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillGroupDefineDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: BillGroupResult</p>
 * <p>Description: BillGroupResult</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/4/7 10:45 Create by <PERSON><PERSON>
 * </pre>
 */
@Data
public class BillGroupResult {

    /**
     * 捆绑单号
     */
    private String code;

    /**
     * 零售价
     */
    private String price;

    /**
     * 捆绑后售价
     */
    private BigDecimal mainSkuPrice;

    /**
     * 捆绑限制
     */
    private BigDecimal bindQtty;

    /**
     * 捆绑商品
     */
    @RedisCacheRecursive
    private List<BillGroupDefineDetail> detailList;
}

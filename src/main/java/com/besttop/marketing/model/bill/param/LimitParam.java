package com.besttop.marketing.model.bill.param;

import com.besttop.common.validate.GroupQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: LimitParam</p>
 * <p>Description: 导购开票中的限量限价</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 *       2020/3/23 10:14 Create by mn773
 *</pre>
*/
@Data
public class LimitParam {


    /**
     *规则单编码
     */
    private String code;

    /**
     * 生效机构
     */
    private String effectStore;

    /**
     * 限价
     */
    private BigDecimal minPrice;

    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空", groups = {GroupQuery.class})
    private String skuCode;

    /**
     *抢购数量
     */
    private BigDecimal qtty;

    /**
     * 活动价
     */
    private BigDecimal discPrice;

    /**
     * 厂家券可用比例
     */
    private BigDecimal couponRate;

    /**
     *抢购结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     *时间差
     */
    private long time;

    private String useScope;
    
    private String exclusiveRule;

}

package com.besttop.marketing.model.bill.param;

import com.besttop.common.validate.GroupQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则定义
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class CoinProUseParam {

    /**
     *券编码
     */
    private String code;

    /**
     *顾客编码
     */
    private String customerCode;

    /**
     *券名称
     */
    private String name;

    /**
     *使用金额不能为空
     */
    @NotNull(message = "结算金额不能为空", groups = {GroupQuery.class})
    private BigDecimal amount;

    /**
     * 分类编码
     */
    @NotNull(message = "品类编码不能为空", groups = {GroupQuery.class})
    private String classCode;

    /**
     * 品牌编码
     */
    @NotNull(message = "品牌编码不能为空", groups = {GroupQuery.class})
    private String brandCode;

    /**
     * sku编码
     */
    @NotNull(message = "sku编码不能为空", groups = {GroupQuery.class})
    private String skuCode;

    /**
     *券类型
     */
    @NotBlank(message = "券类型不能为空", groups = {GroupQuery.class})
    private String type;

    private String message;

    /**
     *开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     *满金额
     */
    private BigDecimal reachAmount;

    /**
     *用金额
     */
    private BigDecimal useAmount;

    /**
     *上限金额
     */
    private BigDecimal maxAmount;
    
    /**
     * 根据商品单价, 满. 用, 上限计算后的最大可用金额
     */
    private BigDecimal maxCanUseAmount;

    /**
     *币券code
     */
    private String coinPromotionCode;

    /**
     * 币券定义code
     */
    private String promotionCode;

    /**
     * 券类型(1: 普通, 2: 内购)
     */
    private String promotionType;

    /**
     *是否选中
     */
    private boolean choose;

    /**
     *规则单编号
     */
    private List<String> codes;

    /**
     *有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireDate;

    /**
     *有效期类型
     */
    private Integer effectType;

    /**
     *生效机构
     */
    private String effectStore;

    private String storeCode;

    /**
     *适用范围
     */
    private String useScope;

    private Integer isFinish;

    /**
     * 券说明
     */
    private String promotionNote;
}

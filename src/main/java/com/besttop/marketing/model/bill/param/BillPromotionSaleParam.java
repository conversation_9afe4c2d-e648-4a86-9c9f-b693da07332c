package com.besttop.marketing.model.bill.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.marketing.model.bill.BillPromotionSale;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: BillPromotionSaleParam</p>
 * <p>Description: BillPromotionSaleParam</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/12 17:36 Create by zx
 * </pre>
 */
@Data
public class BillPromotionSaleParam extends BillPromotionSale {


    private Date startTime;

    private Date endTime;

    private String typeCode;

    @TableField(exist = false)
    private Integer page;

    @TableField(exist = false)
    private Integer rows;

    private String orderByField;

}

package com.besttop.marketing.model.bill.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则定义
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class CoinProUseDTO implements Serializable {

    private String skuCode;

    private List<CoinProUseResult> coupons;

}

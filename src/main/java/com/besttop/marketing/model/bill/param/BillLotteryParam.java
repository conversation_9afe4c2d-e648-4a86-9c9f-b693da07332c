package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillLottery;
import com.besttop.marketing.model.bill.BillLotteryDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Data
public class BillLotteryParam extends BillLottery {
    @RedisCacheRecursive
    @Valid
    private List<BillLotteryDetail> details;

    /**
     * 是否审核 0.不审核 1.审核
     */
    private int flag;


    private String orderByField;

    /**
     * 制单开始时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date beginMakeTime;

    /**
     * 制单结束时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date endMakeTime;

    /**
     * 品类编码
     */
    private String classCode;
    /**
     * 品牌编码
     */
    private String brandCode;
    /**
     * sku编码
     */
    private String skuCode;
}

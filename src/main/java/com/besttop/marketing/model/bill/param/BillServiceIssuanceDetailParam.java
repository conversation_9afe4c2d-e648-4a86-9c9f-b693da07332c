package com.besttop.marketing.model.bill.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.marketing.model.bill.BillServiceIssuanceDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BillServiceIssuanceDetailParam extends BillServiceIssuanceDetail {

    /**
     * 制单开始时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date beginMakeTime;

    /**
     * 制单结束时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date endMakeTime;
    //
    private String skuName;

    private String model;

    private String customerName;

    private String phone;

    private String code;

    private String name;


    private String skuPrice;

    private List<String> serviceCodes;

    @TableField(exist = false)
    private String orderByField;
    @TableField(exist = false)
    private String dCode;
    @TableField(exist = false)
    private List<BillServiceIssuanceDetailItemParam> billServiceIssuanceDetailItemParams;

    private String storeCode;

}

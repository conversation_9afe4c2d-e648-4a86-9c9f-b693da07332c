package com.besttop.marketing.model.bill.param;

import com.besttop.common.validate.GroupQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则定义
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class CoinProUseQueryParam {

    /**
     *券编码
     */
    private String code;

    /**
     *顾客编码
     */
    @NotNull(message = "顾客编码不能为空", groups = {GroupQuery.class})
    private String customerCode;

    /**
     *券名称
     */
    private String name;

    /**
     * 分类编码
     */
    @NotNull(message = "品类编码不能为空", groups = {GroupQuery.class})
    private String classCode;

    /**
     * 品牌编码
     */
    @NotNull(message = "品牌编码不能为空", groups = {GroupQuery.class})
    private String brandCode;

    /**
     * sku编码
     */
    @NotNull(message = "sku编码不能为空", groups = {GroupQuery.class})
    private String skuCode;

    /**
     *有效期类型
     */
    private Integer effectType;

    /**
     *生效机构
     */
    private String effectStore;

    private String storeCode;

    /**
     *适用范围
     */
    private String useScope;

    /**
     *规则单编号
     */
    private List<String> codes;
}

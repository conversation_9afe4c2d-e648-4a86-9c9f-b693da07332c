package com.besttop.marketing.model.bill.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BillScheduleRuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigDecimal shareRate;

    private BigDecimal shareAmount;

    private BigDecimal amount;

    private String shareCode;

    private Integer isShare;

    private String feeType;

    private String unionFeeType;

    private String loginStoreCode;

    private BigDecimal originalRate;

    private Integer giftType;

}

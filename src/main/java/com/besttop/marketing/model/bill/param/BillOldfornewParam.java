package com.besttop.marketing.model.bill.param;

import com.besttop.marketing.model.bill.BillOldfornew;
import com.besttop.marketing.model.bill.BillScheduleService;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: BillScheduleServiceParam</p>
 * <p>Description: BillScheduleServiceParam</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/18 15:42 Create by Sissi
 * </pre>
 */
@Data
public class BillOldfornewParam extends BillOldfornew {

    private String skuCode;

    private List<String> storeCodes;

    private List<String> codes;

    private String orderByField;

    private BigDecimal price;

    private String classCode;

    private String brandCode;
}

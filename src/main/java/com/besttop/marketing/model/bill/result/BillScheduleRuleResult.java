package com.besttop.marketing.model.bill.result;

import com.besttop.marketing.model.bill.BillScheduleRule;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillScheduleRuleResult extends BillScheduleRule {

    //活动名称
    private String scheduleDefineName;

    //生效机构名称
    private String effectStoreName;

    //分摊金额
    private BigDecimal shareAmount;

    //满免分摊金额
    private BigDecimal freeShareRate;

    private String skuCode;

    private String classCode;

    private String brandCode;

    private String orderCode;

    private Integer giftType;

    private String useScopeName;

    //电子币名称
    private String coinPromotionDefineName;

}

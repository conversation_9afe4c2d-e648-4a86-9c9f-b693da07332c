package com.besttop.marketing.model.bill.result;

import com.besttop.marketing.model.bill.BillScheduleServiceRule;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: BillScheduleServiceResult</p>
 * <p>Description: BillScheduleServiceResult</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/19 18:22 Create by zx
 * </pre>
 */
@Data
public class BillScheduleServiceRuleResult extends BillScheduleServiceRule {
    /**
     *服务开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectDate;

    /**
     *服务结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;

    /**
     * 单据号
     */
    private String code;
}

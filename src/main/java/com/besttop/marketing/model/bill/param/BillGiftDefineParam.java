package com.besttop.marketing.model.bill.param;

import com.besttop.marketing.model.bill.BillGiftDefine;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: BillGiftDefineParam</p>
 * <p>Description: BillGiftDefineParam</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/17 17:13 Create by <PERSON><PERSON>
 * </pre>
 */
@Data
public class BillGiftDefineParam extends BillGiftDefine {

    private String skuCode;

    private String classCode;

    private String brandCode;

    private List<String> storeCodes;

    private List<String> codes;

    private String orderByField;

    private String customerCode;

    private String queryName;

    /**
     * 来源编号  1  售后服务端
     */
    private String sourceCode;
}

package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillAcpp;
import com.besttop.marketing.model.bill.BillAcppDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: BillSchedule</p>
 * <p>Description: BillSchedule</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/13 18:04 Create by mn773
 * </pre>
 */
@Data
public class BillAcppParam extends BillAcpp {

    @RedisCacheRecursive
    @Valid
    private List<BillAcppDetail> details;

    /**
     *是否审核 0.不审核 1.审核
     */
    private int flag;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    private String orderByField;
}

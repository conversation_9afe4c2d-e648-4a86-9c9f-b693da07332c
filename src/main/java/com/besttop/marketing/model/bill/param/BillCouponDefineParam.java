package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillCouponDefine;
import com.besttop.marketing.model.bill.BillCouponDefineDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;


/**
 *优惠券定义
 */
@Data
@Accessors(chain = true)
public class BillCouponDefineParam extends BillCouponDefine {

    @RedisCacheRecursive
    @Valid
    List<BillCouponDefineDetail> details;

    /**
     *是否审核 0.不审核 1.审核
     */
    int flag;

    List<String> storeCodes;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date endTime;

    private String skuCode;

    /**
     * 明细sku_code
     */
    private String goodsSkuCode;

    private String orderByField;

    /**
     * 品类编码
     */
    private List<String> classCodes;

    /**
     * 品牌编码
     */
    private List<String> brandCodes;

}

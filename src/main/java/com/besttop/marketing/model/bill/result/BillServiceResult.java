package com.besttop.marketing.model.bill.result;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class BillServiceResult {
    //规则单编码
    private String code;
    //规则名称
    private String name;
    //服务编码
    private String skuCode;
    //服务名称
    private String skuName;
    //规格型号
    private String model;
    //数量
    private String qtty;
    //零售价
    private String skuPrice;
    //备注
    private String note;
    @TableField(exist = false)
    private String dCode;

}

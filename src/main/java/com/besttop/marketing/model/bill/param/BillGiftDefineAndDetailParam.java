package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillGiftDefine;
import com.besttop.marketing.model.bill.BillGiftDefineDetail;
import com.besttop.marketing.model.bill.BillGiftDefineGifts;
import com.besttop.redis.annotation.AddInject;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BillGiftDefineAndDetailPram</p>
 * <p>Description: BillGiftDefineAndDetailPram  礼品规则单</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/17 13:58 Create by <PERSON>ssi
 * </pre>
 */
@Data
public class BillGiftDefineAndDetailParam {

    private static final long serialVersionUID = 1L;

    @Valid
    @RedisCacheRecursive
    @AddInject
    private BillGiftDefine giftDefine;

    @Valid
    @RedisCacheRecursive
    private List<BillGiftDefineDetail> detailList;

    @Valid
    @RedisCacheRecursive
    private List<BillGiftDefineGifts> giftsList;

    private List<String> ids;

    private List<String> giftsIds;
}

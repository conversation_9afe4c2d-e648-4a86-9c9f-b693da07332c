package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillGroupDefine;
import com.besttop.marketing.model.bill.BillGroupDefineDetail;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BillGroupDefineParam</p>
 * <p>Description: 捆绑商品设置</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 *       2020/3/24 14:53 Create by mn773
 *</pre>
*/
@Data
public class BillGroupDefineParam extends BillGroupDefine {

    @RedisCacheRecursive
    @Valid
    private List<BillGroupDefineDetail> details;

    /**
     *是否审核 0.不审核 1.审核
     */
    private int flag;

    /**
     *捆绑商品
     */
    private String skuCode;

    /**
     *编码集合
     */
    private List<String> codes;

    private String orderByField;

}

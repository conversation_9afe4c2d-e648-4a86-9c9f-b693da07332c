package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillCoinProShare;
import com.besttop.marketing.model.bill.BillCoinProShareDetail;
import com.besttop.redis.annotation.AddInject;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BillCoinProShareAndDetailParam</p>
 * <p>Description: BillCoinProShareAndDetailParam 电子币/促销券分摊</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 *       2020/2/25 10:22 Create by lenovo
 *</pre>
*/
@Data
public class BillCoinProShareAndDetailParam {

    @Valid
    @AddInject
    @RedisCacheRecursive
    private BillCoinProShare coinProShare;

    @Valid
    private List<BillCoinProShareDetail> detailList;

    private List<String> ids;

}

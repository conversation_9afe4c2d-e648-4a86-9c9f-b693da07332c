package com.besttop.marketing.model.bill.param;

import com.besttop.marketing.model.bill.BillCoinProUseDetail;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则明细
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class BillCoinProUseDetailParam extends BillCoinProUseDetail {

    private Integer page;

    private Integer rows;

    /**
     *1电子币 2 促销券
     */
    private String type;



}

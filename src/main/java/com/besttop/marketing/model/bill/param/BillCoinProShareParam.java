package com.besttop.marketing.model.bill.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.marketing.model.bill.BillCoinProShare;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: BillCoinProShareParam</p>
 * <p>Description: BillCoinProShareParam 电子币、促销券分摊规则单查询入参</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version 0.0.1
 * <pre>Histroy:
 *       2020/2/25 14:15 Create by <PERSON>ssi
 * </pre>
 */
@Data
public class BillCoinProShareParam extends BillCoinProShare {

    private String skuCode;

    @TableField(
            exist = false
    )
    private Integer page;
    @TableField(
            exist = false
    )
    private Integer rows;

    private List<String> storeCodes;

    private List<String> codes;

    private String orderByField;
}

package com.besttop.marketing.model.bill.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.marketing.model.bill.BillCoinProUse;
import com.besttop.marketing.model.bill.BillCoinProUseDetail;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则定义
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class BillCoinProUseParam extends BillCoinProUse {

    /**
     *商品详细
     */
    private List<BillCoinProUseDetail> details;

    /**
     *编码集合
     */
    private List<String> codes;

    private List<String> storeCodes;

    private String skuCode;

    /**
     *0:新增/编辑，1:新增/编辑并审核
     */
    private int flag;

    private String orderByField;

    /**
     * 品牌
     */
    @TableField(exist = false)
    private String brandCode;
    /**
     * 品类
     */
    @TableField(exist = false)
    private String classCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date effectStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date effectEnd;

    /**
     * 是否互斥：0否 1是
     */
    private Integer isExcluding;

    /**
     * 品牌 多筛选
     */
    @TableField(exist = false)
    private List<String> brandCodes;
    /**
     * 品类 多筛选
     */
    @TableField(exist = false)
    private List<String> classCodes;

    /**
     * 促销类型名称
     */
    @TableField(exist = false)
    private String coinPromotionName;

}

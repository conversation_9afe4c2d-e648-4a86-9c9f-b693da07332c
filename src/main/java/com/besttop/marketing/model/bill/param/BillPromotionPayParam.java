package com.besttop.marketing.model.bill.param;

import com.besttop.marketing.model.bill.BillPromotionPay;
import com.besttop.marketing.model.bill.BillPromotionPayDetail;
import lombok.Data;

import java.util.List;

@Data
public class BillPromotionPayParam extends BillPromotionPay {


    /**
     * 券销售明细
     */
    private List<BillPromotionPayDetail> billPromotionPayDetailList;

    /**
     * opeid
     */
    private String openId;

    /**
     * 支付方式
     */
    private String payConfigCode;

    /**
     * 商户订单号  是否必填：Y
     */
    private String orderNo;

    private String sign;

    private String timeStamp;
}

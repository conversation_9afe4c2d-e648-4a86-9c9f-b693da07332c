package com.besttop.marketing.model.bill.param;

import com.besttop.marketing.model.bill.BillPromotionSale;
import com.besttop.marketing.model.bill.BillPromotionSaleDetail;
import com.besttop.marketing.model.thirdparty.param.PaymentParam;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BillPromotionSaleParam</p>
 * <p>Description: BillPromotionSaleParam 促销券销售入参</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/3 17:12 Create by <PERSON><PERSON>
 * </pre>
 */
@Data
@Accessors(chain = true)
public class BillPromotionSalePayParam extends BillPromotionSale {

    private static final long serialVersionUID = 1L;

    /**
     * 促销券销售明细
     */
    @Valid
    private List<BillPromotionSaleDetail> detailList;

    /**
     * 促销券销售支付记录
     */
    private List<PaymentParam> paymentParamList;

}

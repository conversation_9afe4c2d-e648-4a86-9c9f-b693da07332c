package com.besttop.marketing.model.bill.param;

import java.util.Date;
import java.util.List;

import com.besttop.mybatis.model.BasicExcludeUpdateEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class BigMemberVendorCouponParam extends BasicExcludeUpdateEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7023802692354009437L;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .顾客编码
	 */
	private String customerCode;
	
	private List<CouponItem> coupons;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .券定义编码
	 */
	private String couponDefineCode;
	/**
	 * e-msr-bigmember服务调用时使用
	 * .券定义名称
	 */
	private String couponDefineName;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .券编码
	 */
	private String couponCode;
	/**
	 * e-msr-bigmember服务调用时使用
	 *.券名称
	 */
	private String couponName;
	
    /**
     * e-msr-bigmember服务调用时使用
     * .代为发券的业态
     */

	private String giveChannel;

	/**
	 * e-msr-bigmember服务调用时使用
	 * .促销券投放规则适用范围
	 */
	private String useScope;
	/**
	 * 服务券-来源渠道
	 */
	private String serviceChannel;
	/**
	 * 促销券-来源渠道
	 */
	private String couponChannel;
    
    /**
     * e-msr-bigmember服务调用时使用
     * .活动档期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /**
     * e-msr-bigmember服务调用时使用
     * .活动档期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    /**
     * e-msr-bigmember服务调用时使用
     * .券过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireDate;
    
    private String note;
    
    /**
     * e-msr-bigmember服务调用时使用
     * .券类型
     */
	private Integer couponType;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .是否过期  1: 过期, 0: 未过期
	 */
	private Integer isExpiredConpon;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .券状态
	 */
	private List<String> status;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .发放编码
	 */
	private String issuedCode;

}


package com.besttop.marketing.model.bill.param;

import com.besttop.common.validate.GroupQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: BillSchedule</p>
 * <p>Description: BillSchedule</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/13 18:04 Create by mn773
 * </pre>
 */
@Data
public class BillScheduleParam {


    /**
     *规则单编码
     */
    private String code;

    /**
     * '活动类型：1单品满返，2单品满减，3单品满赠，4套购满返，5套购满减，6套购满赠，7满免活动',
     */
    private String type;

    /**
     * 生效机构
     */
    private String effectStore;

    /**
     * 结算金额
     */
    @NotNull(message = "结算金额不能为空", groups = {GroupQuery.class})
    private BigDecimal standardAmount;

    /**
     * 品类编码
     */
    @NotBlank(message = "品类编码不能为空", groups = {GroupQuery.class})
    private String classCode;

    /**
     * 品牌编码
     */
    @NotBlank(message = "品牌编码不能为空", groups = {GroupQuery.class})
    private String brandCode;

    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空", groups = {GroupQuery.class})
    private String skuCode;

    /**
     * 达标单价
     */
    @NotNull(message = "实际售价不能为空", groups = {GroupQuery.class})
    private BigDecimal reachUnitPrice;

    /**
     * 达标单价,集合
     */
    private List<BigDecimal> reachUnitPriceList;

    /**
     *订单编号集合
     */
    private List<String> orderCodes;

    /**
     *订单编号
     */
    private String orderCode;

    /**
     *满数量
     */
    private Integer num;

    /**
     *达标品类数量
     */
    private Integer rateClassCount;

    private String useScope;
}

package com.besttop.marketing.model.bill.param;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则定义
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class CoinProUseParamNew extends CoinProUseParam {

    /**
     *开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    
    /**
     *有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;
    
    /**
     * 券投放规则note
     */
    private String couponPublishNote;
}

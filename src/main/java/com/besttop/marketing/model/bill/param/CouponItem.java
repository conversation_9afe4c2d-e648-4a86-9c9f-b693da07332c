package com.besttop.marketing.model.bill.param;

import java.io.Serializable;

import lombok.Data;

@Data
public class CouponItem implements Serializable{
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -2325158182816974075L;

	public CouponItem() {}
	/**
	 * e-msr-bigmember服务调用时使用
	 * .券定义编码
	 */
	private String couponDefineCode;
	
	/**
	 * e-msr-bigmember服务调用时使用
	 * .券编码
	 */
	private String couponCode;
	
	/**
	 * 券类型
	 */
	private Integer couponType;
	
	/**
	 * 发放数量
	 */
	private Integer qty;
	
	/**
	 * 发放编码
	 */
	private String issuedCode;
}
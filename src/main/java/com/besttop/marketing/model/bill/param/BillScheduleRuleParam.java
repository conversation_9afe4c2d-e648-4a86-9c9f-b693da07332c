package com.besttop.marketing.model.bill.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.marketing.model.bill.BillScheduleRule;
import lombok.Data;

import java.util.List;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 档期规则入参
 * @return
 * @date 2020/2/18 16:09
 */
@Data
public class BillScheduleRuleParam extends BillScheduleRule {

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 品类
     */
    private String classCode;

    /**
     * 品牌
     */
    private String brandCode;

    /**
     * 赠品编码
     */
    private String giftCode;

    @TableField(exist = false)
    private Integer page;
    @TableField(exist = false)
    private Integer rows;

    @TableField(exist = false)
    private String orderByField;

    private List<String> codes;

    private List<String> storeCodes;

    private List<String> statuses;
}

package com.besttop.marketing.model.bill.result;

import com.besttop.marketing.model.bill.param.CoinProUseParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @param
 * <AUTHOR>
 * @methodName
 * @description 电子币促销券使用规则定义
 * @return
 * @date 2020/2/24 16:09
 */
@Data
@Accessors(chain = true)
public class CoinProUseResult implements Serializable {

    private String code;

    private String name;

    /**
     * 用券门槛金额
     */
    private BigDecimal targetAmount;

    /**
     * 券可用金额
     */
    private BigDecimal amount;

    private BigDecimal faceAmount;
    /**
     *开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     *结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 券编码
     */
    private String coinPromotionCode;

    /**
     * 使用说明
     */
    private String note;


}

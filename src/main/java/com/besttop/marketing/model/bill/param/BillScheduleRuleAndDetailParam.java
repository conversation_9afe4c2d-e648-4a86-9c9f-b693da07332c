package com.besttop.marketing.model.bill.param;

import com.besttop.marketing.model.bill.BillScheduleRule;
import com.besttop.marketing.model.bill.BillScheduleRuleDetail;
import com.besttop.marketing.model.bill.BillScheduleRuleGifts;
import com.besttop.redis.annotation.AddInject;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import java.util.List;

@Data
@Accessors(chain = true)
public class BillScheduleRuleAndDetailParam {

    private static final long serialVersionUID = 1L;

    /**
     * 主表信息
     */
    @Valid
    @AddInject
    private BillScheduleRule billScheduleRule;

    /**
     * 明细信息
     */
    @Valid
    private List<BillScheduleRuleDetail> detailList;

    /**
     * 赠品信息
     */
    @Valid
    private List<BillScheduleRuleGifts> giftsList;

    /**
     * 删除信息ID
     */
    private List<String> ids;

    /**
     * 删除赠品ID
     */
    private List<String> giftsIds;

}

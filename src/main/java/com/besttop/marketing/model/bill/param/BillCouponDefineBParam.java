package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.BillCouponDefine;
import com.besttop.marketing.model.bill.BillCouponDefineBDetail;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;


/**
 *优惠券定义
 */
@Data
@Accessors(chain = true)
public class BillCouponDefineBParam extends BillCouponDefine {

    @RedisCacheRecursive
    @Valid
    List<BillCouponDefineBDetail> details;

    /**
     *是否审核 0.不审核 1.审核
     */
    int flag;

    List<String> storeCodes;

    Date startTime;

    Date endTime;

}

package com.besttop.marketing.model.bill.result;

import com.besttop.marketing.model.bill.BillScheduleLimit;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: BillScheduleLimitResult</p>
 * <p>Description: BillScheduleLimitResult</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/26 10:51 Create by <PERSON><PERSON>
 * </pre>
 */
@Data
public class BillScheduleLimitResult extends BillScheduleLimit {

    /**
     * 限量
     */
    private Integer maxQtty;

    /**
     * 锁定数量
     */
    private Integer lockQtty;

    /**
     * 已卖数量
     */
    private Integer saleQtty;

    /**
     * 剩余数量
     */
    private Integer balanceQtty;

    /**
     * 活动价
     */
    private BigDecimal discPrice;

    /**
     * 厂家券可用比例
     */
    private BigDecimal couponRate;

}

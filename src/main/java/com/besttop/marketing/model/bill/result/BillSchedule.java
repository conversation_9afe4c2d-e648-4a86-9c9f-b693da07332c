package com.besttop.marketing.model.bill.result;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.common.prefix.RedisPrefix_ERP;
import com.besttop.datacache.annotation.RedisCacheConvert;
import com.besttop.marketing.model.bill.BillScheduleRule;
import com.besttop.marketing.model.bill.BillScheduleRuleGifts;
import com.besttop.marketing.model.bill.param.BillScheduleParam;
import com.besttop.redis.config.prefix.RedisSystemPrefix;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: BillSchedule</p>
 * <p>Description: BillSchedule</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/13 18:04 Create by mn773
 * </pre>
 */
@Data
public class BillSchedule extends BillScheduleRule {

    /**
     *sku编码
     */
    @RedisCacheConvert(prefix = RedisPrefix_ERP.SHANG_PING_SKU_XIN_XI,convertList = {"name:skuName"})
    private String skuCode;

    /**
     *赠品单价
     */
    private BigDecimal skuPrice;

    /**
     *赠品图片
     */
    private String pic;

    /**
     *赠送数量
     */
    private Integer qtty;

    /**
     *sku名称
     */
    private String skuName;

    /**
     *出货位置
     */
    @RedisCacheConvert(prefix = RedisSystemPrefix.CONFIG_PREFIX,convertList = {"value:outTypeName"})
    private String outType;

    /**
     *满免单据号，或者满赠详情
     */
    private String other;

    /**
     *单据集合
     */
    private List<BillScheduleParam> orders;

    /**
     *选中标记
     */
    private boolean choose;

    /**
     *赠品列表
     */
    private List<BillScheduleRuleGifts> gifts;

    /**
     *应减/应返
     */
    private BigDecimal amount;

    /**
     *品类编码
     */
    private String classCode;

    /**
     *品牌编码
     */
    private String brandCode;

    /**
     *订单编号
     */
    private List<String> orderCodes;

    /**
     *达标单价
     */
    private BigDecimal reachUnitPrice;

    @TableField(exist = false)
    private Integer giftType;


    /**
     * 导购赠币类型
     */
    @TableField(exist = false)
    private String currencyCode;
}

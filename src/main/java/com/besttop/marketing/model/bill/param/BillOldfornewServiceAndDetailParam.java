package com.besttop.marketing.model.bill.param;

import com.besttop.datacache.annotation.RedisCacheRecursive;
import com.besttop.marketing.model.bill.*;
import com.besttop.redis.annotation.AddInject;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BillScheduleServiceAndDetailParam</p>
 * <p>Description: BillScheduleServiceAndDetailParam 商品服务配置</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/3/18 11:14 Create by Sissi
 * </pre>
 */
@Data
public class BillOldfornewServiceAndDetailParam {

    private static final long serialVersionUID = 1L;

    @Valid
    @RedisCacheRecursive
    @AddInject
    private BillOldfornew service;

    @Valid
    @RedisCacheRecursive
    private List<BillOldfornewLevel> ruleList;

    @Valid
    @RedisCacheRecursive
    private List<BillOldfornewDetail> detailList;

    private List<String> ruleIds;

    private List<String> detailIds;
}

package com.besttop.marketing.model.thirdparty.douyin;

import com.baomidou.mybatisplus.annotation.*;
import com.besttop.mybatis.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抖音礼品退款记录实体类
 * 记录抖音礼品券的退款操作信息
 */
@Data
@TableName("t_douyin_gift_refund_record")
public class DouyinGiftRefundRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;
    
    /** 退款ID */
    @TableField("refund_id")
    private String refundId;
    
    /** 券码 */
    @TableField("coupon_code")
    private String couponCode;
    
    /** 券ID */
    @TableField("coupon_id")
    private String couponId;
    
    /** 退款类型(UNVERIFIED/VERIFIED) */
    @TableField("refund_type")
    private String refundType;
    
    /** 退款场景 */
    @TableField("refund_scenario")
    private String refundScenario;
    
    /** 关联礼品发放单ID */
    @TableField("gift_record_id")
    private String giftRecordId;
    
    /** 退款状态 */
    @TableField("refund_status")
    private String refundStatus;
    
    /** 退款金额 */
    @TableField("refund_amount")
    private BigDecimal refundAmount;
    
    /** 退款原因 */
    @TableField("refund_reason")
    private String refundReason;
    
    /** 退款申请时间 */
    @TableField("refund_request_time")
    private Date refundRequestTime;
    
    /** 退款完成时间 */
    @TableField("refund_complete_time")
    private Date refundCompleteTime;
    
    /** 同步来源(DOUYIN/LAIKE) */
    @TableField("sync_from_source")
    private String syncFromSource;
    
    /** 请求数据JSON */
    @TableField("request_payload_json")
    private String requestPayloadJson;
    
    /** 响应数据JSON */
    @TableField("response_payload_json")
    private String responsePayloadJson;

    /*
      创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /*
      创建时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /*
      更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /*
      更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    /*
      逻辑删除
     */
    @JsonIgnore
    @TableLogic(value = "0", delval = "1")
    private int delFlag;
    
    // 退款类型常量
    public static final String TYPE_UNVERIFIED = "UNVERIFIED";
    public static final String TYPE_VERIFIED = "VERIFIED";
    
    // 退款场景常量
    public static final String SCENARIO_DIRECT_REFUND = "DIRECT_REFUND";
    public static final String SCENARIO_INFO_SYNC = "INFO_SYNC";
    
    // 退款状态常量
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_PROCESSING = "PROCESSING";
}
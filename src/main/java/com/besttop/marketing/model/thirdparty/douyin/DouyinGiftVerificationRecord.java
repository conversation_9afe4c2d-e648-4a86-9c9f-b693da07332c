package com.besttop.marketing.model.thirdparty.douyin;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 抖音礼品核销记录实体类
 * 记录抖音礼品券的核销操作信息
 */
@Data
@TableName("t_douyin_gift_verification_record")
public class DouyinGiftVerificationRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;
    
    /** 券码 */
    @TableField("coupon_code")
    private String couponCode;
    
    /** 券ID */
    @TableField("coupon_id")
    private String couponId;
    
    /** 抖音订单ID */
    @TableField("douyin_order_id")
    private String douyinOrderId;
    
    /** 核销时间 */
    @TableField("verification_time")
    private Date verificationTime;
    
    /** 门店编码 */
    @TableField("store_code")
    private String storeCode;
    
    /** 操作员ID */
    @TableField("staff_id")
    private String staffId;
    
    /** 礼品类型(A/B/C) */
    @TableField("gift_type")
    private String giftType;
    
    /** 关联礼品发放单ID */
    @TableField("gift_record_id")
    private String giftRecordId;
    
    /** 核销状态 */
    @TableField("verification_status")
    private String verificationStatus;
    
    /** 同步状态 */
    @TableField("sync_status")
    private String syncStatus;
    
    /** 礼品发放状态 */
    @TableField("gift_issue_status")
    private String giftIssueStatus;
    
    /** 顾客编码 */
    @TableField("customer_code")
    private String customerCode;
    
    /** 顾客手机号 */
    @TableField("phone")
    private String phone;

    /*
      创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /*
      创建时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /*
      更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /*
      更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    // 核销状态常量
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_PROCESSING = "PROCESSING";
    
    // 同步状态常量
    public static final String SYNC_STATUS_PENDING = "PENDING";
    public static final String SYNC_STATUS_SUCCESS = "SUCCESS";
    public static final String SYNC_STATUS_FAILED = "FAILED";
    
    // 礼品发放状态常量
    public static final String GIFT_ISSUE_STATUS_PENDING = "PENDING";  // 待发放
    public static final String GIFT_ISSUE_STATUS_ISSUED = "ISSUED";    // 已发放
    public static final String GIFT_ISSUE_STATUS_CANCELLED = "CANCELLED"; // 已作废
}
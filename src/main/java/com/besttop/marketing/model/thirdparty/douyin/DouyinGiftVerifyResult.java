package com.besttop.marketing.model.thirdparty.douyin;

import com.besttop.marketing.model.manual.ManualGiftRecord;
import lombok.Data;

import java.util.Map;

/**
 * 抖音礼品券核销结果实体
 * 封装礼品券核销操作的完整结果信息
 */
@Data
public class DouyinGiftVerifyResult {
    
    /**
     * 是否核销成功
     */
    private boolean success;
    
    /**
     * 失败原因/结果描述
     */
    private String message;
    
    /**
     * 券实例信息
     */
    private DouyinCoupon couponInfo;
    
    /**
     * 券规则信息
     */
    private DouyinCouponRule couponRule;
    
    /**
     * 礼品核销记录
     */
    private DouyinGiftVerificationRecord verificationRecord;
    
    /**
     * 礼品发放记录
     */
    private ManualGiftRecord giftRecord;
    
    /**
     * 礼品类型 (A/B/C)
     */
    private String giftType;
    
    /**
     * 核销类型：GIFT_A / GIFT_B / GIFT_C
     */
    private String verificationType;
    
    /**
     * 扩展信息
     */
    private Map<String, Object> additionalInfo;
    
    // 礼品类型常量
    public static final String GIFT_TYPE_A = "A"; // 电子币
    public static final String GIFT_TYPE_B = "B"; // 自营商品
    public static final String GIFT_TYPE_C = "C"; // 联营商品
    
    // 核销类型常量
    public static final String VERIFICATION_TYPE_GIFT_A = "GIFT_A";
    public static final String VERIFICATION_TYPE_GIFT_B = "GIFT_B";
    public static final String VERIFICATION_TYPE_GIFT_C = "GIFT_C";
    
    /**
     * 创建成功结果
     */
    public static DouyinGiftVerifyResult success(DouyinCouponVerifyResult couponResult, 
                                                DouyinGiftVerificationRecord verificationRecord,
                                                ManualGiftRecord giftRecord) {
        DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
        result.setSuccess(true);
        result.setMessage("礼品券核销成功");
        result.setCouponInfo(couponResult.getCouponInfo());
        result.setCouponRule(couponResult.getCouponRule());
        result.setVerificationRecord(verificationRecord);
        result.setGiftRecord(giftRecord);
        result.setGiftType(verificationRecord.getGiftType());
        result.setVerificationType("GIFT_" + verificationRecord.getGiftType());
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static DouyinGiftVerifyResult fail(String message) {
        DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 从券核销结果转换
     */
    public static DouyinGiftVerifyResult fromCouponResult(DouyinCouponVerifyResult couponResult) {
        DouyinGiftVerifyResult result = new DouyinGiftVerifyResult();
        result.setSuccess(couponResult.isSuccess());
        result.setMessage(couponResult.getMessage());
        result.setCouponInfo(couponResult.getCouponInfo());
        result.setCouponRule(couponResult.getCouponRule());
        return result;
    }
    
    /**
     * 获取礼品类型描述
     */
    public String getGiftTypeDescription() {
        switch (giftType) {
            case GIFT_TYPE_A:
                return "电子币";
            case GIFT_TYPE_B:
                return "自营商品";
            case GIFT_TYPE_C:
                return "联营商品";
            default:
                return "未知类型";
        }
    }
    
    /**
     * 是否为电子币类型
     */
    public boolean isElectronicCoin() {
        return GIFT_TYPE_A.equals(giftType);
    }
    
    /**
     * 是否为商品类型
     */
    public boolean isProduct() {
        return GIFT_TYPE_B.equals(giftType) || GIFT_TYPE_C.equals(giftType);
    }
    
    /**
     * 是否为联营商品
     */
    public boolean isJointVentureProduct() {
        return GIFT_TYPE_C.equals(giftType);
    }
}

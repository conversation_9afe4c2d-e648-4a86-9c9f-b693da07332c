package com.besttop.marketing.model.douyin.response;

import lombok.Data;
import java.util.List;

/**
 * 抖音订单状态查询响应详情
 * 
 * <AUTHOR> @since 2024-12-25
 */
@Data
public class DouyinOrderDetail {
    
    /**
     * 抖音侧订单ID
     */
    private String orderId;
    
    /**
     * 三方订单ID
     */
    private String outOrderId;
    
    /**
     * 抖音侧订单状态
     * 100-待支付，101-支付取消，200-已支付，201-可使用，1-已完成
     */
    private Integer orderStatus;
    
    /**
     * 抖音侧item单列表（售后场景使用）
     */
    private List<OrderItem> orderItems;
    
    /**
     * 三方码列表（履约场景使用）
     */
    private List<CodeRecord> codes;
    
    /**
     * 总凭证数量
     */
    private Integer totalQuantity;
    
    /**
     * 已核销凭证数量
     */
    private Integer usedQuantity;
    
    /**
     * 已退款凭证数量
     */
    private Integer refundQuantity;
    
    /**
     * 订单项详情
     */
    @Data
    public static class OrderItem {
        /**
         * 抖音skuId
         */
        private String skuId;
        
        /**
         * 三方skuId
         */
        private String outSkuId;
        
        /**
         * 抖音侧item单id
         */
        private String orderItemId;
        
        /**
         * 凭证单id
         */
        private String certificateId;
        
        /**
         * 抖音侧item单状态
         * 100-待使用，200-预约中，201-已预约，400-履约中，401-已履约，300-退款中，301-已退款
         */
        private Integer itemStatus;
        
        /**
         * 退款金额，单位分
         */
        private Long refundAmount;
        
        /**
         * 退款时间，秒级时间戳
         */
        private Long refundTime;
    }
    
    /**
     * 券码记录
     */
    @Data
    public static class CodeRecord {
        /**
         * 凭证值
         */
        private String value;
        
        /**
         * 凭证类型
         * code-三方码，qr_code-二维码，certificate_no-凭证号，id_card-身份证等
         */
        private String type;
        
        /**
         * 核销状态
         * 1-已核销，2-未核销，3-已退款
         */
        private Integer fulfilStatus;
        
        /**
         * 核销时间，秒级时间戳
         */
        private Long fulfilTime;
        
        /**
         * 核销幂等token
         */
        private String verifyToken;
        
        /**
         * 核销门店id
         */
        private String poiId;
    }
}

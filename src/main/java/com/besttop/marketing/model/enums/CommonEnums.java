package com.besttop.marketing.model.enums;

/**
 * <p>Title: CommonEnums</p>
 * <p>Description: CommonEnums 公共枚举类</p>
 * <p>Copyright: Xi An BestTop Technologies, ltd. Copyright(c) 2018/p>
 *
 * <AUTHOR>
 * @version *******
 * <pre>Histroy:
 *       2020/1/7 17:44 Create by <PERSON><PERSON>
 * </pre>
 */
public enum CommonEnums {

    //商品类型
    BASE_GOODS_TYPE_A("erp:goods_type:1", "自营单品"),
    BASE_GOODS_TYPE_B("erp:goods_type:2", "联营单品"),
    BASE_GOODS_TYPE_C("erp:goods_type:3", "联营租赁"),
    BASE_GOODS_TYPE_D("erp:goods_type:4", "联营类品"),
    BASE_GOODS_TYPE_E("erp:goods_type:5", "自营折让"),
    BASE_GOODS_TYPE_F("erp:goods_type:6", "联营服务"),
    BASE_GOODS_TYPE_G("erp:goods_type:7", "虚拟服务"),
    BASE_GOODS_TYPE_H("erp:goods_type:8", "付费服务"),

    //入库单单据类型
    STORAGE_INOUT_TYPE_A("erp:storage_inout_type:1", "采购入库"),
    STORAGE_INOUT_TYPE_B("erp:storage_inout_type:2", "商品退库"),
    STORAGE_INOUT_TYPE_C("erp:storage_inout_type:3", "调拨"),
    STORAGE_INOUT_TYPE_D("erp:storage_inout_type:4", "零售"),
    STORAGE_INOUT_TYPE_E("erp:storage_inout_type:5", "零售(现场提货)"),
    STORAGE_INOUT_TYPE_F("erp:storage_inout_type:6", "零售退货"),
    STORAGE_INOUT_TYPE_G("erp:storage_inout_type:7", "零售退货(现场提货)"),
    STORAGE_INOUT_TYPE_H("erp:storage_inout_type:8", "零售换货"),
    STORAGE_INOUT_TYPE_I("erp:storage_inout_type:9", "赠品"),
    STORAGE_INOUT_TYPE_J("erp:storage_inout_type:10", "零售换货(现场提货)"),
    STORAGE_INOUT_TYPE_K("erp:storage_inout_type:11", "城际调拨"),
    STORAGE_INOUT_TYPE_L("erp:storage_inout_type:12", "代管送货"),
    STORAGE_INOUT_TYPE_M("erp:storage_inout_type:13", "代管退货"),
    STORAGE_INOUT_TYPE_N("erp:storage_inout_type:14", "礼品"),
    STORAGE_INOUT_TYPE_O("erp:storage_inout_type:15", "积分兑换礼品"),
    STORAGE_INOUT_TYPE_T("erp:storage_inout_type:20", "渠道配送"),
    STORAGE_INOUT_TYPE_U("erp:storage_inout_type:21", "渠道退库"),
    STORAGE_INOUT_TYPE_V("erp:storage_inout_type:22", "渠道销售配送"),
    STORAGE_INOUT_TYPE_W("erp:storage_inout_type:23", "渠道销售退货"),

    //仓库出库取消日志类型
    STORAGE_INOUT_LOG_TYPE_A("erp:storage_inout_log_type:1", "入库"),
    STORAGE_INOUT_LOG_TYPE_B("erp:storage_inout_log_type:2", "出库"),
    STORAGE_INOUT_LOG_TYPE_C("erp:storage_inout_log_type:3", "取消出库"),


    //物流配送单单据类型
    LOGISTICS_DELIVER_BILL_TYPE_A("erp:logistics_deliver_bill_type:1", "零售送货"),
    LOGISTICS_DELIVER_BILL_TYPE_B("erp:logistics_deliver_bill_type:2", "零售退货"),
    LOGISTICS_DELIVER_BILL_TYPE_C("erp:logistics_deliver_bill_type:3", "零售换货"),
    LOGISTICS_DELIVER_BILL_TYPE_D("erp:logistics_deliver_bill_type:4", "分销送货"),
    LOGISTICS_DELIVER_BILL_TYPE_E("erp:logistics_deliver_bill_type:5", "分销退货"),
    LOGISTICS_DELIVER_BILL_TYPE_F("erp:logistics_deliver_bill_type:6", "调拨"),
    LOGISTICS_DELIVER_BILL_TYPE_G("erp:logistics_deliver_bill_type:7", "样机调拨"),
    LOGISTICS_DELIVER_BILL_TYPE_H("erp:logistics_deliver_bill_type:8", "临时送货"),
    LOGISTICS_DELIVER_BILL_TYPE_I("erp:logistics_deliver_bill_type:9", "城际调拨"),
    LOGISTICS_DELIVER_BILL_TYPE_J("erp:logistics_deliver_bill_type:10", "代管送货"),
    LOGISTICS_DELIVER_BILL_TYPE_K("erp:logistics_deliver_bill_type:11", "代管退货"),
    LOGISTICS_DELIVER_BILL_TYPE_L("erp:logistics_deliver_bill_type:12", "渠道销售配送"),
    LOGISTICS_DELIVER_BILL_TYPE_M("erp:logistics_deliver_bill_type:13", "渠道销售退货"),
    LOGISTICS_DELIVER_BILL_TYPE_N("erp:logistics_deliver_bill_type:14", "赠品礼品发放"),
    LOGISTICS_DELIVER_BILL_TYPE_O("erp:logistics_deliver_bill_type:15", "赠品礼品回收"),

    //物流配送单日志操作类型
    LOGISTICS_DELIVER_LOG_OPER_TYPE_A("erp:logistics_deliver_log_oper_type:1", "分拣"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_B("erp:logistics_deliver_log_oper_type:2", "派工"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_C("erp:logistics_deliver_log_oper_type:3", "派工确认"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_D("erp:logistics_deliver_log_oper_type:4", "出库"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_E("erp:logistics_deliver_log_oper_type:5", "签收"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_F("erp:logistics_deliver_log_oper_type:6", "取消派工"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_G("erp:logistics_deliver_log_oper_type:7", "取消分拣"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_H("erp:logistics_deliver_log_oper_type:8", "取消出库"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_I("erp:logistics_deliver_log_oper_type:9", "暂不发货"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_J("erp:logistics_deliver_log_oper_type:10", "改派"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_K("erp:logistics_deliver_log_oper_type:11", "暂存"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_L("erp:logistics_deliver_log_oper_type:12", "暂存出库"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_M("erp:logistics_deliver_log_oper_type:13", "入库"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_N("erp:logistics_deliver_log_oper_type:14", "拒收"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_O("erp:logistics_deliver_log_oper_type:15", "结算"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_P("erp:logistics_deliver_log_oper_type:16", "开单"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_R("erp:logistics_deliver_log_oper_type:18", "修改配送信息"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_S("erp:logistics_deliver_log_oper_type:26", "添加远期送货"),
    LOGISTICS_DELIVER_LOG_OPER_TYPE_T("erp:logistics_deliver_log_oper_type:27", "取消远期送货"),

    //物流配送单状态
    LOGISTICS_DELIVER_STATUS_A("erp:logistics_deliver_status:1", "待分拣"),
    LOGISTICS_DELIVER_STATUS_B("erp:logistics_deliver_status:2", "待派工"),
    LOGISTICS_DELIVER_STATUS_C("erp:logistics_deliver_status:3", "待确认"),
    LOGISTICS_DELIVER_STATUS_D("erp:logistics_deliver_status:4", "待出库"),
    LOGISTICS_DELIVER_STATUS_E("erp:logistics_deliver_status:5", "配送在途"),
    LOGISTICS_DELIVER_STATUS_F("erp:logistics_deliver_status:6", "配送完成"),
    LOGISTICS_DELIVER_STATUS_G("erp:logistics_deliver_status:7", "已作废"),
    //物流配送单暂存状态
    LOGISTICS_DELIVER_KEEP_STATUS_A("erp:logistics_deliver_keep_status:1", "未暂存"),
    LOGISTICS_DELIVER_KEEP_STATUS_B("erp:logistics_deliver_keep_status:2", "已暂存"),
    LOGISTICS_DELIVER_KEEP_STATUS_C("erp:logistics_deliver_keep_status:3", "已出库"),
    //批次库存日志类型
    SKU_STOCK_BATCH_RECORD_TYPE_A("erp:batch_type:1", "验收入库单"),
    SKU_STOCK_BATCH_RECORD_TYPE_C("erp:batch_type:3", "调拨单"),
    SKU_STOCK_BATCH_RECORD_TYPE_D("erp:batch_type:4", "渠道销售"),
    SKU_STOCK_BATCH_RECORD_TYPE_E("erp:batch_type:5", "KA销售"),
    SKU_STOCK_BATCH_RECORD_TYPE_F("erp:batch_type:6", "零售收款"),
    SKU_STOCK_BATCH_RECORD_TYPE_G("erp:batch_type:7", "顾客退换货"),
    SKU_STOCK_BATCH_RECORD_TYPE_H("erp:batch_type:8", "经销分销折让"),
    SKU_STOCK_BATCH_RECORD_TYPE_I("erp:batch_type:9", "代销分销折让"),
    SKU_STOCK_BATCH_RECORD_TYPE_J("erp:batch_type:10", "库存成本调整"),
    SKU_STOCK_BATCH_RECORD_TYPE_K("erp:batch_type:11", "领用单"),
    SKU_STOCK_BATCH_RECORD_TYPE_L("erp:batch_type:12", "损溢单"),
    SKU_STOCK_BATCH_RECORD_TYPE_M("erp:batch_type:13", "采购退库单"),
    SKU_STOCK_BATCH_RECORD_TYPE_N("erp:batch_type:14", "调拨批次锁定单"),
    SKU_STOCK_BATCH_RECORD_TYPE_O("erp:batch_type:15", "销售成本调整"),
    SKU_STOCK_BATCH_RECORD_TYPE_P("erp:batch_type:16", "营销预售"),
    SKU_STOCK_BATCH_RECORD_TYPE_Q("erp:batch_type:17", "营销预售冲减"),
    SKU_STOCK_BATCH_RECORD_TYPE_R("erp:batch_type:18", "营销销售调出"),
    SKU_STOCK_BATCH_RECORD_TYPE_S("erp:batch_type:19", "营销销售调入"),
    SKU_STOCK_BATCH_RECORD_TYPE_U("erp:batch_type:21", "入库调整销售成本"),
    SKU_STOCK_BATCH_RECORD_TYPE_V("erp:batch_type:22", "入库调整库存成本"),
    SKU_STOCK_BATCH_RECORD_TYPE_W("erp:batch_type:23", "库存补差成本调整"),
    SKU_STOCK_BATCH_RECORD_TYPE_X("erp:batch_type:24", "预售转销"),
    SKU_STOCK_BATCH_RECORD_TYPE_Z("erp:batch_type:26", "礼品发放"),
    SKU_STOCK_BATCH_RECORD_TYPE_AA("erp:batch_type:27", "礼品回收"),
    SKU_STOCK_BATCH_RECORD_TYPE_AC("erp:batch_type:29", "KA调拨"),
    SKU_STOCK_BATCH_RECORD_TYPE_AD("erp:batch_type:30", "加盟订单销售"),
    SKU_STOCK_BATCH_RECORD_TYPE_AE("erp:batch_type:31", "其他成本-入库调整"),
    
    SKU_STOCK_BATCH_RECORD_TYPE_AG("erp:batch_type:33", "销售成本-入库调整费用"),
    SKU_STOCK_BATCH_RECORD_TYPE_AH("erp:batch_type:34", "库存成本-入库调整费用"),
    //入库单收货类型
    STORAGE_INOUT_ENTRY_TYPE_A("erp:storage_inout_entry_type:1", "入库"),
    STORAGE_INOUT_ENTRY_TYPE_B("erp:storage_inout_entry_type:2", "出库"),

    //入库单状态
    STORAGE_INOUT_STATUS_A("erp:storage_inout_status:1", "待入库"),
    STORAGE_INOUT_STATUS_B("erp:storage_inout_status:2", "已入库"),
    STORAGE_INOUT_STATUS_C("erp:storage_inout_status:3", "待出库"),
    STORAGE_INOUT_STATUS_D("erp:storage_inout_status:4", "已出库"),
    STORAGE_INOUT_STATUS_E("erp:storage_inout_status:5", "已作废"),

    //商品类型
    STORAGE_INOUT_INVENTORY_TYPE_A("erp:inventory_type:1", "正品"),
    STORAGE_INOUT_INVENTORY_TYPE_B("erp:inventory_type:2", "样品"),
    STORAGE_INOUT_INVENTORY_TYPE_C("erp:inventory_type:3", "残次品"),

    //采购订单类型
    BILL_ORDER_TYPE_A("erp:bill_order_type:1", "正常机"),
    BILL_ORDER_TYPE_B("erp:bill_order_type:2", "特价机"),
    BILL_ORDER_TYPE_C("erp:bill_order_type:3", "无返利"),

    //类型
    COIN_PROMOTION_DEFINE_TYPE_A("erp:coin_promotion_define_type:1", "电子币"),
    COIN_PROMOTION_DEFINE_TYPE_B("erp:coin_promotion_define_type:2", "促销券"),
    // 券类型
    COIN_PROMOTION_DEFINE_DEFINE_TYPE_A("erp:coin_promotion_define_define_type:1", "普通促销券"),
    COIN_PROMOTION_DEFINE_DEFINE_TYPE_B("erp:coin_promotion_define_define_type:2", "内购促销券"),

    //档期规则状态
    SCHEDULE_RULE_STATUS_UNAUDIT("erp:bill_schedule_rule_status:1", "未审核"),
    SCHEDULE_RULE_STATUS_AUDIT("erp:bill_schedule_rule_status:2", "已审核"),
    SCHEDULE_RULE_STATUS_TERMINATED("erp:bill_schedule_rule_status:3", "已终止"),

    //档期规则类型
    SCHEDULE_RULE_TYPE_SINGLE_RETURN("erp:bill_schedule_rule_type:1", "单品满返"),
    SCHEDULE_RULE_TYPE_SINGLE_REDUCE("erp:bill_schedule_rule_type:2", "单品满减"),
    SCHEDULE_RULE_TYPE_SINGLE_GIFTS("erp:bill_schedule_rule_type:3", "单品满赠"),
    SCHEDULE_RULE_TYPE_ARBITRAGE_RETURN("erp:bill_schedule_rule_type:4", "套购满返"),
    SCHEDULE_RULE_TYPE_ARBITRAGE_REDUCE("erp:bill_schedule_rule_type:5", "套购满减"),
    SCHEDULE_RULE_TYPE_ARBITRAGE_GIFTS("erp:bill_schedule_rule_type:6", "套购满赠"),
    SCHEDULE_RULE_TYPE_FREE("erp:bill_schedule_rule_type:7", "满免活动"),
    SCHEDULE_RULE_TYPE_GUIDE_FREE_COIN("erp:bill_schedule_rule_type:8", "导购赠币"),

    //分摊规则类型
    PRO_SHARE_COIN("erp:coin_promotion_define_type:1", "电子币"),
    PRO_SHARE_COUPON("erp:coin_promotion_define_type:2", "促销券"),

    //分摊规则状态
    PRO_SHARE_STATUS_UNAUDIT("erp:bill_coin_pro_share_status:1", "未审核"),
    PRO_SHARE_STATUS_AUDIT("erp:bill_coin_pro_share_status:2", "已审核"),
    PRO_SHARE_STATUS_STOP("erp:bill_coin_pro_share_status:3", "已终止"),

    // 电子币使用规则
    BILL_COIN_RULE_DEFINE_UN_AUDIT("erp:bill_coin_pro_use_status:1", "未审核"),
    BILL_COIN_RULE_DEFINE_AUDIT("erp:bill_coin_pro_use_status:2", "已审核"),
    BILL_COIN_RULE_DEFINE_STOP("erp:bill_coin_pro_use_status:3", "已终止"),

    //促销券投放规则单
    PROMOTION_PUBLISH_UN_AUDIT("erp:promotion_publish_status:1", "未审核"),
    PROMOTION_PUBLISH_AUDIT("erp:promotion_publish_status:2", "已审核"),
    PROMOTION_PUBLISH_STOP("erp:promotion_publish_status:3", "已终止"),

    //促销券使用状态
    CUSTOMER_ACCOUNT_STATUS_A("erp:customer_account_status:1", "未使用"),
    CUSTOMER_ACCOUNT_STATUS_B("erp:customer_account_status:2", "已锁定"),
    CUSTOMER_ACCOUNT_STATUS_C("erp:customer_account_status:3", "已退回"),
    CUSTOMER_ACCOUNT_STATUS_D("erp:customer_account_status:4", "已使用"),
    CUSTOMER_ACCOUNT_STATUS_E("erp:customer_account_status:5", "转赠中"),
    CUSTOMER_ACCOUNT_STATUS_F("erp:customer_account_status:6", "已转赠"),
    CUSTOMER_ACCOUNT_STATUS_G("erp:customer_account_status:7", "作废"),

    //顾客账户日志操作类型
    CUSTOMER_ACCOUNT_LOG_TYPE_A("erp:customer_account_log_type:1", "领券"),
    CUSTOMER_ACCOUNT_LOG_TYPE_B("erp:customer_account_log_type:2", "消费"),
    CUSTOMER_ACCOUNT_LOG_TYPE_C("erp:customer_account_log_type:3", "退款"),
    CUSTOMER_ACCOUNT_LOG_TYPE_D("erp:customer_account_log_type:4", "转赠"),
    CUSTOMER_ACCOUNT_LOG_TYPE_E("erp:customer_account_log_type:5", "兑换电子币"),
    CUSTOMER_ACCOUNT_LOG_TYPE_F("erp:customer_account_log_type:6", "购物赠送"),
    CUSTOMER_ACCOUNT_LOG_TYPE_G("erp:customer_account_log_type:7", "促销券发放"),
    CUSTOMER_ACCOUNT_LOG_TYPE_I("erp:customer_account_log_type:8", "促销券销售"),
    CUSTOMER_ACCOUNT_LOG_TYPE_J("erp:customer_account_log_type:9", "分享"),
    CUSTOMER_ACCOUNT_LOG_TYPE_K("erp:customer_account_log_type:10", "回冲"),
    CUSTOMER_ACCOUNT_LOG_TYPE_L("erp:customer_account_log_type:11", "储值卡充值"),
    CUSTOMER_ACCOUNT_LOG_TYPE_M("erp:customer_account_log_type:12", "核销"),
    CUSTOMER_ACCOUNT_LOG_TYPE_N("erp:customer_account_log_type:13", "手动增加"),
    CUSTOMER_ACCOUNT_LOG_TYPE_O("erp:customer_account_log_type:14", "手动减少"),
    CUSTOMER_ACCOUNT_LOG_TYPE_P("erp:customer_account_log_type:15", "礼品发放"),
    CUSTOMER_ACCOUNT_LOG_TYPE_Q("erp:customer_account_log_type:16", "礼品回收"),
    CUSTOMER_ACCOUNT_LOG_TYPE_R("erp:customer_account_log_type:17", "修改有效期"),
    CUSTOMER_ACCOUNT_LOG_TYPE_S("erp:customer_account_log_type:18", "注册"),
    CUSTOMER_ACCOUNT_LOG_TYPE_T("erp:customer_account_log_type:19", "签到"),
    CUSTOMER_ACCOUNT_LOG_TYPE_U("erp:customer_account_log_type:20", "订单评价"),
    CUSTOMER_ACCOUNT_LOG_TYPE_V("erp:customer_account_log_type:21", "完善信息"),
    CUSTOMER_ACCOUNT_LOG_TYPE_X("erp:customer_account_log_type:22", "使用单删除"),
    CUSTOMER_ACCOUNT_LOG_TYPE_W("erp:customer_account_log_type:23", "兑换礼品"),
    CUSTOMER_ACCOUNT_LOG_TYPE_Y("erp:customer_account_log_type:24", "兑换礼品回收"),
    CUSTOMER_ACCOUNT_LOG_TYPE_Z("erp:customer_account_log_type:25", "兑换电子券回收"),
    CUSTOMER_ACCOUNT_LOG_TYPE_H("erp:customer_account_log_type:26", "促销券发放回收"),
    CUSTOMER_ACCOUNT_LOG_TYPE_FF("erp:customer_account_log_type:27", "服务券发放"),
    CUSTOMER_ACCOUNT_LOG_TYPE_FH("erp:customer_account_log_type:28", "服务券回收"),
    CUSTOMER_ACCOUNT_LOG_TYPE_AA("erp:customer_account_log_type:29", "多点促销券锁定"),
    CUSTOMER_ACCOUNT_LOG_TYPE_AB("erp:customer_account_log_type:30", "多点促销券解锁"),

    //顾客卡包账户类型
    CUSTOMER_ACCOUNT_TYPE_A("erp:customer_account_type:1", "储值卡"),
    CUSTOMER_ACCOUNT_TYPE_B("erp:customer_account_type:2", "顾客促销券"),
    CUSTOMER_ACCOUNT_TYPE_C("erp:customer_account_type:3", "电子币账号"),
    CUSTOMER_ACCOUNT_TYPE_D("erp:customer_account_type:4", "积分"),
    CUSTOMER_ACCOUNT_TYPE_E("erp:customer_account_type:5", "服务类"),
    CUSTOMER_ACCOUNT_TYPE_F("erp:customer_account_type:6", "延保卡"),
    CUSTOMER_ACCOUNT_TYPE_H("erp:customer_account_type:7", "导购促销券"),

    //顾客账户来源渠道
    CUSTOMER_ACCOUNT_SOURCE_A("erp:customer_account_source_channel:1", "扫码领取"),
    CUSTOMER_ACCOUNT_SOURCE_B("erp:customer_account_source_channel:2", "销售获得"),
    CUSTOMER_ACCOUNT_SOURCE_C("erp:customer_account_source_channel:3", "导购推送"),
    CUSTOMER_ACCOUNT_SOURCE_D("erp:customer_account_source_channel:4", "促销券发放"),
    CUSTOMER_ACCOUNT_SOURCE_E("erp:customer_account_source_channel:5", "促销券销售"),
    CUSTOMER_ACCOUNT_SOURCE_F("erp:customer_account_source_channel:6", "积分兑换"),
    CUSTOMER_ACCOUNT_SOURCE_G("erp:customer_account_source_channel:7", "点击领取"),
    CUSTOMER_ACCOUNT_SOURCE_H("erp:customer_account_source_channel:8", "手动增加"),
    CUSTOMER_ACCOUNT_SOURCE_I("erp:customer_account_source_channel:9", "手动减少"),
    CUSTOMER_ACCOUNT_SOURCE_J("erp:customer_account_source_channel:10", "礼品发放"),
    CUSTOMER_ACCOUNT_SOURCE_K("erp:customer_account_source_channel:11", "礼品回收"),
    CUSTOMER_ACCOUNT_SOURCE_L("erp:customer_account_source_channel:12", "注册"),
    CUSTOMER_ACCOUNT_SOURCE_M("erp:customer_account_source_channel:13", "签到"),
    CUSTOMER_ACCOUNT_SOURCE_N("erp:customer_account_source_channel:14", "订单评价"),
    CUSTOMER_ACCOUNT_SOURCE_O("erp:customer_account_source_channel:15", "完善信息"),
    CUSTOMER_ACCOUNT_SOURCE_P("erp:customer_account_source_channel:16", "用币删除"),

    CUSTOMER_ACCOUNT_SOURCE_W("erp:customer_account_source_channel:23", "以旧换新推送"),
    //促销券发放类型
    PROMOTION_PUBLISH_GRANT_TYPE_A("erp:promotion_publish_grant_type:1", "顾客"),
    PROMOTION_PUBLISH_GRANT_TYPE_B("erp:promotion_publish_grant_type:2", "内部员工"),

    //促销券发放状态
    PROMOTION_PUBLISH_GRANT_STATUS_A("erp:promotion_publish_grant_status:1", "待发放"),
    PROMOTION_PUBLISH_GRANT_STATUS_B("erp:promotion_publish_grant_status:2", "已发放"),
    PROMOTION_PUBLISH_GRANT_STATUS_C("erp:promotion_publish_grant_status:3", "待回收"),
    PROMOTION_PUBLISH_GRANT_STATUS_D("erp:promotion_publish_grant_status:4", "已回收"),

    //顾客卡包订单状态
    CUSTOMER_PROMOTION_LOG_ORDER_TYPE_A("erp:customer_promotion_log_order_type:1", "零售"),
    CUSTOMER_PROMOTION_LOG_ORDER_TYPE_B("erp:customer_promotion_log_order_type:2", "促销券发放"),
    CUSTOMER_PROMOTION_LOG_ORDER_TYPE_C("erp:customer_promotion_log_order_type:3", "促销券销售"),

    //促销券销售状态
    PROMOTION_SALE_STATUS_PAY("erp:bill_promotion_sale_status:1", "未完成"),
    PROMOTION_SALE_STATUS_PAID("erp:bill_promotion_sale_status:2", "已完成"),
    PROMOTION_SALE_STATUS_REFUNDING("erp:bill_promotion_sale_status:3", "退款中"),
    PROMOTION_SALE_STATUS_REFUNDED("erp:bill_promotion_sale_status:4", "已退款"),
    PROMOTION_SALE_STATUS_USE("erp:bill_promotion_sale_status:5", "已使用"),


    //支付记录
    PAY_RECORD_STATUS_FINISH("erp:shopping_pay_record_status:1", "完成"),
    PAY_RECORD_STATUS_UNFINISH("erp:shopping_pay_record_status:2", "未完成"),
    PAY_RECORD_STATUS_REFUND("erp:shopping_pay_record_status:3", "退款"),
    PAY_RECORD_STATUS_FAIL("erp:shopping_pay_record_status:4", "支付失败"),
//    PAY_RECORD_STATUS_FINISH("erp:shopping_pay_record_status:1", "支付成功"),
//    PAY_RECORD_STATUS_UNFINISH("erp:shopping_pay_record_status:2", "失败"),
//    PAY_RECORD_STATUS_REFUND("erp:shopping_pay_record_status:3", "退款成功"),
//    PAY_RECORD_STATUS_FAIL("erp:shopping_pay_record_status:4", "失败"),

    //优惠券单据审核状态
    COUPON_DEFINE_UN_AUDIT("erp:coupon_define_status:1", "未审核"),
    COUPON_DEFINE_AUDIT("erp:coupon_define_status:2", "已审核"),
    COUPON_DEFINE_STOP("erp:coupon_define_status:3", "已终止"),

    //优惠券定义类型
    COUPON_DEFINE_TYPE_A("erp:coupon_define_type:3", "A账户"),
    COUPON_DEFINE_TYPE_B("erp:coupon_define_type:4", "B账户"),
    COUPON_DEFINE_TYPE_C("erp:coupon_define_type:1", "厂家券"),
    COUPON_DEFINE_TYPE_D("erp:coupon_define_type:2", "商场券"),
    COUPON_DEFINE_TYPE_E("erp:coupon_define_type:5", "B账户商品设置"),

    //限量限价审核状态
    SCHEDULE_LIMIT_UN_AUDIT("erp:bill_schedule_limit_status:1", "未审核"),
    SCHEDULE_LIMIT_AUDIT("erp:bill_schedule_limit_status:2", "已审核"),
    SCHEDULE_LIMIT_STOP("erp:bill_schedule_limit_status:3", "已终止"),

    //延保服务审核状态
    ACPP_UN_AUDIT("erp:bill_acpp_status:1", "未审核"),
    ACPP_AUDIT("erp:bill_acpp_status:2", "已审核"),
    ACPP_STOP("erp:bill_acpp_status:3", "已终止"),

    //礼品规则单状态
    GIFT_DEFINE_STATUS_UNAUDIT("erp:bill_gift_define_status:1", "未审核"),
    GIFT_DEFINE_STATUS_AUDIT("erp:bill_gift_define_status:2", "已审核"),
    GIFT_DEFINE_STATUS_STOP("erp:bill_gift_define_status:3", "已终止"),

    //礼品类型
    MANUAL_GIFT_RECORD_GIFT_TYPE_A("erp:manual_gift_record_gift_type:1", "电子币"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_B("erp:manual_gift_record_gift_type:2", "自营商品"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_C("erp:manual_gift_record_gift_type:3", "联营商品"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_E("erp:manual_gift_record_gift_type:4", "礼品"),
    MANUAL_GIFT_RECORD_GIFT_TYPE_D("erp:manual_gift_record_gift_type:5", "抖音券"),

    //销售单礼品是否允许多次发放参数启停状态
    MANUAL_GIFT_IS_MULTIPLE_TIMES_ON("erp:gift_is_multiple_times:1", "启用"),
    MANUAL_GIFT_IS_MULTIPLE_TIMES_OFF("erp:gift_is_multiple_times:0", "停用"),

    //商品服务配置
    SERVICE_STATUS_UNAUDIT("erp:bill_schedule_service_status:1", "未审核"),
    SERVICE_STATUS_AUDIT("erp:bill_schedule_service_status:2", "已审核"),
    SERVICE_STATUS_STOP("erp:bill_schedule_service_status:3", "已终止"),

    //商品服务配置
    OLDFORNEW_STATUS_A("erp:bill_oldfornew_status:1", "未审核"),
    OLDFORNEW_STATUS_B("erp:bill_oldfornew_status:2", "已审核"),
    OLDFORNEW_STATUS_C("erp:bill_oldfornew_status:3", "已终止"),

    //支付记录类型 1零售2欠款3回冲4代收代付5促销券销售6储值卡销售7零售退货8欠款收款9以物抵物
    PAY_RECORE_TYPE_A("shopping_pay_record_type:1", "零售"),
    PAY_RECORE_TYPE_B("shopping_pay_record_type:2", "欠款"),
    PAY_RECORE_TYPE_C("shopping_pay_record_type:3", "回冲"),
    PAY_RECORE_TYPE_D("shopping_pay_record_type:4", "代收代付"),
    PAY_RECORE_TYPE_E("shopping_pay_record_type:5", "促销券销售"),
    PAY_RECORE_TYPE_F("shopping_pay_record_type:6", "储值卡销售"),
    PAY_RECORE_TYPE__LING_SHOU_TUI_KUAN("shopping_pay_record_type:7", "零售退货"),
    PAY_RECORE_TYPE__QIAN_KUAN_SHOU_KUAN("shopping_pay_record_type:8", "欠款收款"),
    PAY_RECORE_TYPE__YI_WU_DI_WU("shopping_pay_record_type:9", "以物抵物"),

    //销售订单状态
    SHOPPING_ORDER_STATUS_A("erp:shopping_order_status:1", "待结算"),
    SHOPPING_ORDER_STATUS_B("erp:shopping_order_status:2", "支付中"),
    SHOPPING_ORDER_STATUS_C("erp:shopping_order_status:3", "已支付"),
    SHOPPING_ORDER_STATUS_D("erp:shopping_order_status:4", "已退货"),
    SHOPPING_ORDER_STATUS_E("erp:shopping_order_status:5", "退款中"),
    SHOPPING_ORDER_STATUS_F("erp:shopping_order_status:6", "已退款"),
    SHOPPING_ORDER_STATUS_G("erp:shopping_order_status:7", "已转销"),
    SHOPPING_ORDER_STATUS_H("erp:shopping_order_status:8", "待退款"),
    SHOPPING_ORDER_STATUS_I("erp:shopping_order_status:9", "未审核"),
    SHOPPING_ORDER_STATUS_J("erp:shopping_order_status:10", "已审核"),
    SHOPPING_ORDER_STATUS_K("erp:shopping_order_status:11", "已作废"),
    
    //特价申请单状态
    SHOPPING_ORDER_SPECIAL_STATUS__DIAN_ZHANG_SHEN_PI_ZHONG("erp:shopping_order_special_status:1","店长审批中"),
    SHOPPING_ORDER_SPECIAL_STATUS__CAI_GOU_SHEN_PI_ZHONG("erp:shopping_order_special_status:2","采购审批中"),
    SHOPPING_ORDER_SPECIAL_STATUS__TONG_YI("erp:shopping_order_special_status:3","同意"),
    SHOPPING_ORDER_SPECIAL_STATUS__YI_JU_JUE("erp:shopping_order_special_status:4","已拒绝"),
    SHOPPING_ORDER_SPECIAL_STATUS__KE_FU_SHEN_PI_ZHONG("erp:shopping_order_special_status:5","客服审批中"),

    //销售订单类型
    SHOPPING_ORDER_TYPE_A("erp:shopping_order_type:1", "销售单"),
    SHOPPING_ORDER_TYPE_B("erp:shopping_order_type:2", "订金单"),
    SHOPPING_ORDER_TYPE_C("erp:shopping_order_type:3", "预售单"),
    SHOPPING_ORDER_TYPE_D("erp:shopping_order_type:4", "退货单"),
    SHOPPING_ORDER_TYPE_E("erp:shopping_order_type:5", "换货单"),
    SHOPPING_ORDER_TYPE_F("erp:shopping_order_type:6", "代收"),
    SHOPPING_ORDER_TYPE_G("erp:shopping_order_type:7", "代付"),
    SHOPPING_ORDER_TYPE_H("erp:shopping_order_type:8", "营销回冲"),
    SHOPPING_ORDER_TYPE_J("erp:shopping_order_type:9", "普通退货单"),
    //虚拟订单类型，支付区分使用
    SHOPPING_ORDER_TYPE_I("erp:shopping_order_type:10", "欠款还款"),
    SHOPPING_ORDER_TYPE_K("erp:shopping_order_type:11", "礼品销售单"),
    SHOPPING_ORDER_TYPE_L("erp:shopping_order_type:12", "礼品退货单"),

    CHANNEL_SALE("erp:channel_sale:1", "20渠道销售"),

    //操作渠道
    OPER_CHANNEL_A("erp:oper_channel:1", "PC"),
    OPER_CHANNEL_B("erp:oper_channel:2", "内部小程序"),
    OPER_CHANNEL_C("erp:oper_channel:3", "小贝仓储APP"),
    OPER_CHANNEL_D("erp:oper_channel:4", "京东"),

    //促销活动类型
    SHOPPING_ORDER_PROMOTION_A("erp:shopping_order_promotion_type:1", "单品满返"),
    SHOPPING_ORDER_PROMOTION_B("erp:shopping_order_promotion_type:2", "单品满减"),
    SHOPPING_ORDER_PROMOTION_C("erp:shopping_order_promotion_type:3", "单品满赠"),
    SHOPPING_ORDER_PROMOTION_D("erp:shopping_order_promotion_type:4", "套购满返"),
    SHOPPING_ORDER_PROMOTION_E("erp:shopping_order_promotion_type:5", "套购满减"),
    SHOPPING_ORDER_PROMOTION_F("erp:shopping_order_promotion_type:6", "套购满赠"),
    SHOPPING_ORDER_PROMOTION_G("erp:shopping_order_promotion_type:7", "满免活动"),
    SHOPPING_ORDER_PROMOTION_H("erp:shopping_order_promotion_type:8", "促销券使用"),
    SHOPPING_ORDER_PROMOTION_I("erp:shopping_order_promotion_type:9", "服务券使用规则"),
    SHOPPING_ORDER_PROMOTION_J("erp:shopping_order_promotion_type:10", "电子币使用"),
    SHOPPING_ORDER_PROMOTION_K("erp:shopping_order_promotion_type:11", "商场券"),
    SHOPPING_ORDER_PROMOTION_L("erp:shopping_order_promotion_type:12", "厂家券"),
    SHOPPING_ORDER_PROMOTION_M("erp:shopping_order_promotion_type:13", "A账号"),
    SHOPPING_ORDER_PROMOTION_N("erp:shopping_order_promotion_type:14", "B账户"),
    SHOPPING_ORDER_PROMOTION_O("erp:shopping_order_promotion_type:15", "限量限价"),
    SHOPPING_ORDER_PROMOTION_P("erp:shopping_order_promotion_type:16", "延保"),
    SHOPPING_ORDER_PROMOTION_Q("erp:shopping_order_promotion_type:17", "赠品发放"),
    SHOPPING_ORDER_PROMOTION_R("erp:shopping_order_promotion_type:18", "积分"),
    SHOPPING_ORDER_PROMOTION_X("erp:shopping_order_promotion_type:19", "支付手续费"),
    SHOPPING_ORDER_PROMOTION_S("erp:shopping_order_promotion_type:20", "礼品发放"),
    SHOPPING_ORDER_PROMOTION_T("erp:shopping_order_promotion_type:21", "以旧换新"),
    SHOPPING_ORDER_PROMOTION_U("erp:shopping_order_promotion_type:22", "抽奖"),
    SHOPPING_ORDER_PROMOTION_V("erp:shopping_order_promotion_type:23", "捆绑"),
    SHOPPING_ORDER_PROMOTION_W("erp:shopping_order_promotion_type:24", "导购赠电子币"),
    SHOPPING_ORDER_PROMOTION_Y("erp:shopping_order_promotion_type:25", "销售补差"),
    SHOPPING_ORDER_PROMOTION_Z("erp:shopping_order_promotion_type:26", "导购促销券"),
    SHOPPING_ORDER_PROMOTION_AA("erp:shopping_order_promotion_type:27", "内购券"),
    SHOPPING_ORDER_PROMOTION_AB("erp:shopping_order_promotion_type:28", "红卡"),
    SHOPPING_ORDER_PROMOTION_AC("erp:shopping_order_promotion_type:29", "黄卡"),
    SHOPPING_ORDER_PROMOTION_AD("erp:shopping_order_promotion_type:30", "蓝卡"),
    SHOPPING_ORDER_PROMOTION_AE("erp:shopping_order_promotion_type:31", "绿卡"),
    SHOPPING_ORDER_PROMOTION_AG("erp:shopping_order_promotion_type:33", "成长值"),
    //订单商品类型
    SHOPPING_SKU_TYPE_MAIN("erp:shopping_order_sku_sku_type:1", "主商品"),
    SHOPPING_SKU_TYPE_GROUP("erp:shopping_order_sku_sku_type:2", "捆绑商品"),
    SHOPPING_SKU_TYPE_C("erp:shopping_order_sku_sku_type:3 ", "单品活动赠品"),
    SHOPPING_SKU_TYPE_D("erp:shopping_order_sku_sku_type:4", "套购活动赠品"),
    SHOPPING_SKU_TYPE_E("erp:shopping_order_sku_sku_type:5", "服务"),
    SHOPPING_SKU_TYPE_F("erp:shopping_order_sku_sku_type:6", "代收代付"),
    SHOPPING_SKU_TYPE_G("erp:shopping_order_sku_sku_type:7", "电子币"),
    SHOPPING_SKU_TYPE_H("erp:shopping_order_sku_sku_type:8", "延保"),
    SHOPPING_SKU_TYPE_I("erp:shopping_order_sku_sku_type:9", "单品赠币"),
    SHOPPING_SKU_TYPE_J("erp:shopping_order_sku_sku_type:10", "套购赠币"),
    SHOPPING_SKU_TYPE_K("erp:shopping_order_sku_sku_type:11", "礼品商品"),
    SHOPPING_SKU_TYPE_L("erp:shopping_order_sku_sku_type:12", "礼品电子币"),
    //出库方式
    SHOPPING_OUT_TYPE_SPOT("erp:shopping_order_out_type:1", "现场提货"),
    SHOPPING_OUT_TYPE_STORE("erp:shopping_order_out_type:2", "集中配送"),
    SHOPPING_OUT_TYPE_FAC("erp:shopping_order_out_type:3", "厂家配送"),

    //经营方式
    PROVIDER_CONTRACT_BUSINESS_TYPE_A("erp:provider_contract_business_type:1", "自营"),
    PROVIDER_CONTRACT_BUSINESS_TYPE_B("erp:provider_contract_business_type:2", "联营"),

    //机构状态
    STORE_STATUS_NORMAL("erp:store_status:1", "正常"),
    //机构类型
    STORE_TYPE_A("erp:store_type:1", "分子公司"),
    STORE_TYPE_B("erp:store_type:2", "配送中心"),
    STORE_TYPE_C("erp:store_type:3", "销售部门"),
    STORE_TYPE_D("erp:store_type:4", "售后维修"),
    STORE_TYPE_E("erp:store_type:5", "职能部门"),
    STORE_TYPE_F("erp:store_type:6", "综合机构"),
    //sku状态
    SKU_STATUS_NORMAL("erp:base_sku_status:2", "正常流转"),

    //供应商状态
    PROVIDER_STATUS_NORAML("erp:base_provider_status:1", "正常"),

    //捆绑商品审核状态
    GROUP_DEFINE_UN_AUDIT("erp:bill_group_define_status:1", "未审核"),
    GROUP_DEFINE_AUDIT("erp:bill_group_define_status:2", "已审核"),
    GROUP_DEFINE_STOP("erp:bill_group_define_status:3", "已终止"),

    //
    CARD_RULE_TYPE_A("erp:card_rule_type:1", "无"),
    CARD_RULE_TYPE_B("erp:card_rule_type:2", "赠送额度"),
    CARD_RULE_TYPE_C("erp:card_rule_type:3", "折扣"),

    CARD_RULE_STATUS_A("erp:card_rule_status:1", "未储值"),
    CARD_RULE_STATUS_B("erp:card_rule_status:2", "已储值"),

    // 卡领用状态
    CARD_DISTRIBUTE_STATUS_A("erp:card_distribute_status:1", "未审核"),
    CARD_DISTRIBUTE_STATUS_B("erp:card_distribute_status:2", "已审核"),

    //临时送货单
    SHOPPING_DELIVER_STATUS_A("erp:shopping_deliver_status:1", "未审核"),
    SHOPPING_DELIVER_STATUS_B("erp:shopping_deliver_status:2", "已审核"),

    //代管库存单状态
    SHOPPING_THIRD_DELIVER_STATUS_A("erp:shopping_third_deliver_status:1", "未审核"),
    SHOPPING_THIRD_DELIVER_STATUS_B("erp:shopping_third_deliver_status:2", "已审核"),

    // 代管库存单类型
    SHOPPING_THIRD_DELIVER_TYPE_A("erp:shopping_third_deliver_type:1", "送货单"),
    SHOPPING_THIRD_DELIVER_TYPE_B("erp:shopping_third_deliver_type:2", "退货单"),

    // 储值卡状态
    CARD_DETAIL_STATUS_A("erp:card_detail_status:1", "未制卡"),
    CARD_DETAIL_STATUS_B("erp:card_detail_status:2", "已制卡"),
    CARD_DETAIL_STATUS_C("erp:card_detail_status:3", "已销售"),
    CARD_DETAIL_STATUS_D("erp:card_detail_status:4", "已使用"),
    CARD_DETAIL_STATUS_E("erp:card_detail_status:5", "已领用"),


    // 储值卡销售状态
    CARD_SALE_STATUS_A("erp:card_sale_status:1", "未完成"),
    CARD_SALE_STATUS_B("erp:card_sale_status:2", "已完成"),
    CARD_SALE_STATUS_C("erp:card_sale_status:3", "已退款"),
    CARD_SALE_STATUS_D("erp:card_sale_status:4", "支付中"),
    CARD_SALE_STATUS_E("erp:card_sale_status:5", "退款中"),

    // 储值卡日志类型
    CARD_LOG_OPRATION_A("erp:card_log_operation_type:1", "卡类型定义"),
    CARD_LOG_OPRATION_B("erp:card_log_operation_type:2", "卡制作"),
    CARD_LOG_OPRATION_C("erp:card_log_operation_type:3", "卡领用"),
    CARD_LOG_OPRATION_D("erp:card_log_operation_type:4", "卡销售"),
    CARD_LOG_OPRATION_E("erp:card_log_operation_type:5", "消费"),
    CARD_LOG_OPRATION_F("erp:card_log_operation_type:6", "冻结"),
    CARD_LOG_OPRATION_G("erp:card_log_operation_type:7", "解冻"),
    CARD_LOG_OPRATION_H("erp:card_log_operation_type:8", "重写"),
    CARD_LOG_OPRATION_I("erp:card_log_operation_type:9", "实名"),
    CARD_LOG_OPRATION_J("erp:card_log_operation_type:10", "解绑"),
    CARD_LOG_OPRATION_K("erp:card_log_operation_type:11", "卡入库"),
    CARD_LOG_OPRATION_L("erp:card_log_operation_type:12", "卡充值"),
    CARD_LOG_OPRATION_M("erp:card_log_operation_type:13", "启用"),
    CARD_LOG_OPRATION_N("erp:card_log_operation_type:14", "停用"),
    CARD_LOG_OPRATION_O("erp:card_log_operation_type:15", "延期"),

    //监控单类型
    BILL_PROVIDER_FEE_TYPE_A("erp:bill_provider_fee_type:1", "销售实时分摊"),
    BILL_PROVIDER_FEE_TYPE_B("erp:bill_provider_fee_type:2", "营销手工分摊"),
    BILL_PROVIDER_FEE_TYPE_C("erp:bill_provider_fee_type:3", "合同自动产生"),
    BILL_PROVIDER_FEE_TYPE_D("erp:bill_provider_fee_type:4", "临时手工录入"),
    BILL_PROVIDER_FEE_TYPE_E("erp:bill_provider_fee_type:5", "临时手工按销售分摊"),
    BILL_PROVIDER_FEE_TYPE_F("erp:bill_provider_fee_type:6", "联营手工录入"),
    //顾客激励类型
    BILL_SCHEDULE_AWARD_TYPE_A("erp:bill_schedule_award_type:1", "签到"),
    BILL_SCHEDULE_AWARD_TYPE_B("erp:bill_schedule_award_type:2", "连续签到"),
    BILL_SCHEDULE_AWARD_TYPE_C("erp:bill_schedule_award_type:3", "文字评价"),
    BILL_SCHEDULE_AWARD_TYPE_D("erp:bill_schedule_award_type:4", "图片评价"),
    BILL_SCHEDULE_AWARD_TYPE_E("erp:bill_schedule_award_type:5", "完善信息"),
    BILL_SCHEDULE_AWARD_TYPE_F("erp:bill_schedule_award_type:6", "新用户注册"),
    BILL_SCHEDULE_AWARD_TYPE_G("erp:bill_schedule_award_type:7", "投诉建议"),

    //顾客激励赠送类型
    BILL_SCHEDULE_AWARD_GIVE_TYPE_A("erp:bill_schedule_award_give_type:1", "积分"),
    BILL_SCHEDULE_AWARD_GIVE_TYPE_B("erp:bill_schedule_award_give_type:2", "电子币"),
    BILL_SCHEDULE_AWARD_GIVE_TYPE_C("erp:bill_schedule_award_give_type:3", "促销券"),
    
    // 规则单类型
    BILL_SCHEDULE_RULE_TYPE__DAN_PIN_MAN_FAN("erp:bill_schedule_rule_type:1","单品满返"),
    BILL_SCHEDULE_RULE_TYPE__DAN_PIN_MAN_JIAN("erp:bill_schedule_rule_type:2","单品满减"),
    BILL_SCHEDULE_RULE_TYPE__DAN_PIN_MAN_ZENG("erp:bill_schedule_rule_type:3","单品满赠"),
    BILL_SCHEDULE_RULE_TYPE__TAO_GOU_MAN_FAN("erp:bill_schedule_rule_type:4","套购满返"),
    BILL_SCHEDULE_RULE_TYPE__TAO_GOU_MAN_JIAN("erp:bill_schedule_rule_type:5","套购满减"),
    BILL_SCHEDULE_RULE_TYPE__TAO_GOU_MAN_ZENG("erp:bill_schedule_rule_type:6","套购满赠"),
    BILL_SCHEDULE_RULE_TYPE__MAN_MIAN_HUO_DONG("erp:bill_schedule_rule_type:7","满免活动"),
    BILL_SCHEDULE_RULE_TYPE__DAO_GOU_ZENG_BI("erp:bill_schedule_rule_type:8","导购赠币"),
    
    CUSTOMER_STATUS__ZHENG_CHANG("erp:customer_status:1", "正常"),
    CUSTOMER_STATUS__DONG_JIE("erp:customer_status:2", "冻结"),

    //编码规则key
    CLASS_CODE_TYPE("erp:code_rule:2", "品类编码"),
    BILL_CODE_TYPE("erp:code_rule:1", "单据编码"),
    CARD_LIMIT_NUM("erp:card_limit_num:1", "储值卡入库限量"),
    //抽奖审核状态
    BILL_LOTTERY_STATUS_A("erp:bill_lottery_status:1", "未审核"),
    BILL_LOTTERY_STATUS_B("erp:bill_lottery_status:2", "已审核"),
    BILL_LOTTERY_STATUS_C("erp:bill_lottery_status:3", "已终止"),

    LOTTERY_NUMBER_STATUS_A("erp:lottery_number_status:1", "未抽奖"),
    LOTTERY_NUMBER_STATUS_B("erp:lottery_number_status:2", "未中奖"),
    LOTTERY_NUMBER_STATUS_C("erp:lottery_number_status:3", "已中奖"),
    LOTTERY_NUMBER_STATUS_D("erp:lottery_number_status:4", "已作废"),

    // 赠品类型
	SCHEDULE_GIFT_RECORD_TYPE__FA_FANG("erp:schedule_gift_record_type:1", "发放"),
	SCHEDULE_GIFT_RECORD_TYPE__HUI_SHOU("erp:schedule_gift_record_type:2", "回收"),
	SCHEDULE_GIFT_RECORD_TYPE__WEI_FA_FANG("erp:schedule_gift_record_type:3", "待发放"),
	
    CHANNEL_ORDER_STATUS_A("erp:channel_order_status:1", "未审核"),
    CHANNEL_ORDER_STATUS_B("erp:channel_order_status:2", "待开单"),
    CHANNEL_ORDER_STATUS_C("erp:channel_order_status:3", "部分开单"),
    CHANNEL_ORDER_STATUS_D("erp:channel_order_status:4", "全部开单"),
    CHANNEL_ORDER_STATUS_E("erp:channel_order_status:5", "已终止"),
    CHANNEL_ORDER_PAY_STATUS_A("erp:channel_order_pay_status:1", "未付款"),
    CHANNEL_ORDER_PAY_STATUS_B("erp:channel_order_pay_status:2", "已付款"),
    //渠道政策状态
    CHANNEL_POLICY_STATUS_A("erp:channel_policy_status:1", "未审核"),
    CHANNEL_POLICY_STATUS_B("erp:channel_policy_status:2", "已审核"),
    CHANNEL_POLICY_STATUS_C("erp:channel_policy_status:3", "已作废"),
    //渠道确认单政策
    CHANNEL_POLICY_CONFIRM_STATUS_A("erp:channel_policy_confirm_status:1", "未审核"),
    CHANNEL_POLICY_CONFIRM_STATUS_B("erp:channel_policy_confirm_status:2", "已审核"),
    CHANNEL_POLICY_CONFIRM_STATUS_C("erp:channel_policy_confirm_status:3", "已作废"),
    //渠道政策类型
    CHANNEL_POLICY_TYPE_A("erp:policy_type:1", "特价政策"),
    CHANNEL_POLICY_TYPE_B("erp:policy_type:2", "限时抢购"),
    CHANNEL_POLICY_TYPE_C("erp:policy_type:3", "规模奖励"),
    CHANNEL_POLICY_TYPE_D("erp:policy_type:4", "常规机政策"),
    CHANNEL_POLICY_TYPE_E("erp:policy_type:5", "提货台阶"),
    CHANNEL_POLICY_TYPE_F("erp:policy_type:6", "出样政策"),
    CHANNEL_POLICY_TYPE_G("erp:policy_type:7", "众筹台返"),
    CHANNEL_POLICY_TYPE_H("erp:policy_type:8", "台阶直降"),
    CHANNEL_POLICY_TYPE_I("erp:policy_type:9", "特价申请"),
    CHANNEL_POLICY_TYPE_J("erp:policy_type:10", "无"),

    //订单来源
    CHANNEL_ORDER_SOURCE_TYPE_A("erp:channel_order_source_type:1", "渠道手工"),
    CHANNEL_ORDER_SOURCE_TYPE_B("erp:channel_order_source_type:2", "加盟订单"),
    //开单来源
    CHANNEL_SALE_SOURCE_TYPE_A("erp:channel_sale_source_type:1", "渠道手工"),
    CHANNEL_SALE_SOURCE_TYPE_B("erp:channel_sale_source_type:2", "加盟订单"),
    CHANNEL_SALE_SOURCE_TYPE_C("erp:channel_sale_source_type:3", "特价申请"),
    CHANNEL_SALE_SOURCE_TYPE_D("erp:channel_sale_source_type:4", "手工开单"),
    CHANNEL_SALE_SALE_SOURCE_A("erp:channel_sale_sale_source:1","零售"),
    CHANNEL_SALE_SALE_SOURCE_B("erp:channel_sale_sale_source:2","团购"),

    //开单状态
    CHANNEL_SALE_STATUS_A("erp:channel_sale_status:1", "未审核"),
    CHANNEL_SALE_STATUS_B("erp:channel_sale_status:2", "已审核"),
    //渠道属性
    STORE_CHANNEL_ATTRIBUTE_A("erp:store_attribute:1", "经销商"),
    STORE_CHANNEL_ATTRIBUTE_B("erp:store_attribute:2", "加盟店"),
    STORE_CHANNEL_ATTRIBUTE_C("erp:store_attribute:3", "KA卖场"),

    //结算方式
    PROVIDER_CONTRACT_SKU_SUM_MODE_A("erp:provider_contract_sku_sum_mode:1", "进价"),
    PROVIDER_CONTRACT_SKU_SUM_MODE_B("erp:provider_contract_sku_sum_mode:2", "扣率"),

    //延保类型
	BILL_ACPP_DETAIL_ACPP_TYPE_ONE_YEAR("erp:bill_acpp_detail_acpp_type:1","一年延保"),
	BILL_ACPP_DETAIL_ACPP_TYPE_TWOONE_YEAR("erp:bill_acpp_detail_acpp_type:2","两年延保"),
	BILL_ACPP_DETAIL_ACPP_TYPE_THREE_YEAR("erp:bill_acpp_detail_acpp_type:3","三年延保"),

    //特价申请状态
    CHANNEL_SPECIAL_PRICE_STATUS_A("erp:channel_special_price_apply_status:1", "未审核"),
    CHANNEL_SPECIAL_PRICE_STATUS_B("erp:channel_special_price_apply_status:2", "待开单"),
    CHANNEL_SPECIAL_PRICE_STATUS_C("erp:channel_special_price_apply_status:3", "全部开单"),
    CHANNEL_SPECIAL_PRICE_STATUS_D("erp:channel_special_price_apply_status:4", "已终止"),

    // 串号标签
    SKU_STOCK_TANDEM_TAG_A("erp:sku_stock_tandem_tag:1", "在库"),
    SKU_STOCK_TANDEM_TAG_B("erp:sku_stock_tandem_tag:2", "已销"),
    SKU_STOCK_TANDEM_TAG_C("erp:sku_stock_tandem_tag:3", "已退库"),
    SKU_STOCK_TANDEM_TAG_D("erp:sku_stock_tandem_tag:4", "调拨在途"),
    SKU_STOCK_TANDEM_TAG_E("erp:sku_stock_tandem_tag:5", "领用"),

    SHOPPING_ORDER_ACPP_TYPE_A("erp:shopping_order_acpp_type:1", "绑定"),
    SHOPPING_ORDER_ACPP_TYPE_B("erp:shopping_order_acpp_type:2", "取消绑定"),

    STORE_BUSINESS_TYPE_A("erp:store_business_type:1", "机构信息"),
    STORE_BUSINESS_TYPE_B("erp:store_business_type:2", "渠道信息"),

    BILL_APPROPRIATION_APPROVAL_STATUS_A("erp:bill_appropriation_approval_status:1", "未审批"),
    BILL_APPROPRIATION_APPROVAL_STATUS_B("erp:bill_appropriation_approval_status:2", "审批中"),
    BILL_APPROPRIATION_APPROVAL_STATUS_C("erp:bill_appropriation_approval_status:3", "审批完成"),
    BILL_APPROPRIATION_APPROVAL_STATUS_D("erp:bill_appropriation_approval_status:4", "审批未通过"),

    /**
     * 新百小程序发放促销券支付方式
     */
    PROMOTION_PUBLISH_GRANT_PAY_TYPE_A("erp:promotion_publish_grant_pay_type:1", "现金"),
    PROMOTION_PUBLISH_GRANT_PAY_TYPE_B("erp:promotion_publish_grant_pay_type:2", "微信"),

    CUSTOMER_INTEGRAL_RECORD_TYPE_A("erp:customer_integral_record_type:1", "销售获取"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_B("erp:customer_integral_record_type:2", "手动增加"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_C("erp:customer_integral_record_type:3", "手动减少"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_D("erp:customer_integral_record_type:4", "注册"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_E("erp:customer_integral_record_type:5", "签到"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_F("erp:customer_integral_record_type:6", "订单评价"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_G("erp:customer_integral_record_type:7", "完善信息"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_I("erp:customer_integral_record_type:8", "积分兑换电子币"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_J("erp:customer_integral_record_type:9", "积分兑换电子币回收"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_K("erp:customer_integral_record_type:10", "送积分订单退货"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_M("erp:customer_integral_record_type:11", "欠条"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_N("erp:customer_integral_record_type:12", "还欠条"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_O("erp:customer_integral_record_type:13", "用于还欠条"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_P("erp:customer_integral_record_type:14", "积分兑换礼品"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_Q("erp:customer_integral_record_type:15", "积分兑换礼品回收"),
    CUSTOMER_INTEGRAL_RECORD_TYPE_ZZ("erp:customer_integral_record_type:99", "过期"),
    /**
     * 新百服务券代发渠道
     */
    DISTRIBUTION_CHANNEL_A("erp:distribution_channel:1", "多点超市"),
    DISTRIBUTION_CHANNEL_B("erp:distribution_channel:2", "多点百货"),

    /**
     * 顾客成长值类型
     */
    CUSTOMER_GROWTH_TYPE_A("erp:customer_growth_type:1", "购物赠送"),
    CUSTOMER_GROWTH_TYPE_B("erp:customer_growth_type:2", "退款"),
    CUSTOMER_GROWTH_TYPE_C("erp:customer_growth_type:3", "过期"),
    CUSTOMER_GROWTH_TYPE_D("erp:customer_growth_type:4", "多点同步"),
	
	/**
	 * 异业券发放规则单状态
	 */
    VONDER_COUPON_STATUS__WEI_SHEN_HE("erp:vonder_coupon_status:1", "未审核"),
    VONDER_COUPON_STATUS__YI_SHEN_HE("erp:vonder_coupon_status:2", "已审核"),
    VONDER_COUPON_STATUS__YI_ZHONG_ZHI("erp:vonder_coupon_status:3", "已终止"),

    /**
     * 以旧换新单状态
     */
    OLDFORNEW_SKU_DETAIL_STATUS_A("erp:oldfornew_sku_detail_status:1", "待审核"),
    OLDFORNEW_SKU_DETAIL_STATUS_B("erp:oldfornew_sku_detail_status:2", "待确认"),
    OLDFORNEW_SKU_DETAIL_STATUS_C("erp:oldfornew_sku_detail_status:3", "已成交"),
    OLDFORNEW_SKU_DETAIL_STATUS_D("erp:oldfornew_sku_detail_status:4", "已作废"),

    /**
     * 旧机类型
     */
    OLD_GOODS_TYPE_A("erp:old_goods_type:1", "旧家电"),
    OLD_GOODS_TYPE_B("erp:old_goods_type:2", "旧手机"),
    /**
     * 旧机管理单状态
     */
    OLDFORNEW_SKU_INOUT_STATUS_A("erp:oldfornew_sku_inout_status:1", "待入库"),
    OLDFORNEW_SKU_INOUT_STATUS_B("erp:oldfornew_sku_inout_status:2", "已入库"),
    OLDFORNEW_SKU_INOUT_STATUS_C("erp:oldfornew_sku_inout_status:3", "已出库"),
    OLDFORNEW_SKU_INOUT_STATUS_D("erp:oldfornew_sku_inout_status:4", "调拨待确认"),
    OLDFORNEW_SKU_INOUT_STATUS_E("erp:oldfornew_sku_inout_status:5", "调拨待入库"),
    OLDFORNEW_SKU_INOUT_STATUS_F("erp:oldfornew_sku_inout_status:6", "调拨已入库"),

    /**
     * 以旧换新单推送规则单状态
     */
    OLDFORNEW_SCHEDULE_STATUS_A("erp:oldfornew_schedule_status:1", "未审核"),
    OLDFORNEW_SCHEDULE_STATUS_B("erp:oldfornew_schedule_status:2", "已审核"),

    /**
     * 契税活动类型
     */
    COUPON_UNDERTAKE_RULE_TYPE_A("erp:coupon_undertake_rule_type:1", "适老补贴"),
    COUPON_UNDERTAKE_RULE_TYPE_B("erp:coupon_undertake_rule_type:2", "以旧换新"),
    COUPON_UNDERTAKE_RULE_TYPE_C("erp:coupon_undertake_rule_type:3", "契税券"),
    /**
     * 契税单据状态
     */
    COUPON_UNDERTAKE_RULE_STATUS_A("erp:coupon_undertake_rule_status:1", "未审核"),
    COUPON_UNDERTAKE_RULE_STATUS_B("erp:coupon_undertake_rule_status:2", "已审核"),
    COUPON_UNDERTAKE_RULE_STATUS_C("erp:coupon_undertake_rule_status:3", "已终止"),
    /**
     * 挂券承担方
     */
    UNDERTAKE_NAME_A("erp:undertake_name:1", "公司"),
    UNDERTAKE_NAME_B("erp:undertake_name:2", "供应商"),
    UNDERTAKE_NAME_C("erp:undertake_name:3", "政府"),
    /**
	 * 异业券类型
	 */
    VONDER_COUPON_TYPE__LIAN_CHAO("erp:vonder_coupon_type:1", "新百连超"),
    VONDER_COUPON_TYPE__BAI_HUO("erp:vonder_coupon_type:2", "新百百货"),

    // 抖音券类型
    DOUYIN_COUPON_GROUPON_TYPE_MEAL("erp:douyin_coupon_groupon_type:1", "代金券"),
    DOUYIN_COUPON_GROUPON_TYPE_CASH("erp:douyin_coupon_groupon_type:2", "礼品券"),

    // 抖音券有效期类型
    DOUYIN_COUPON_VALIDITY_TYPE_FIXED("erp:douyin_coupon_validity_type:1", "固定范围"),
    DOUYIN_COUPON_VALIDITY_TYPE_DAYS("erp:douyin_coupon_validity_type:2", "领取后N天"),

    // 抖音券规则状态 (参照 douyin.md, 1待审核, :2 已启用, :3 已终止)

    DOUYIN_COUPON_RULE_STATUS_SYNCING("erp:douyin_coupon_rule_status:1", "待审核"),
    DOUYIN_COUPON_RULE_STATUS_ENABLED("erp:douyin_coupon_rule_status:2", "已审核/已启用"),
    DOUYIN_COUPON_RULE_STATUS_ENDED("erp:douyin_coupon_rule_status:3", "已终止"),


    // 抖音商品映射状态
    DOUYIN_PRODUCT_STATUS_DISABLED("erp:douyin_product_status:0", "未启用"),
    DOUYIN_PRODUCT_STATUS_ENABLED("erp:douyin_product_status:1", "已启用"),

    // 抖音券面值类型
    DOUYIN_COUPON_VALUE_TYPE_FIXED("erp:douyin_coupon_value_type:1", "固定金额"),

    // 抖音用户绑定来源
    DOUYIN_BINDING_SOURCE_DOUYIN_PURCHASE("erp:douyin_binding_source:1", "抖音购买"),
    DOUYIN_BINDING_SOURCE_MANUAL("erp:douyin_binding_source:2", "主动绑定"),
    DOUYIN_BINDING_SOURCE_PHONE_MATCH("erp:douyin_binding_source:3", "手机号匹配"),

    // 抖音用户绑定状态
    DOUYIN_BINDING_STATUS_PENDING("erp:douyin_binding_status:0", "待确认"),
    DOUYIN_BINDING_STATUS_CONFIRMED("erp:douyin_binding_status:1", "已确认"),
    DOUYIN_BINDING_STATUS_UNBOUND("erp:douyin_binding_status:2", "已解绑"),

    // 抖音券实例状态
    DOUYIN_COUPON_STATUS_UNUSED("erp:douyin_coupon_status:0", "待使用"),
    DOUYIN_COUPON_STATUS_USED("erp:douyin_coupon_status:1", "已使用"),
    DOUYIN_COUPON_STATUS_EXPIRED("erp:douyin_coupon_status:2", "已过期"),
    DOUYIN_COUPON_STATUS_REFUNDED("erp:douyin_coupon_status:3", "已退款"),
    DOUYIN_COUPON_STATUS_CANCELLED("erp:douyin_coupon_status:4", "已撤销"),
    DOUYIN_COUPON_STATUS_FROZEN("erp:douyin_coupon_status:5", "冻结中"),
    DOUYIN_COUPON_STATUS_PENDING_CLAIM("erp:douyin_coupon_status:6", "待认领"),

    // 抖音同步状态 (用于核销、退款等同步场景)
    DOUYIN_SYNC_STATUS_NOT_SYNCED("erp:douyin_sync_status:0", "未同步"),
    DOUYIN_SYNC_STATUS_SUCCESS("erp:douyin_sync_status:1", "同步成功"),
    DOUYIN_SYNC_STATUS_FAILED("erp:douyin_sync_status:2", "同步失败"),

    // 通用操作结果状态 (用于日志等)
    OPERATION_RESULT_STATUS_FAILED("erp:operation_result_status:0", "失败"),
    OPERATION_RESULT_STATUS_SUCCESS("erp:operation_result_status:1", "成功"),
    OPERATION_RESULT_STATUS_PROCESSING("erp:operation_result_status:2", "处理中"),

    // 抖音授权状态枚举
    DOUYIN_AUTH_STATUS_PENDING("erp:douyin_auth_status:0", "待审核"),
    DOUYIN_AUTH_STATUS_APPROVED("erp:douyin_auth_status:1", "审核通过"),
    DOUYIN_AUTH_STATUS_REJECTED("erp:douyin_auth_status:2", "审核拒绝");

	
    private String code;

    private String desc;

    CommonEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CommonEnums convertCode(String code) {
        for (CommonEnums constantEnum : values()) {
            if (constantEnum.getCode().equals(code)) {
                return constantEnum;
            }
        }
        return null;
    }

    public static String getValue(String code) {
        CommonEnums[] commonEnums = values();
        for (CommonEnums common : commonEnums) {
            if (common.getCode().equals(code)) {
                return common.getDesc();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

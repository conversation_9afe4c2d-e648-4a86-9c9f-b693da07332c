package com.besttop.marketing.model.aftersales;

import com.baomidou.mybatisplus.annotation.TableField;
import com.besttop.mybatis.model.BasicEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 服务单管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ServiceBill extends BasicEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 单据号
     */
    private String code;

    /**
     * 单据来源,1:客服录入，2顾客申请，3门店销售
     */
    private String source;

    /**
     * 原单据号
     */
    private String sourceCode;

    /**
     * 服务类型：1新机安装，2维修
     */
    private String serviceType;

    /**
     * 1待分拣，2待派工，3派工待确认，4服务待确认，5待评价，6安装完成，7作废，8已退货
     */
    private String status;

    /**
     * 顾客编码
     */
    private String customerCode;

    /**
     * 收货人姓名
     */
    private String customerName;

    /**
     * 顾客手机
     */
    private String customerTelephone;

    /**
     * 顾客地址
     */
    private String customerAddress;

    /**
     * 预约安装时间
     */
    private Date orderInstallTime;

    /**
     * 销售店code
     */
    private String saleStoreCode;

    /**
     * 销售时间
     */
    private Date saleTime;

    /**
     * sku代码
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku规格型号
     */
    private String skuModel;

    /**
     * 商品图片
     */
    private String pic;

    /**
     * sku数量
     */
    private BigDecimal skuQtty;

    /**
     * sku备注
     */
    private String skuNote;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 制单店
     */
    private String storeCode;

    /**
     * 预约单code
     */
    private String troubleRecordCode;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 是否锁定 0.否 1.是
     */
    private Integer isLock;

    /**
     * 备注
     */
    private String note;

    /**
     * 线路编码
     */
    private String routeCode;

    @TableField(exist = false)
    private String type;

    /**
     * 是否远期送货 ( 0否1是, 默认0 )
     */
    private Integer isLongtermPick;
}

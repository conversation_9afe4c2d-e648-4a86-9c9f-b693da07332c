# 抖音三方码核销流程分析

本文档分析抖音三方码核销流程，包括代金券和礼品券两种核销方式，并对比其差异。

## 1. 代金券核销流程

代金券核销的入口是`DouyinCouponPay.java`的`pay`方法，系统将其作为一种支付方式处理。

### 1.1 代金券核销流程图

```mermaid
flowchart TD
    A[开始: DouyinCouponPay.pay] --> B{参数校验}
    B -->|不通过| C[返回失败结果]
    B -->|通过| D[准备核销参数]
    D --> E[调用DouyinCouponVerifyService.verifyCoupon]
    
    E --> F{核销结果}
    F -->|失败| G[记录支付日志<br/>返回失败结果]
    F -->|成功| H[获取核销券信息]
    H --> I[构建PayResult<br/>设置支付成功状态]
    I --> J[记录支付日志]
    J --> K[返回支付结果]
```

### 1.2 代金券核销详细流程

1. **入口: DouyinCouponPay.pay()**
   - 接收参数: 支付参数(PaymentParam)，包括券码、支付金额、订单号等
   - 获取当前用户和门店信息

2. **检查重复支付**
   - 查询订单是否已使用抖音券支付
   - 一个订单只能使用一张抖音券

3. **参数校验**
   - 验证券码、支付金额、门店编码等不为空
   - 支付金额必须大于0

4. **准备核销参数**
   - 查询订单商品的品类、品牌信息
   - 构建核销所需参数Map

5. **调用核销服务**
   - 调用`DouyinCouponVerifyService.verifyCoupon`进行核销
   - 传入券码、门店编码、操作员ID、核销参数

6. **处理核销结果**
   - 核销失败: 记录日志并返回支付失败结果
   - 核销成功: 获取核销券信息，构建支付成功结果

7. **支付日志记录**
   - 记录支付过程信息到`DouyinCouponLog`表

### 1.3 代金券核销数据库操作节点

| 操作顺序 | 数据库表 | 操作类型 | 操作内容 | 执行条件 |
|---------|---------|---------|---------|---------|
| 1 | t_shopping_pay_record | 查询 | 检查订单是否已有抖音券支付 | 必执行 |
| 2 | t_shopping_order_sku | 查询 | 获取订单商品的品类、品牌信息 | 必执行 |
| 3 | t_douyin_coupon | 查询 | 通过券码查询券信息 | verifyCoupon内部执行 |
| 4 | t_douyin_coupon_rule | 查询 | 获取券关联的规则信息 | verifyCoupon内部执行 |
| 5 | t_douyin_coupon | 更新 | 更新券状态为已使用，记录核销信息 | 券码验证通过后执行 |
| 6 | t_douyin_coupon_log | 插入 | 记录支付/核销日志 | 结束时执行 |

## 2. 礼品券核销流程

礼品券核销的入口是`DouyinCouponController.java`的`verifyCoupon`方法，系统将其作为礼品进行处理。

### 2.1 礼品券核销流程图

```mermaid
flowchart TD
    A[开始: DouyinCouponController.verifyCoupon] --> B[获取券码参数]
    B --> C[调用DouyinCouponService.verifyCoupon]
    C --> D{核销结果}
    D -->|失败| E[记录错误日志<br/>返回失败结果]
    D -->|成功| F[记录成功日志]
    F --> G[返回成功结果]
    
    subgraph "DouyinCouponService.verifyCoupon内部流程"
    C1[获取分布式锁] --> C2{获取锁成功?}
    C2 -->|否| C3[返回处理中]
    C2 -->|是| C4[调用DouyinCouponVerifyService]
    C4 --> C5[判断是否为礼品券]
    C5 -->|是| C6[调用DouyinGiftVerificationService]
    C5 -->|否| C7[普通券处理]
    C6 --> C9[返回处理结果]
    C7 --> C9
    end
```

### 2.2 礼品券核销详细流程

1. **入口: DouyinCouponController.verifyCoupon()**
   - 接收参数: Map类型参数，包含券码等信息
   - 提取券码参数

2. **调用核销服务**
   - 调用`DouyinCouponService.verifyCoupon`进行核销
   - 只传入券码和参数Map

3. **DouyinCouponService.verifyCoupon内部流程**
   - 获取分布式锁，防止并发核销
   - 调用`DouyinCouponVerifyService.verifyCoupon`进行基础验证
   - 检测券类型，如果是礼品券，则调用`DouyinGiftVerificationService`

4. **礼品券特殊处理**
   - 调用`DouyinGiftVerificationService.verifyGiftCouponWithValidatedData`
   - 创建礼品券核销记录(DouyinGiftVerificationRecord)
   - 核销后礼品处于待发放状态，不再创建发放记录

5. **处理核销结果**
   - 核销失败: 记录错误日志并返回失败结果
   - 核销成功: 记录成功日志并返回成功结果，包含核销类型等信息

### 2.3 礼品券核销数据库操作节点

| 操作顺序 | 数据库表 | 操作类型 | 操作内容 | 执行条件 |
|---------|---------|---------|---------|---------|
| 1 | t_douyin_coupon | 查询 | 通过券码查询券信息 | 必执行 |
| 2 | t_douyin_coupon_rule | 查询 | 获取券关联的规则信息 | 必执行 |
| 3 | t_douyin_coupon_rule_detail | 查询 | 获取券规则的明细信息 | 礼品券执行 |
| 4 | t_douyin_gift_verification_record | 插入 | 创建礼品券核销记录 | 礼品券执行 |
| 5 | t_douyin_coupon | 更新 | 更新券状态为已使用，记录核销信息 | 核销成功执行 |
| 6 | t_douyin_coupon_log | 插入 | 记录核销日志 | 结束时执行 |

## 3. 核销流程关键组件

### 3.1 核心服务类

1. **DouyinCouponPay**: 代金券核销入口，实现Pay接口
2. **DouyinCouponController**: 礼品券核销入口
3. **DouyinCouponVerifyService**: 核销验证服务，提供基础验证功能
4. **DouyinCouponService**: 券服务，提供券相关操作
5. **DouyinGiftVerificationService**: 礼品券核销专用服务

### 3.2 关键验证步骤

1. **前置校验(preVerifyCheck)**:
   - 券码存在性验证
   - 券状态验证(未使用、未过期)
   - 有效期验证

2. **规则验证(verifyRule)**:
   - 门店限制验证
   - 规则状态验证
   - 规则时间有效性验证
   - 库存验证

3. **处理验证(processVerification)**:
   - 更新券状态为已使用
   - 记录核销时间、核销门店、核销员等信息

4. **后置处理(postVerifyProcess)**:
   - 同步核销状态到抖音平台(异步)
   - 更新相关订单状态

## 4. 代金券与礼品券核销对比

| 对比项 | 代金券核销 | 礼品券核销 |
|-------|----------|----------|
| **入口类** | DouyinCouponPay | DouyinCouponController |
| **入口方法** | pay | verifyCoupon |
| **业务定位** | 作为支付方式处理 | 作为礼品处理 |
| **参数要求** | 需要订单号、支付金额等 | 只需券码 |
| **关联订单** | 必须关联订单 | 不强制关联订单 |
| **核销后操作** | 完成支付流程 | 礼品处于待发放状态 |
| **特殊表操作** | t_shopping_pay_record | t_douyin_gift_verification_record |
| **校验严格度** | 较高，需校验金额、订单关联 | 相对宽松，主要校验券有效性 |
| **并发控制** | 支付记录表防重 | 分布式锁 |
| **执行事务** | 支付事务 | 核销事务 |
| **核心服务调用路径** | DouyinCouponPay → DouyinCouponVerifyService | DouyinCouponController → DouyinCoupon
Service → DouyinGiftVerificationService |
| **异步通知** | 无 | 部分场景支持 |

## 5. 核心流程共通点

尽管代金券和礼品券的核销入口和处理逻辑不同，但它们都共享以下核心流程：

1. **基础验证流程**:
   - 两种券都通过DouyinCouponVerifyService进行基础验证
   - 包含前置校验、规则验证、处理验证、后置处理四个步骤

2. **数据库操作**:
   - 都会查询t_douyin_coupon和t_douyin_coupon_rule表
   - 都会更新券状态为已使用
   - 都会记录核销日志

3. **异常处理**:
   - 都有完整的异常捕获和日志记录
   - 都有友好的错误提示

4. **安全控制**:
   - 都实施了防并发和幂等性控制
   - 都有完整的权限和参数验证

## 6. 系统优化建议

1. **代码重构**:
   - 统一代金券和礼品券的核销入口
   - 抽取共同的验证逻辑到基类中

2. **性能优化**:
   - 优化分布式锁机制，减少锁持有时间
   - 增加缓存层，减少数据库查询

3. **可维护性提升**:
   - 完善日志体系，增加更详细的操作追踪
   - 规范化错误码和错误信息

4. **功能增强**:
   - 支持批量核销场景
   - 增强核销撤销功能
   - 增强异步通知机制 
# 抖音礼品券核销模块实现方案

## 目录
1. [现有架构分析](#现有架构分析)
2. [核心需求分析](#核心需求分析)
3. [技术架构设计](#技术架构设计)
4. [数据库设计完善](#数据库设计完善)
5. [核心服务实现](#核心服务实现)
6. [接口设计](#接口设计)
7. [集成方案](#集成方案)
8. [退款处理机制](#退款处理机制)

---

## 现有架构分析

### 现有模块概览
通过对现有代码的深度分析，发现系统已具备完善的抖音集成架构：

#### 1. 控制器层 (Controller Layer)
- **DouyinSpiController** (174行) - 标准SPI回调处理
- **DouyinProSpiController** (120行) - Pro版本SPI回调处理  
- **DouyinCouponController** (96行) - 内部API控制器
- **DouyinAuthController** (41行) - 授权管理控制器

#### 2. 服务层 (Service Layer)
- **DouyinCouponServiceImpl** (559行) - 核心券服务
  - 分布式锁机制 (Redis)
  - 券码生成与管理
  - 验证委托处理
  - 退款流程处理
- **DouyinCouponVerifyServiceImpl** (689行) - 完整验证服务
  - 多步骤验证工作流
  - 事务管理
  - 抖音API集成
- **DouyinProSpiServiceImpl** (1505行) - Pro版本SPI服务
  - 复杂退款场景处理
  - 补码逻辑处理
  - 上下文管理机制

#### 3. 数据层 (Data Layer)
- 完整的MyBatis映射器配置
- 支持复杂查询和事务操作
- 分布式数据一致性保证

#### 4. 核心特性
- **分布式锁**: 基于Redis的并发控制
- **事务管理**: Spring @Transactional注解
- **日志审计**: 完整的操作日志记录
- **状态机管理**: 券码生命周期状态控制
- **幂等性保证**: 防重复操作机制

### 礼品发放系统分析
**ManualGiftRecordServiceImpl** (2329行) 提供完整的礼品发放和回收功能：

#### 礼品类型支持
- **A类礼品**: 电子币 (disposeA方法)
- **B类礼品**: 自营商品 (disposeB方法) 
- **C类礼品**: 联营商品 (disposeC方法)

#### 核心能力
- 复杂的供应商分摊算法
- 库存管理和批次处理
- 物流配送系统集成
- 财务分类账管理
- 客户账户管理

---

## 核心需求分析

### 功能需求
1. **券核销处理**: 完成抖音礼品券的核销操作
2. **核销记录生成**: 生成一张礼品核销单
3. **发放单创建**: 生成状态为"待发放"的礼品发放单
4. **退款接口**: 处理未核销券的退款申请
5. **信息同步**: 处理已核销券的退款信息同步

### 非功能需求
1. **性能要求**: 券核销响应时间 < 2s
2. **并发能力**: 支持1000+ TPS
3. **可靠性**: 99.9%可用性，完整事务保证
4. **兼容性**: 与现有模块100%兼容
5. **可扩展性**: 支持未来礼品类型扩展

---

## 技术架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  抖音平台        │    │  EDP营销系统     │    │  礼品发放系统    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   SPI接口   │ │◄──►│ │抖音券核销模块│ │◄──►│ │ 礼品发放模块│ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│                 │    │ │  退款处理   │ │    │ │ 客户账户    │ │
│                 │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件设计

#### 1. DouyinGiftVerificationService (新增)
**职责**: 抖音礼品券核销的业务编排层
```java
@Service
public class DouyinGiftVerificationService {
    // 核销主流程
    public DouyinGiftVerifyResult verifyGiftCoupon(String couponCode, String storeCode, String staffId, Map<String, Object> params);
    
    // 生成核销记录
    private DouyinGiftVerificationRecord createVerificationRecord(...);
    
    // 创建礼品发放单
    private ManualGiftRecord createPendingGiftRecord(...);
    
    // 处理不同礼品类型
    private void processGiftTypeA/B/C(...);
}
```

#### 2. DouyinGiftRefundService (新增)
**职责**: 抖音礼品券退款处理
```java
@Service
public class DouyinGiftRefundService {
    // 未核销券退款
    public Map<String, Object> processUnverifiedRefund(String couponCode, String refundId);
    
    // 已核销券信息同步
    public Map<String, Object> syncVerifiedRefundInfo(String couponCode, String refundId, Map<String, Object> refundInfo);
    
    // 礼品发放单处理
    private void handleGiftRecordRefund(...);
}
```


---

## 数据库设计完善

### 新增表结构

#### 1. t_douyin_gift_verification_record (抖音礼品核销记录表)
```sql
CREATE TABLE t_douyin_gift_verification_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    coupon_code VARCHAR(64) NOT NULL COMMENT '券码',
    coupon_id VARCHAR(32) NOT NULL COMMENT '券ID',
    douyin_order_id VARCHAR(64) NOT NULL COMMENT '抖音订单ID',
    verification_time DATETIME NOT NULL COMMENT '核销时间',
    store_code VARCHAR(32) NOT NULL COMMENT '门店编码',
    staff_id VARCHAR(32) NOT NULL COMMENT '操作员ID',
    gift_type CHAR(1) NOT NULL COMMENT '礼品类型(A/B/C)',
    gift_record_id VARCHAR(32) COMMENT '关联礼品发放单ID',
    verification_status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '核销状态',
    sync_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '同步状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(32),
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    update_by VARCHAR(32),
    INDEX idx_coupon_code (coupon_code),
    INDEX idx_douyin_order_id (douyin_order_id),
    INDEX idx_verification_time (verification_time)
) COMMENT='抖音礼品核销记录表';
```

#### 2. t_douyin_gift_refund_record (抖音礼品退款记录表)
```sql
CREATE TABLE t_douyin_gift_refund_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    refund_id VARCHAR(64) NOT NULL COMMENT '退款ID',
    coupon_code VARCHAR(64) NOT NULL COMMENT '券码',
    coupon_id VARCHAR(32) NOT NULL COMMENT '券ID',
    refund_type VARCHAR(20) NOT NULL COMMENT '退款类型(UNVERIFIED/VERIFIED)',
    refund_scenario VARCHAR(30) NOT NULL COMMENT '退款场景',
    gift_record_id VARCHAR(32) COMMENT '关联礼品发放单ID',
    refund_status VARCHAR(20) NOT NULL COMMENT '退款状态',
    refund_amount DECIMAL(10,2) COMMENT '退款金额',
    refund_reason TEXT COMMENT '退款原因',
    refund_request_time DATETIME NOT NULL COMMENT '退款申请时间',
    refund_complete_time DATETIME COMMENT '退款完成时间',
    sync_from_source VARCHAR(20) COMMENT '同步来源(DOUYIN/LAIKE)',
    request_payload_json TEXT COMMENT '请求数据JSON',
    response_payload_json TEXT COMMENT '响应数据JSON',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(32),
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    update_by VARCHAR(32),
    INDEX idx_refund_id (refund_id),
    INDEX idx_coupon_code (coupon_code),
    INDEX idx_refund_type (refund_type)
) COMMENT='抖音礼品退款记录表';
```

### 现有表结构扩展

#### t_douyin_coupon 表扩展
```sql
ALTER TABLE t_douyin_coupon ADD COLUMN gift_type CHAR(1) COMMENT '礼品类型(A/B/C)' AFTER coupon_sub_code;
ALTER TABLE t_douyin_coupon ADD COLUMN gift_verification_record_id VARCHAR(32) COMMENT '礼品核销记录ID' AFTER gift_type;
ALTER TABLE t_douyin_coupon ADD COLUMN gift_record_id VARCHAR(32) COMMENT '礼品发放单ID' AFTER gift_verification_record_id;
```

#### manual_gift_record 表扩展
```sql
ALTER TABLE manual_gift_record ADD COLUMN douyin_coupon_code VARCHAR(64) COMMENT '关联抖音券码' AFTER create_by;
ALTER TABLE manual_gift_record ADD COLUMN douyin_verification_id VARCHAR(32) COMMENT '抖音核销记录ID' AFTER douyin_coupon_code;
```

---

## 核心服务实现

### 1. DouyinGiftVerificationService 实现

```java
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DouyinGiftVerificationServiceImpl implements DouyinGiftVerificationService {
    
    private final DouyinCouponVerifyService douyinCouponVerifyService;
    private final ManualGiftRecordService manualGiftRecordService;
    private final DouyinGiftVerificationRecordMapper verificationRecordMapper;
    private final StringRedisTemplate redisTemplate;
    
    private static final String LOCK_PREFIX = "douyin:gift:verify:";
    
    @Override
    public DouyinGiftVerifyResult verifyGiftCoupon(String couponCode, String storeCode, 
                                                   String staffId, Map<String, Object> params) {
        // 分布式锁控制并发
        String lockKey = LOCK_PREFIX + couponCode;
        boolean locked = acquireLock(lockKey);
        if (!locked) {
            return DouyinGiftVerifyResult.fail("券码正在处理中，请稍后重试");
        }
        
        try {
            // 1. 调用现有券核销服务进行基础验证和核销
            DouyinCouponVerifyResult couponResult = douyinCouponVerifyService
                .verifyCoupon(couponCode, storeCode, staffId, params);
            
            if (!couponResult.isSuccess()) {
                return DouyinGiftVerifyResult.fromCouponResult(couponResult);
            }
            
            // 2. 获取券信息和规则
            DouyinCoupon coupon = couponResult.getCouponInfo();
            DouyinCouponRule rule = couponResult.getCouponRule();
            
            // 3. 确定礼品类型
            String giftType = determineGiftType(rule);
            
            // 4. 创建礼品核销记录
            DouyinGiftVerificationRecord verificationRecord = createVerificationRecord(
                coupon, rule, giftType, storeCode, staffId, couponResult);
            
            // 5. 创建待发放礼品记录
            ManualGiftRecord giftRecord = createPendingGiftRecord(
                coupon, rule, giftType, verificationRecord, params);
            
            // 6. 更新券表关联信息
            updateCouponGiftInfo(coupon.getId(), giftType, 
                verificationRecord.getId(), giftRecord.getId());
            
            // 7. 构建返回结果
            return DouyinGiftVerifyResult.success(couponResult, verificationRecord, giftRecord);
            
        } catch (Exception e) {
            log.error("抖音礼品券核销异常: {}", couponCode, e);
            throw new RuntimeException("礼品券核销处理失败: " + e.getMessage());
        } finally {
            releaseLock(lockKey);
        }
    }
    
    /**
     * 确定礼品类型
     */
    private String determineGiftType(DouyinCouponRule rule) {
        // 根据券规则的 groupon_type 确定礼品类型
        String grouponType = rule.getGrouponType();
        if ("erp:douyin_coupon_groupon_type:1".equals(grouponType)) {
            return "A"; // 代金券对应电子币
        } else if ("erp:douyin_coupon_groupon_type:2".equals(grouponType)) {
            // 根据商品属性进一步判断B类还是C类
            return rule.getStoreCode().startsWith("JV") ? "C" : "B";
        }
        return "B"; // 默认B类
    }
    
    /**
     * 创建礼品核销记录
     */
    private DouyinGiftVerificationRecord createVerificationRecord(
            DouyinCoupon coupon, DouyinCouponRule rule, String giftType,
            String storeCode, String staffId, DouyinCouponVerifyResult couponResult) {
        
        DouyinGiftVerificationRecord record = new DouyinGiftVerificationRecord();
        record.setId(primaryKeyUtil.generateKey("t_douyin_gift_verification_record", "DGV", 10));
        record.setCouponCode(coupon.getCouponCode());
        record.setCouponId(coupon.getId());
        record.setDouyinOrderId(coupon.getDouyinOrderId());
        record.setVerificationTime(new Date());
        record.setStoreCode(storeCode);
        record.setStaffId(staffId);
        record.setGiftType(giftType);
        record.setVerificationStatus("SUCCESS");
        record.setSyncStatus("PENDING");
        record.setCreateBy("DOUYIN_GIFT_VERIFY");
        
        verificationRecordMapper.insert(record);
        
        log.info("创建礼品核销记录成功: recordId={}, couponCode={}, giftType={}", 
                record.getId(), coupon.getCouponCode(), giftType);
        
        return record;
    }
    
    /**
     * 创建待发放礼品记录
     */
    private ManualGiftRecord createPendingGiftRecord(
            DouyinCoupon coupon, DouyinCouponRule rule, String giftType,
            DouyinGiftVerificationRecord verificationRecord, Map<String, Object> params) {
        
        // 构建礼品发放参数
        ManualGiftRecordCreateDTO createDTO = buildGiftRecordDTO(
            coupon, rule, giftType, verificationRecord, params);
        
        // 调用现有礼品发放服务创建发放单
        ManualGiftRecord giftRecord = manualGiftRecordService.createGiftRecord(createDTO);
        
        // 设置为待发放状态
        giftRecord.setStatus(ManualGiftRecord.STATUS_PENDING);
        giftRecord.setDouyinCouponCode(coupon.getCouponCode());
        giftRecord.setDouyinVerificationId(verificationRecord.getId());
        
        manualGiftRecordService.updateById(giftRecord);
        
        log.info("创建待发放礼品记录成功: giftRecordId={}, couponCode={}, giftType={}", 
                giftRecord.getId(), coupon.getCouponCode(), giftType);
        
        return giftRecord;
    }
    
    /**
     * 构建礼品发放单创建参数
     */
    private ManualGiftRecordCreateDTO buildGiftRecordDTO(
            DouyinCoupon coupon, DouyinCouponRule rule, String giftType,
            DouyinGiftVerificationRecord verificationRecord, Map<String, Object> params) {
        
        ManualGiftRecordCreateDTO dto = new ManualGiftRecordCreateDTO();
        
        // 基础信息
        dto.setCustomerPhone(coupon.getPhone());
        dto.setStoreCode(verificationRecord.getStoreCode());
        dto.setOperatorId(verificationRecord.getStaffId());
        dto.setSource("DOUYIN_GIFT_VERIFY");
        dto.setSourceOrderId(coupon.getDouyinOrderId());
        
        // 根据礼品类型设置具体参数
        switch (giftType) {
            case "A":
                setupGiftTypeA(dto, coupon, rule, params);
                break;
            case "B":
                setupGiftTypeB(dto, coupon, rule, params);
                break;
            case "C":
                setupGiftTypeC(dto, coupon, rule, params);
                break;
        }
        
        return dto;
    }
    
    private void setupGiftTypeA(ManualGiftRecordCreateDTO dto, DouyinCoupon coupon, 
                               DouyinCouponRule rule, Map<String, Object> params) {
        dto.setGiftType("A");
        dto.setAmount(rule.getPayAmount()); // 代金券金额转为电子币
        dto.setAccountType("ELECTRONIC_COIN");
        dto.setDescription("抖音代金券核销发放电子币");
    }
    
    private void setupGiftTypeB(ManualGiftRecordCreateDTO dto, DouyinCoupon coupon,
                               DouyinCouponRule rule, Map<String, Object> params) {
        dto.setGiftType("B");
        dto.setProductCode(rule.getProductCode());
        dto.setProductName(rule.getProductName());
        dto.setQuantity(1);
        dto.setDeliveryType("LOGISTICS");
        dto.setDescription("抖音礼品券核销发放自营商品");
    }
    
    private void setupGiftTypeC(ManualGiftRecordCreateDTO dto, DouyinCoupon coupon,
                               DouyinCouponRule rule, Map<String, Object> params) {
        dto.setGiftType("C");
        dto.setProductCode(rule.getProductCode());
        dto.setProductName(rule.getProductName());
        dto.setSupplierCode(rule.getSupplierCode());
        dto.setQuantity(1);
        dto.setDeliveryType("SUPPLIER_DIRECT");
        dto.setDescription("抖音礼品券核销发放联营商品");
    }
}
```

### 2. DouyinGiftRefundService 实现

```java
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DouyinGiftRefundServiceImpl implements DouyinGiftRefundService {
    
    private final DouyinCouponService douyinCouponService;
    private final ManualGiftRecordService manualGiftRecordService;
    private final DouyinGiftRefundRecordMapper refundRecordMapper;
    private final DouyinGiftVerificationRecordMapper verificationRecordMapper;
    
    @Override
    public Map<String, Object> processUnverifiedRefund(String couponCode, String refundId) {
        log.info("处理未核销券退款: couponCode={}, refundId={}", couponCode, refundId);
        
        try {
            // 1. 查询券信息
            DouyinCoupon coupon = douyinCouponService.findByCouponCode(couponCode).get(0);
            if (coupon == null) {
                return buildErrorResponse("券码不存在");
            }
            
            // 2. 验证券状态
            if (!DouyinCoupon.STATUS_UNUSED.equals(coupon.getCouponStatus())) {
                return buildErrorResponse("券已使用，无法退款");
            }
            
            // 3. 调用现有退款服务
            Map<String, Object> refundResult = douyinCouponService.processRefund(
                coupon.getDouyinOrderId(), refundId);
            
            if (!(Boolean) refundResult.get("success")) {
                return refundResult;
            }
            
            // 4. 创建礼品退款记录
            createGiftRefundRecord(coupon, refundId, "UNVERIFIED", "DIRECT_REFUND", null);
            
            log.info("未核销券退款处理成功: couponCode={}", couponCode);
            return buildSuccessResponse("退款处理成功");
            
        } catch (Exception e) {
            log.error("处理未核销券退款异常: {}", couponCode, e);
            return buildErrorResponse("退款处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> syncVerifiedRefundInfo(String couponCode, String refundId, 
                                                      Map<String, Object> refundInfo) {
        log.info("同步已核销券退款信息: couponCode={}, refundId={}", couponCode, refundId);
        
        try {
            // 1. 查询券信息
            DouyinCoupon coupon = douyinCouponService.findByCouponCode(couponCode).get(0);
            if (coupon == null) {
                return buildErrorResponse("券码不存在");
            }
            
            // 2. 验证券已核销
            if (!DouyinCoupon.STATUS_USED.equals(coupon.getCouponStatus())) {
                return buildErrorResponse("券未核销，无法同步退款信息");
            }
            
            // 3. 查询核销记录
            DouyinGiftVerificationRecord verificationRecord = 
                verificationRecordMapper.selectByCouponCode(couponCode);
            if (verificationRecord == null) {
                return buildErrorResponse("核销记录不存在");
            }
            
            // 4. 处理礼品发放单退款
            if (StringUtils.isNotEmpty(verificationRecord.getGiftRecordId())) {
                handleGiftRecordRefund(verificationRecord.getGiftRecordId(), refundInfo);
            }
            
            // 5. 更新券状态
            coupon.setCouponStatus(DouyinCoupon.STATUS_REFUNDED);
            coupon.setRefundId(refundId);
            coupon.setRefundCompleteTime(new Date());
            douyinCouponService.updateById(coupon);
            
            // 6. 创建礼品退款记录
            createGiftRefundRecord(coupon, refundId, "VERIFIED", "INFO_SYNC", refundInfo);
            
            log.info("已核销券退款信息同步成功: couponCode={}", couponCode);
            return buildSuccessResponse("退款信息同步成功");
            
        } catch (Exception e) {
            log.error("同步已核销券退款信息异常: {}", couponCode, e);
            return buildErrorResponse("退款信息同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理礼品发放单退款
     */
    private void handleGiftRecordRefund(String giftRecordId, Map<String, Object> refundInfo) {
        ManualGiftRecord giftRecord = manualGiftRecordService.getById(giftRecordId);
        if (giftRecord == null) {
            log.warn("礼品发放单不存在: {}", giftRecordId);
            return;
        }
        
        String giftType = giftRecord.getGiftType();
        String currentStatus = giftRecord.getStatus();
        
        log.info("处理礼品发放单退款: giftRecordId={}, giftType={}, status={}", 
                giftRecordId, giftType, currentStatus);
        
        switch (currentStatus) {
            case ManualGiftRecord.STATUS_PENDING:
                // 待发放状态，直接取消
                giftRecord.setStatus(ManualGiftRecord.STATUS_CANCELLED);
                giftRecord.setCancelReason("抖音退款取消发放");
                break;
                
            case ManualGiftRecord.STATUS_GRANTED:
                // 已发放状态，需要回收处理
                processGrantedGiftRefund(giftRecord, giftType, refundInfo);
                break;
                
            case ManualGiftRecord.STATUS_SHIPPED:
                // 已发货状态，需要特殊处理
                processShippedGiftRefund(giftRecord, giftType, refundInfo);
                break;
                
            default:
                log.warn("礼品发放单状态不支持退款: giftRecordId={}, status={}", 
                        giftRecordId, currentStatus);
                return;
        }
        
        giftRecord.setUpdateBy("DOUYIN_GIFT_REFUND");
        giftRecord.setUpdateTime(new Date());
        manualGiftRecordService.updateById(giftRecord);
    }
    
    /**
     * 处理已发放礼品的退款
     */
    private void processGrantedGiftRefund(ManualGiftRecord giftRecord, String giftType, 
                                         Map<String, Object> refundInfo) {
        switch (giftType) {
            case "A":
                // 电子币退款：调用客户账户服务扣减余额
                processElectronicCoinRefund(giftRecord, refundInfo);
                break;
            case "B":
                // 自营商品退款：调用库存服务回收库存
                processSelfOperatedProductRefund(giftRecord, refundInfo);
                break;
            case "C":
                // 联营商品退款：通知供应商处理
                processJointVentureProductRefund(giftRecord, refundInfo);
                break;
        }
        
        giftRecord.setStatus(ManualGiftRecord.STATUS_REFUNDED);
        giftRecord.setRefundReason("抖音客服/来客系统退款");
    }
    
    /**
     * 创建礼品退款记录
     */
    private void createGiftRefundRecord(DouyinCoupon coupon, String refundId, 
                                       String refundType, String refundScenario, 
                                       Map<String, Object> refundInfo) {
        DouyinGiftRefundRecord record = new DouyinGiftRefundRecord();
        record.setId(primaryKeyUtil.generateKey("t_douyin_gift_refund_record", "DGR", 10));
        record.setRefundId(refundId);
        record.setCouponCode(coupon.getCouponCode());
        record.setCouponId(coupon.getId());
        record.setRefundType(refundType);
        record.setRefundScenario(refundScenario);
        record.setGiftRecordId(coupon.getGiftRecordId());
        record.setRefundStatus("SUCCESS");
        record.setRefundRequestTime(new Date());
        record.setRefundCompleteTime(new Date());
        
        if (refundInfo != null) {
            record.setRefundAmount((BigDecimal) refundInfo.get("refund_amount"));
            record.setRefundReason((String) refundInfo.get("refund_reason"));
            record.setSyncFromSource((String) refundInfo.get("source"));
            record.setRequestPayloadJson(JSON.toJSONString(refundInfo));
        }
        
        record.setCreateBy("DOUYIN_GIFT_REFUND");
        refundRecordMapper.insert(record);
        
        log.info("创建礼品退款记录成功: recordId={}, couponCode={}, refundType={}", 
                record.getId(), coupon.getCouponCode(), refundType);
    }
}
```

---

## 接口设计

### 1. 内部API接口扩展

#### 扩展现有DouyinCouponController
**无需创建新的Controller，在现有的DouyinCouponController中增加礼品券查询接口**

```java
// 在现有DouyinCouponController中添加礼品券核销记录查询
@PostMapping("/gift/verification/list")
public ResultEntity listGiftVerificationRecords(@RequestBody Map<String, Object> params) {
    try {
        log.info("查询抖音礼品核销记录: {}", params);
        
        String couponCode = String.valueOf(params.getOrDefault("couponCode", ""));
        String storeCode = String.valueOf(params.getOrDefault("storeCode", ""));
        String giftType = String.valueOf(params.getOrDefault("giftType", ""));
        
        // 调用礼品核销记录查询服务
        DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);
        List<DouyinGiftVerificationRecord> records = giftService.listVerificationRecords(params);
        
        return success(records);
    } catch (Exception e) {
        log.error("查询抖音礼品核销记录失败", e);
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询失败：" + e.getMessage());
    }
}

/**
 * 查询礼品退款记录
 */
@PostMapping("/gift/refund/list")
public ResultEntity listGiftRefundRecords(@RequestBody Map<String, Object> params) {
    try {
        log.info("查询抖音礼品退款记录: {}", params);
        
        // 调用礼品退款记录查询服务
        DouyinGiftRefundService refundService = SpringContext.getBean(DouyinGiftRefundService.class);
        List<DouyinGiftRefundRecord> records = refundService.listRefundRecords(params);
        
        return success(records);
    } catch (Exception e) {
        log.error("查询抖音礼品退款记录失败", e);
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询失败：" + e.getMessage());
    }
}

/**
 * 查询礼品券详情（包含核销和发放信息）
 */
@PostMapping("/gift/detail")
public ResultEntity getGiftCouponDetail(@RequestBody Map<String, Object> params) {
    try {
        String couponCode = String.valueOf(params.getOrDefault("couponCode", ""));
        log.info("查询抖音礼品券详情: {}", couponCode);
        
        // 构建完整的礼品券信息
        Map<String, Object> result = new HashMap<>();
        
        // 1. 基础券信息
        Map<String, Object> couponDetail = douyinCouponService.getCouponDetail(couponCode);
        result.put("coupon", couponDetail);
        
        // 2. 核销记录
        DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);
        DouyinGiftVerificationRecord verificationRecord = giftService.getVerificationRecord(couponCode);
        result.put("verification", verificationRecord);
        
        // 3. 礼品发放记录
        if (verificationRecord != null && StringUtils.isNotEmpty(verificationRecord.getGiftRecordId())) {
            ManualGiftRecord giftRecord = manualGiftRecordService.getById(verificationRecord.getGiftRecordId());
            result.put("giftRecord", giftRecord);
        }
        
        // 4. 退款记录
        DouyinGiftRefundService refundService = SpringContext.getBean(DouyinGiftRefundService.class);
        List<DouyinGiftRefundRecord> refundRecords = refundService.getRefundRecords(couponCode);
        result.put("refundRecords", refundRecords);
        
        return success(result);
    } catch (Exception e) {
        log.error("查询抖音礼品券详情失败", e);
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "查询详情失败：" + e.getMessage());
    }
}
```

### 2. SPI接口增强

#### DouyinProSpiService 礼品券处理增强
**在现有的DouyinProSpiController的退款接口中集成礼品券处理逻辑**

通过在现有的DouyinProSpiController的`/refund-apply`和`/refund-notice`接口中增加礼品券检测和路由逻辑，实现礼品券退款功能的无缝集成。这种方案保持API的向后兼容性，抖音平台无需修改任何调用逻辑。

```java
// 在DouyinProSpiServiceImpl中增强handleRefundApply方法
@Override
@Transactional(rollbackFor = Exception.class)
public Map<String, Object> handleRefundApply(Map<String, Object> requestBody) {
    String orderId = null;
    try {
        // 1. 现有的参数校验和预处理逻辑...
        RefundRequestContext context = validateAndPrepareRequest(requestBody);
        orderId = context.getOrderId();
        
        // 2. 幂等性和重复申请检查...
        Map<String, Object> cachedResult = checkIdempotencyAndDuplication(context);
        if (cachedResult != null) {
            return cachedResult;
        }
        
        // 3. 验签检查...
        validateRequestSignature(requestBody);
        
        // 4. 查询和验证订单...
        DouyinPreOrder order = queryAndValidateOrder(context.getOrderId());
        context.setOrder(order);
        
        // ===== 新增：礼品券类型检查和处理 =====
        if (isGiftCouponOrder(order, context)) {
            log.info("检测到礼品券退款申请，orderId={}", orderId);
            return handleGiftCouponRefund(context);
        }
        
        // 5. 现有的业务场景路由处理...
        Map<String, Object> result = routeRefundScenario(context);
        
        // 6. 记录操作日志...
        recordRefundOperationLog(context, result);
        
        return result;
        
    } catch (Exception e) {
        log.error("退款申请系统异常，orderId={}", orderId, e);
        return buildErrorResponse(500, "服务商系统处理异常，请重试");
    }
}

// 在DouyinProSpiServiceImpl中增强handleRefundNotice方法
@Override
@Transactional(rollbackFor = Exception.class)
public Map<String, Object> handleRefundNotice(Map<String, Object> requestBody) {
    try {
        // 1. 现有的验签和参数校验...
        if (!verifyHeaderSignature(requestBody)) {
            return buildErrorResponse(300, "签名验证失败");
        }

        String orderId = getStringValue(requestBody, "order_id");
        String afterSaleId = getStringValue(requestBody, "after_sale_id");
        List<Map<String, Object>> noticeList = (List<Map<String, Object>>)requestBody.get("notice_list");
        
        // 2. 查询订单...
        DouyinPreOrder order = douyinPreOrderMapper.selectByPreOrderId(orderId);
        if (order == null) {
            return buildErrorResponse(300, "订单不存在，请重试");
        }

        // ===== 新增：礼品券退款信息同步处理 =====
        if (isGiftCouponOrder(order, null)) {
            log.info("检测到礼品券退款信息同步，orderId={}", orderId);
            return handleGiftCouponRefundNotice(order, noticeList, requestBody);
        }

        // 3. 现有的退款状态变更处理...
        if(noticeList != null) {
            for (Map<String, Object> contactInfo : noticeList) {
                Integer auditResult = (Integer) contactInfo.get("audit_result");
                String code = contactInfo.get("code").toString();
                handleRefundStatusChange(requestBody, order, auditResult, code);
            }
        }

        // 4. 记录日志...
        recordOrderLog("REFUND_NOTICE_PRO", orderId, null, order.getPhone(), 
                      JSON.toJSONString(requestBody, SerializerFeature.WriteMapNullValue));

        return buildSuccessResponse(new HashMap<>());
    } catch (Exception e) {
        log.error("处理信息同步异常", e);
        return buildErrorResponse(300, "服务商系统处理异常，请重试: " + e.getMessage());
    }
}

/**
 * 判断是否为礼品券订单
 */
private boolean isGiftCouponOrder(DouyinPreOrder order, RefundRequestContext context) {
    // 根据订单类型判断：order_type = 22 表示礼品券
    if (order.getOrderType() != null && order.getOrderType() == 22) {
        return true;
    }
    
    // 或者根据商品规则判断
    if (StringUtils.isNotEmpty(order.getSkuId())) {
        DouyinCouponRule rule = douyinCouponRuleMapper.findByDouyinProductId(order.getSkuId());
        if (rule != null) {
            // groupon_type = 2 表示礼品券
            return "erp:douyin_coupon_groupon_type:2".equals(rule.getGrouponType());
        }
    }
    
    return false;
}

/**
 * 处理礼品券退款申请
 */
private Map<String, Object> handleGiftCouponRefund(RefundRequestContext context) {
    try {
        // 调用礼品券退款服务
        DouyinGiftRefundService giftRefundService = SpringContext.getBean(DouyinGiftRefundService.class);
        
        // 确定退款场景
        List<String> couponCodes = context.getCouponCodes();
        if (CollectionUtils.isEmpty(couponCodes)) {
            // 补码场景：查询订单所有券码
            List<DouyinCoupon> orderCoupons = douyinCouponMapper.selectByOrderId(context.getOrderId());
            couponCodes = orderCoupons.stream()
                .map(DouyinCoupon::getCouponCode)
                .collect(Collectors.toList());
        }
        
        Map<String, Object> result = new HashMap<>();
        boolean hasUnverifiedCoupons = false;
        
        for (String couponCode : couponCodes) {
            DouyinCoupon coupon = douyinCouponService.findByCouponCode(couponCode).get(0);
            if (coupon != null && DouyinCoupon.STATUS_UNUSED.equals(coupon.getCouponStatus())) {
                // 未核销券：直接退款
                Map<String, Object> refundResult = giftRefundService.processUnverifiedRefund(
                    couponCode, context.getAfterSaleId());
                hasUnverifiedCoupons = true;
            }
        }
        
        if (hasUnverifiedCoupons) {
            result.put("result", 1); // 允许退款
            result.put("message", "礼品券退款处理成功");
        } else {
            result.put("result", 2); // 拒绝退款
            result.put("reason", "所有礼品券均已核销，无法退款");
        }
        
        // 缓存响应结果
        cacheResponse(context.getCacheKey(), buildSuccessResponse(result));
        
        return buildSuccessResponse(result);
        
    } catch (Exception e) {
        log.error("处理礼品券退款申请异常: orderId={}", context.getOrderId(), e);
        return buildErrorResponse(500, "礼品券退款处理失败：" + e.getMessage());
    }
}

/**
 * 处理礼品券退款信息同步
 */
private Map<String, Object> handleGiftCouponRefundNotice(DouyinPreOrder order, 
                                                        List<Map<String, Object>> noticeList,
                                                        Map<String, Object> requestBody) {
    try {
        DouyinGiftRefundService giftRefundService = SpringContext.getBean(DouyinGiftRefundService.class);
        
        for (Map<String, Object> notice : noticeList) {
            String couponCode = (String) notice.get("code");
            Integer auditResult = (Integer) notice.get("audit_result");
            
            if (StringUtils.isNotEmpty(couponCode)) {
                // 构建退款信息
                Map<String, Object> refundInfo = new HashMap<>();
                refundInfo.put("audit_result", auditResult);
                refundInfo.put("refund_reason", notice.get("refund_reason"));
                refundInfo.put("source", "DOUYIN_CUSTOMER_SERVICE");
                refundInfo.put("sync_time", new Date());
                
                // 调用礼品券退款信息同步
                if (auditResult == 1) { // 退款通过
                    giftRefundService.syncVerifiedRefundInfo(couponCode, 
                        getStringValue(requestBody, "after_sale_id"), refundInfo);
                }
            }
        }
        
        // 更新订单退款状态
        if (noticeList.stream().anyMatch(n -> (Integer)n.get("audit_result") == 1)) {
            order.setRefundStatus(DouyinPreOrder.REFUND_STATUS_SUCCESS);
            order.setRefundCompleteTime(new Date());
        } else {
            order.setRefundStatus(DouyinPreOrder.REFUND_STATUS_REJECTED);
        }
        order.setOperatorId("DOUYIN_GIFT_SYSTEM");
        order.setSyncStatus(DouyinPreOrder.SYNC_STATUS_SUCCESS);
        douyinPreOrderMapper.updateById(order);
        
        return buildSuccessResponse(new HashMap<>());
        
    } catch (Exception e) {
        log.error("处理礼品券退款信息同步异常: orderId={}", order.getOrderId(), e);
        return buildErrorResponse(500, "礼品券退款信息同步失败：" + e.getMessage());
    }
}
```

---

## 集成方案

### 1. 与现有券核销服务集成

#### 增强DouyinCouponVerifyServiceImpl
```java
@Override
@Transactional(rollbackFor = Exception.class)
public DouyinCouponVerifyResult verifyCoupon(String couponCode, String storeCode, 
                                             String staffId, Map<String, Object> params) {
    log.info("开始执行完整券验证流程: 券码={}, 门店={}, 操作员={}", couponCode, storeCode, staffId);
    
    // 1. 现有的验证前检查...
    DouyinCouponVerifyResult checkResult = preVerifyCheck(couponCode, storeCode);
    if (!checkResult.isSuccess()) {
        return checkResult;
    }
    
    DouyinCoupon coupon = checkResult.getCouponInfo();
    
    // 2. 获取并验证券规则...
    DouyinCouponRule rule = douyinCouponRuleMapper.selectById(coupon.getCouponRuleId());
    if (rule == null) {
        checkResult.setSuccess(false);
        checkResult.setMessage("找不到券规则");
        return checkResult;
    }
    
    // ===== 新增：礼品券类型检查和处理 =====
    if (isGiftCoupon(rule)) {
        log.info("检测到礼品券核销请求: 券码={}, 礼品类型={}", couponCode, determineGiftType(rule));
        // 委托给礼品券核销服务处理
        DouyinGiftVerificationService giftService = SpringContext.getBean(DouyinGiftVerificationService.class);
        DouyinGiftVerifyResult giftResult = giftService.verifyGiftCoupon(couponCode, storeCode, staffId, params);
        
        // 将礼品券结果转换为券核销结果
        return convertGiftResultToCouponResult(giftResult);
    }
    
    // 3. 现有的代金券核销逻辑...
    DouyinCouponVerifyResult ruleResult = verifyRule(coupon, rule, storeCode, params);
    if (!ruleResult.isSuccess()) {
        return ruleResult;
    }
    
    // ... 其余现有逻辑
}

/**
 * 判断是否为礼品券
 */
private boolean isGiftCoupon(DouyinCouponRule rule) {
    // groupon_type = 2 表示礼品券类型
    return "erp:douyin_coupon_groupon_type:2".equals(rule.getGrouponType());
}

/**
 * 确定礼品类型
 */
private String determineGiftType(DouyinCouponRule rule) {
    // 根据商品属性确定A/B/C类型
    if (rule.getProductCode().startsWith("EC_")) {
        return "A"; // 电子币类礼品
    } else if (rule.getSupplierCode().startsWith("JV_")) {
        return "C"; // 联营商品
    } else {
        return "B"; // 自营商品
    }
}

/**
 * 将礼品券结果转换为券核销结果
 */
private DouyinCouponVerifyResult convertGiftResultToCouponResult(DouyinGiftVerifyResult giftResult) {
    DouyinCouponVerifyResult couponResult = new DouyinCouponVerifyResult();
    couponResult.setSuccess(giftResult.isSuccess());
    couponResult.setMessage(giftResult.getMessage());
    couponResult.setCouponInfo(giftResult.getCouponInfo());
    couponResult.setCouponRule(giftResult.getCouponRule());
    couponResult.setVerificationType("GIFT_" + giftResult.getGiftType());
    
    // 添加礼品券特有信息
    Map<String, Object> additionalInfo = new HashMap<>();
    additionalInfo.put("gift_type", giftResult.getGiftType());
    additionalInfo.put("verification_record_id", giftResult.getVerificationRecord().getId());
    additionalInfo.put("gift_record_id", giftResult.getGiftRecord().getId());
    couponResult.setAdditionalInfo(additionalInfo);
    
    return couponResult;
}
```

#### 扩展DouyinCouponController
```java
// 在现有DouyinCouponController的verify方法中，礼品券会自动路由到礼品券处理逻辑
@PostMapping("/coupon/verify")
public ResultEntity verifyCoupon(@RequestBody Map<String, Object> params) {
    try {
        String couponCode = String.valueOf(params.getOrDefault("couponCode", ""));
        String storeCode = String.valueOf(params.getOrDefault("storeCode", ""));
        String staffId = String.valueOf(params.getOrDefault("staffId", ""));
        
        log.info("核销抖音券: {}, 门店: {}, 操作人: {}", couponCode, storeCode, staffId);
        
        // 现有的核销服务会自动判断券类型并路由到相应处理逻辑
        DouyinCouponVerifyResult result = douyinCouponService.verifyCoupon(couponCode, storeCode, staffId, params);
        
        if (result.isSuccess()) {
            log.info("核销成功: {}, 类型: {}", couponCode, result.getVerificationType());
            return success(result);
        } else {
            log.warn("核销失败: {}, 原因: {}", couponCode, result.getMessage());
            return error(ResultEnum.ERP_MARK_ADD_ERROR, "核销失败：" + result.getMessage());
        }
    } catch (Exception e) {
        log.error("核销抖音券失败", e);
        return error(ResultEnum.ERP_MARK_ADD_ERROR, "核销失败：" + e.getMessage());
    }
}
```

### 2. 与礼品发放系统集成

#### 扩展ManualGiftRecordService
```java
/**
 * 为抖音券核销创建礼品发放记录
 */
public ManualGiftRecord createDouyinGiftRecord(DouyinGiftCreateDTO dto) {
    log.info("创建抖音礼品发放记录: couponCode={}, giftType={}", dto.getCouponCode(), dto.getGiftType());
    
    // 复用现有的grantOrRecycle逻辑
    ManualGiftRecord record = new ManualGiftRecord();
    
    // 设置基础信息
    record.setCustomerPhone(dto.getCustomerPhone());
    record.setStoreCode(dto.getStoreCode());
    record.setGiftType(dto.getGiftType());
    record.setStatus(ManualGiftRecord.STATUS_PENDING); // 待发放状态
    record.setSource("DOUYIN_GIFT_VERIFY");
    record.setSourceOrderId(dto.getSourceOrderId());
    record.setOperatorId(dto.getOperatorId());
    
    // 设置抖音特有字段  
    record.setDouyinCouponCode(dto.getCouponCode());
    record.setDouyinVerificationId(dto.getVerificationId());
    record.setCreateBy("DOUYIN_GIFT_VERIFY");
    
    // 根据礼品类型设置具体参数
    switch (dto.getGiftType()) {
        case "A":
            record.setAmount(dto.getAmount());
            record.setAccountType("ELECTRONIC_COIN");
            record.setDescription("抖音代金券核销发放电子币");
            break;
        case "B":
            record.setProductCode(dto.getProductCode());
            record.setProductName(dto.getProductName());
            record.setQuantity(dto.getQuantity());
            record.setDeliveryType("LOGISTICS");
            record.setDescription("抖音礼品券核销发放自营商品");
            break;
        case "C":
            record.setProductCode(dto.getProductCode());
            record.setProductName(dto.getProductName());
            record.setSupplierCode(dto.getSupplierCode());
            record.setQuantity(dto.getQuantity());
            record.setDeliveryType("SUPPLIER_DIRECT");
            record.setDescription("抖音礼品券核销发放联营商品");
            break;
    }
    
    // 保存记录
    this.save(record);
    
    log.info("抖音礼品发放记录创建成功: giftRecordId={}, couponCode={}", 
            record.getId(), dto.getCouponCode());
    
    return record;
}

/**
 * 处理抖音礼品退款时的发放单状态更新
 */
public void handleDouyinGiftRefund(String giftRecordId, Map<String, Object> refundInfo) {
    ManualGiftRecord giftRecord = this.getById(giftRecordId);
    if (giftRecord == null) {
        log.warn("礼品发放单不存在: {}", giftRecordId);
        return;
    }
    
    log.info("处理抖音礼品退款: giftRecordId={}, currentStatus={}", 
            giftRecordId, giftRecord.getStatus());
    
    // 根据当前状态决定处理方式
    switch (giftRecord.getStatus()) {
        case ManualGiftRecord.STATUS_PENDING:
            // 待发放状态，直接取消
            giftRecord.setStatus(ManualGiftRecord.STATUS_CANCELLED);
            giftRecord.setCancelReason("抖音退款取消发放");
            break;
            
        case ManualGiftRecord.STATUS_GRANTED:
            // 已发放状态，需要回收处理
            processGrantedGiftRefund(giftRecord, refundInfo);
            break;
            
        case ManualGiftRecord.STATUS_SHIPPED:
            // 已发货状态，需要特殊处理
            processShippedGiftRefund(giftRecord, refundInfo);
            break;
            
        default:
            log.warn("礼品发放单状态不支持退款: giftRecordId={}, status={}", 
                    giftRecordId, giftRecord.getStatus());
            return;
    }
    
    giftRecord.setUpdateBy("DOUYIN_GIFT_REFUND");
    giftRecord.setUpdateTime(new Date());
    this.updateById(giftRecord);
    
    log.info("抖音礼品退款处理完成: giftRecordId={}, newStatus={}", 
            giftRecordId, giftRecord.getStatus());
}
```

### 3. 与现有SPI服务集成

现有的DouyinProSpiController接口无需修改，通过在DouyinProSpiServiceImpl中增强处理逻辑来支持礼品券：

- **`/refund-apply`** 接口自动检测礼品券订单并路由到礼品券退款处理逻辑
- **`/refund-notice`** 接口自动检测礼品券订单并处理礼品发放单状态变更
- 保持现有API的向后兼容性，抖音平台无需修改调用方式

### 4. 状态流转集成

```java
/**
 * 抖音礼品券状态管理器
 */
@Component
public class DouyinGiftStatusManager {
    
    /**
     * 券状态与礼品发放状态的映射关系
     */
    public static final Map<String, String> COUPON_TO_GIFT_STATUS = Map.of(
        DouyinCoupon.STATUS_UNUSED, ManualGiftRecord.STATUS_INIT,
        DouyinCoupon.STATUS_USED, ManualGiftRecord.STATUS_PENDING,
        DouyinCoupon.STATUS_REFUNDED, ManualGiftRecord.STATUS_CANCELLED
    );
    
    /**
     * 礼品发放状态到券状态的映射
     */
    public static final Map<String, String> GIFT_TO_COUPON_STATUS = Map.of(
        ManualGiftRecord.STATUS_GRANTED, DouyinCoupon.STATUS_GIFT_GRANTED,
        ManualGiftRecord.STATUS_SHIPPED, DouyinCoupon.STATUS_GIFT_SHIPPED,
        ManualGiftRecord.STATUS_COMPLETED, DouyinCoupon.STATUS_GIFT_COMPLETED
    );
    
    /**
     * 同步券状态到礼品发放单
     */
    public void syncCouponStatusToGift(String couponId, String newCouponStatus) {
        DouyinCoupon coupon = douyinCouponService.getById(couponId);
        if (coupon != null && StringUtils.isNotEmpty(coupon.getGiftRecordId())) {
            String giftStatus = COUPON_TO_GIFT_STATUS.get(newCouponStatus);
            if (giftStatus != null) {
                ManualGiftRecord giftRecord = manualGiftRecordService.getById(coupon.getGiftRecordId());
                if (giftRecord != null) {
                    giftRecord.setStatus(giftStatus);
                    giftRecord.setUpdateBy("DOUYIN_STATUS_SYNC");
                    manualGiftRecordService.updateById(giftRecord);
                }
            }
        }
    }
    
    /**
     * 同步礼品发放状态到券
     */  
    public void syncGiftStatusToCoupon(String giftRecordId, String newGiftStatus) {
        ManualGiftRecord giftRecord = manualGiftRecordService.getById(giftRecordId);
        if (giftRecord != null && StringUtils.isNotEmpty(giftRecord.getDouyinCouponCode())) {
            String couponStatus = GIFT_TO_COUPON_STATUS.get(newGiftStatus);
            if (couponStatus != null) {
                List<DouyinCoupon> coupons = douyinCouponService.findByCouponCode(giftRecord.getDouyinCouponCode());
                for (DouyinCoupon coupon : coupons) {
                    coupon.setCouponStatus(couponStatus);
                    douyinCouponService.updateById(coupon);
                }
            }
        }
    }
}
```

---

## 退款处理机制

### 退款场景分类

#### 1. 未核销券退款（直接退款）
```java
/**
 * 场景：用户购买券后未使用，通过抖音申请退款
 * 处理：直接调用现有退款逻辑，更新券状态
 * 影响：无礼品发放单产生
 */
@Override
public Map<String, Object> processUnverifiedRefund(String couponCode, String refundId) {
    // 1. 验证券状态（必须是未使用）
    // 2. 调用DouyinCouponService.processRefund()
    // 3. 记录退款日志
    // 4. 返回处理结果
}
```

#### 2. 已核销券退款信息同步
```java
/**
 * 场景：券已核销发放礼品后，通过抖音客服/来客系统申请退款
 * 处理：同步退款信息，处理已发放礼品的回收/补偿
 * 影响：需要处理礼品发放单状态变更
 */
@Override
public Map<String, Object> syncVerifiedRefundInfo(String couponCode, String refundId, 
                                                  Map<String, Object> refundInfo) {
    // 1. 验证券状态（必须是已使用）
    // 2. 查询关联的礼品发放单
    // 3. 根据礼品发放状态进行相应处理
    // 4. 更新券状态为已退款
    // 5. 记录退款信息同步日志
}
```

### 礼品退款处理策略

#### A类礼品（电子币）退款
```java
private void processElectronicCoinRefund(ManualGiftRecord giftRecord, Map<String, Object> refundInfo) {
    String customerPhone = giftRecord.getCustomerPhone();
    BigDecimal refundAmount = giftRecord.getAmount();
    
    // 调用客户账户服务扣减电子币余额
    CustomerAccountService accountService = SpringContext.getBean(CustomerAccountService.class);
    
    CustomerAccountOperationDTO operation = new CustomerAccountOperationDTO();
    operation.setCustomerPhone(customerPhone);
    operation.setOperationType("DEDUCT");
    operation.setAmount(refundAmount);
    operation.setReason("抖音礼品券退款扣减电子币");
    operation.setOperatorId("DOUYIN_GIFT_REFUND");
    
    boolean success = accountService.processAccountOperation(operation);
    if (!success) {
        throw new RuntimeException("电子币扣减失败，退款处理中止");
    }
    
    log.info("电子币退款处理成功: phone={}, amount={}", customerPhone, refundAmount);
}
```

#### B类礼品（自营商品）退款
```java
private void processSelfOperatedProductRefund(ManualGiftRecord giftRecord, Map<String, Object> refundInfo) {
    String productCode = giftRecord.getProductCode();
    Integer quantity = giftRecord.getQuantity();
    
    switch (giftRecord.getStatus()) {
        case ManualGiftRecord.STATUS_GRANTED:
            // 已发放未发货：回收库存
            recoverProductStock(productCode, quantity, giftRecord.getStoreCode());
            break;
        case ManualGiftRecord.STATUS_SHIPPED:
            // 已发货：创建逆向物流单
            createReverseLogisticsOrder(giftRecord, refundInfo);
            break;
        default:
            log.warn("自营商品退款：不支持的礼品状态 {}", giftRecord.getStatus());
    }
}

private void recoverProductStock(String productCode, Integer quantity, String storeCode) {
    // 调用库存服务回收库存
    InventoryService inventoryService = SpringContext.getBean(InventoryService.class);
    
    StockOperationDTO operation = new StockOperationDTO();
    operation.setProductCode(productCode);
    operation.setStoreCode(storeCode);
    operation.setOperationType("RECOVER");
    operation.setQuantity(quantity);
    operation.setReason("抖音礼品券退款回收库存");
    operation.setOperatorId("DOUYIN_GIFT_REFUND");
    
    inventoryService.processStockOperation(operation);
}
```

#### C类礼品（联营商品）退款
```java
private void processJointVentureProductRefund(ManualGiftRecord giftRecord, Map<String, Object> refundInfo) {
    String supplierCode = giftRecord.getSupplierCode();
    
    // 创建供应商退款通知
    SupplierRefundNotificationDTO notification = new SupplierRefundNotificationDTO();
    notification.setSupplierCode(supplierCode);
    notification.setGiftRecordId(giftRecord.getId());
    notification.setProductCode(giftRecord.getProductCode());
    notification.setQuantity(giftRecord.getQuantity());
    notification.setRefundReason("抖音礼品券客服退款");
    notification.setRefundInfo(refundInfo);
    
    // 发送异步通知到供应商系统
    SupplierNotificationService notificationService = SpringContext.getBean(SupplierNotificationService.class);
    notificationService.sendRefundNotification(notification);
    
    // 创建财务调账记录
    createFinancialAdjustmentRecord(giftRecord, refundInfo);
    
    log.info("联营商品退款通知已发送: supplierCode={}, giftRecordId={}", 
            supplierCode, giftRecord.getId());
}
```

---



---

## 测试方案

### 1. 单元测试

#### 礼品券核销服务测试
```java
@RunWith(SpringRunner.class)
@SpringBootTest
public class DouyinGiftVerificationServiceTest {
    
    @Autowired
    private DouyinGiftVerificationService giftVerificationService;
    
    @Test
    public void testVerifyGiftCouponTypeA() {
        // 测试A类礼品券核销
        String couponCode = "TEST_A_COUPON_001";
        String storeCode = "STORE001";
        String staffId = "STAFF001";
        Map<String, Object> params = new HashMap<>();
        
        DouyinGiftVerifyResult result = giftVerificationService.verifyGiftCoupon(
            couponCode, storeCode, staffId, params);
        
        assertTrue(result.isSuccess());
        assertEquals("A", result.getGiftType());
        assertNotNull(result.getVerificationRecord());
        assertNotNull(result.getGiftRecord());
        assertEquals(ManualGiftRecord.STATUS_PENDING, result.getGiftRecord().getStatus());
    }
    
    @Test
    public void testConcurrentVerification() {
        // 测试并发核销场景，验证分布式锁有效性
        String couponCode = "TEST_CONCURRENT_001";
        
        ExecutorService executor = Executors.newFixedThreadPool(10);
        CountDownLatch latch = new CountDownLatch(10);
        AtomicInteger successCount = new AtomicInteger(0);
        
        for (int i = 0; i < 10; i++) {
            executor.submit(() -> {
                try {
                    DouyinGiftVerifyResult result = giftVerificationService.verifyGiftCoupon(
                        couponCode, "STORE001", "STAFF001", new HashMap<>());
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        // 应该只有一次成功
        assertEquals(1, successCount.get());
    }
}
```

#### 礼品券退款服务测试
```java
@RunWith(SpringRunner.class)
@SpringBootTest  
public class DouyinGiftRefundServiceTest {
    
    @Autowired
    private DouyinGiftRefundService giftRefundService;
    
    @Test
    public void testUnverifiedRefund() {
        // 测试未核销券退款
        String couponCode = "TEST_UNVERIFIED_001";
        String refundId = "REFUND_001";
        
        Map<String, Object> result = giftRefundService.processUnverifiedRefund(
            couponCode, refundId);
        
        assertTrue((Boolean) result.get("success"));
    }
    
    @Test
    public void testVerifiedRefundSync() {
        // 测试已核销券退款信息同步
        String couponCode = "TEST_VERIFIED_001";
        String refundId = "REFUND_002";
        Map<String, Object> refundInfo = new HashMap<>();
        refundInfo.put("refund_amount", new BigDecimal("50.00"));
        refundInfo.put("source", "DOUYIN_CUSTOMER_SERVICE");
        
        Map<String, Object> result = giftRefundService.syncVerifiedRefundInfo(
            couponCode, refundId, refundInfo);
        
        assertTrue((Boolean) result.get("success"));
    }
}
```

### 2. 集成测试

#### SPI接口增强测试
```java
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class DouyinProSpiControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testGiftCouponRefundApply() {
        // 测试现有退款申请接口对礼品券的处理增强
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("order_id", "GIFT_ORDER_001");
        requestBody.put("biz_uniq_key", "REFUND_KEY_001");
        requestBody.put("refund_amount", 5000); // 50.00元
        
        List<Map<String, Object>> refundInfoList = new ArrayList<>();
        Map<String, Object> refundInfo = new HashMap<>();
        refundInfo.put("order_item_id", "ITEM_001");
        refundInfo.put("item_refund_amount", 5000);
        refundInfoList.add(refundInfo);
        requestBody.put("refund_info_list", refundInfoList);
        
        ResponseEntity<Map> response = restTemplate.postForEntity(
            "/spi/douyin/pro/refund-apply", requestBody, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Map<String, Object> responseBody = response.getBody();
        assertNotNull(responseBody);
        
        Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
        assertEquals(0, data.get("error_code"));
        assertEquals(1, data.get("result")); // 允许退款
    }
    
    @Test
    public void testGiftCouponRefundNotice() {
        // 测试现有退款通知接口对礼品券的处理增强
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("order_id", "GIFT_ORDER_001");
        requestBody.put("after_sale_id", "AFTER_SALE_001");
        
        List<Map<String, Object>> noticeList = new ArrayList<>();
        Map<String, Object> notice = new HashMap<>();
        notice.put("code", "GIFT_COUPON_001");
        notice.put("audit_result", 1); // 退款通过
        notice.put("refund_reason", "客服退款");
        noticeList.add(notice);
        requestBody.put("notice_list", noticeList);
        
        ResponseEntity<Map> response = restTemplate.postForEntity(
            "/spi/douyin/pro/refund-notice", requestBody, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        Map<String, Object> responseBody = response.getBody();
        assertNotNull(responseBody);
        
        Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
        assertEquals(0, data.get("error_code"));
    }
}
```

#### 现有券核销服务增强测试
```java
@RunWith(SpringRunner.class)
@SpringBootTest
public class DouyinCouponVerifyServiceIntegrationTest {
    
    @Autowired
    private DouyinCouponVerifyService couponVerifyService;
    
    @Test
    public void testGiftCouponAutoRouting() {
        // 测试现有券核销服务对礼品券的自动路由
        String giftCouponCode = "GIFT_COUPON_001";
        String storeCode = "STORE001";
        String staffId = "STAFF001";
        Map<String, Object> params = new HashMap<>();
        
        DouyinCouponVerifyResult result = couponVerifyService.verifyCoupon(
            giftCouponCode, storeCode, staffId, params);
        
        assertTrue(result.isSuccess());
        assertTrue(result.getVerificationType().startsWith("GIFT_"));
        assertNotNull(result.getAdditionalInfo());
        assertTrue(result.getAdditionalInfo().containsKey("gift_type"));
        assertTrue(result.getAdditionalInfo().containsKey("verification_record_id"));
        assertTrue(result.getAdditionalInfo().containsKey("gift_record_id"));
    }
}
```

### 3. 性能测试

#### 并发核销测试
```java
@Test
public void testConcurrentGiftVerification() {
    int threadCount = 100;
    int couponCount = 10;
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch latch = new CountDownLatch(threadCount * couponCount);
    
    List<String> couponCodes = generateTestCoupons(couponCount);
    AtomicInteger successCount = new AtomicInteger(0);
    AtomicInteger failCount = new AtomicInteger(0);
    
    long startTime = System.currentTimeMillis();
    
    for (String couponCode : couponCodes) {
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    DouyinGiftVerifyResult result = giftVerificationService.verifyGiftCoupon(
                        couponCode, "STORE001", "STAFF001", new HashMap<>());
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                    } else {
                        failCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
    }
    
    latch.await();
    long endTime = System.currentTimeMillis();
    
    // 验证结果：每个券码只能成功核销一次
    assertEquals(couponCount, successCount.get());
    assertEquals(threadCount * couponCount - couponCount, failCount.get());
    
    // 验证性能：平均处理时间应小于100ms
    long avgTime = (endTime - startTime) / (threadCount * couponCount);
    assertTrue("平均处理时间应小于100ms，实际: " + avgTime + "ms", avgTime < 100);
}
```

### 4. 回归测试

#### 现有功能兼容性测试
```java
@Test
public void testExistingFunctionCompatibility() {
    // 测试现有代金券功能不受影响
    String cashCouponCode = "CASH_COUPON_001";
    DouyinCouponVerifyResult result = couponVerifyService.verifyCoupon(
        cashCouponCode, "STORE001", "STAFF001", new HashMap<>());
    
    assertTrue(result.isSuccess());
    assertEquals("CASH", result.getVerificationType());
    
    // 测试现有退款功能不受影响
    Map<String, Object> refundResult = douyinCouponService.processRefund(
        "CASH_ORDER_001", "REFUND_001");
    assertTrue((Boolean) refundResult.get("success"));
}

@Test
public void testEnhancedSpiEndpointsBackwardCompatibility() {
    // 测试增强后的SPI接口向后兼容性
    // 普通退款申请应该正常处理（非礼品券）
    Map<String, Object> normalRefundRequest = new HashMap<>();
    normalRefundRequest.put("order_id", "NORMAL_ORDER_001");
    normalRefundRequest.put("biz_uniq_key", "REFUND_KEY_001");
    normalRefundRequest.put("refund_amount", 5000);
    
    DouyinProSpiService proSpiService = SpringContext.getBean(DouyinProSpiService.class);
    Map<String, Object> result = proSpiService.handleRefundApply(normalRefundRequest);
    
    assertNotNull(result);
    assertEquals(0, ((Map<String, Object>)result.get("data")).get("error_code"));
}
```

---

## 部署与监控

### 1. 部署配置

#### application.yml配置扩展
```yaml
douyin:
  gift:
    verification:
      enabled: true
      concurrent-limit: 1000
      timeout: 30000
      retry-count: 3
    refund:
      enabled: true
      async-processing: true
      notification-timeout: 10000
    
redis:
  gift-lock:
    prefix: "douyin:gift:lock:"
    expire-seconds: 30
    
logging:
  level:
    com.besttop.marketing.service.thirdparty.douyin.gift: INFO
```


### 2. 监控指标

#### 业务指标监控
```yaml
douyin_gift_metrics:
  verification:
    - name: "douyin_gift_verification_total"
      type: "counter"
      labels: ["gift_type", "store_code", "result"]
      description: "抖音礼品券核销总数"
    
    - name: "douyin_gift_verification_duration"
      type: "histogram" 
      labels: ["gift_type", "store_code"]
      description: "抖音礼品券核销耗时分布"
      buckets: [0.1, 0.5, 1.0, 2.0, 5.0]
  
  refund:
    - name: "douyin_gift_refund_total"
      type: "counter"
      labels: ["refund_type", "result"]
      description: "抖音礼品券退款总数"
    
    - name: "douyin_gift_refund_processing_duration"
      type: "histogram"
      labels: ["refund_type"]
      description: "抖音礼品券退款处理耗时"
      buckets: [1.0, 5.0, 10.0, 30.0, 60.0]

  spi_enhancement:
    - name: "douyin_pro_spi_gift_detection_total"
      type: "counter"
      labels: ["endpoint", "is_gift_order", "result"]
      description: "抖音Pro SPI礼品券检测统计"
    
    - name: "douyin_pro_spi_processing_duration"
      type: "histogram"
      labels: ["endpoint", "order_type"]
      description: "增强后的Pro SPI接口处理耗时"
      buckets: [0.1, 0.5, 1.0, 2.0, 5.0]

alert_rules:
  - alert: "DouyinGiftVerificationFailureRate"
    expr: "rate(douyin_gift_verification_total{result=\"failed\"}[5m]) / rate(douyin_gift_verification_total[5m]) > 0.05"
    for: "2m"
    labels:
      severity: "warning"
    annotations:
      summary: "抖音礼品券核销失败率过高"
      
  - alert: "DouyinGiftRefundDelayed" 
    expr: "histogram_quantile(0.95, rate(douyin_gift_refund_processing_duration_bucket[5m])) > 30"
    for: "5m"
    labels:
      severity: "critical"
    annotations:
      summary: "抖音礼品券退款处理耗时过长"

  - alert: "DouyinProSpiGiftProcessingError"
    expr: "rate(douyin_pro_spi_gift_detection_total{result=\"error\"}[5m]) > 0.1"
    for: "2m"
    labels:
      severity: "warning"
    annotations:
      summary: "抖音Pro SPI礼品券处理出现错误"
```

### 3. 日志配置

#### 结构化日志
```java
@Slf4j
@Component
public class DouyinGiftLogger {
    
    public void logVerificationStart(String couponCode, String storeCode, String staffId) {
        log.info("抖音礼品券核销开始: coupon_code={}, store_code={}, staff_id={}, timestamp={}", 
                couponCode, storeCode, staffId, System.currentTimeMillis());
    }
    
    public void logVerificationSuccess(String couponCode, String giftType, 
                                      String verificationRecordId, String giftRecordId, long duration) {
        log.info("抖音礼品券核销成功: coupon_code={}, gift_type={}, verification_record_id={}, " +
                "gift_record_id={}, duration_ms={}, timestamp={}", 
                couponCode, giftType, verificationRecordId, giftRecordId, duration, System.currentTimeMillis());
    }
    
    public void logVerificationFailure(String couponCode, String errorCode, String errorMessage) {
        log.error("抖音礼品券核销失败: coupon_code={}, error_code={}, error_message={}, timestamp={}", 
                couponCode, errorCode, errorMessage, System.currentTimeMillis());
    }
    
    public void logRefundProcessing(String couponCode, String refundType, String refundId) {
        log.info("抖音礼品券退款处理: coupon_code={}, refund_type={}, refund_id={}, timestamp={}", 
                couponCode, refundType, refundId, System.currentTimeMillis());
    }
    
    public void logSpiGiftDetection(String endpoint, String orderId, boolean isGiftOrder, String result) {
        log.info("抖音Pro SPI礼品券检测: endpoint={}, order_id={}, is_gift_order={}, result={}, timestamp={}", 
                endpoint, orderId, isGiftOrder, result, System.currentTimeMillis());
    }
}
```

---

## 总结

本实现方案基于对现有抖音对接模块的深度分析，设计了完整的抖音礼品券核销模块，具备以下特点：

### 核心优势
1. **完全兼容现有架构**: 复用现有的券核销、礼品发放、客户账户等模块
2. **SPI接口增强方案**: 通过增强现有DouyinProSpiController的退款接口，无需创建新接口，保持API向后兼容
3. **事务一致性保证**: 使用分布式锁和事务管理确保数据一致性
4. **完整的退款处理**: 支持未核销和已核销两种退款场景，在现有SPI接口内进行礼品券检测和路由
5. **可扩展设计**: 支持A/B/C三种礼品类型，可轻松扩展新类型
6. **全面的监控告警**: 提供完整的业务指标监控和告警机制，包括SPI接口增强的监控

### 核心架构特点
1. **现有SPI接口增强**: 
   - 在DouyinProSpiController的`/refund-apply`和`/refund-notice`接口中增加礼品券检测逻辑
   - 根据order_type=22或groupon_type=2自动识别礼品券订单
   - 将礼品券退款请求路由到专门的处理逻辑
   - 保持现有API签名和响应格式不变
   - 确保全项目退款申请和信息同步的唯一入口

2. **无缝集成**:
   - 现有券核销服务自动检测礼品券类型并路由到礼品券处理逻辑
   - 礼品发放系统通过ManualGiftRecordService自然集成
   - 状态同步管理确保券状态和礼品发放状态的一致性

### 实施步骤
1. **阶段一**: 数据库表结构创建和现有表扩展
2. **阶段二**: 核心服务类实现（DouyinGiftVerificationService、DouyinGiftRefundService）
3. **阶段三**: 现有服务增强（DouyinCouponVerifyService、DouyinProSpiService、ManualGiftRecordService）
4. **阶段四**: 单元测试和集成测试，重点测试SPI接口的向后兼容性
5. **阶段五**: 性能优化和监控配置，包括SPI接口增强的监控指标
6. **阶段六**: 生产部署和灰度验证

### 风险控制
1. **数据一致性**: 通过事务管理和分布式锁保证
2. **性能风险**: 通过缓存和异步处理优化，SPI接口增强的性能监控
3. **兼容性风险**: 通过全面的集成测试验证，特别是现有SPI接口的向后兼容性测试
4. **业务风险**: 通过完整的日志记录和监控告警控制，包括礼品券检测和路由的监控

### 技术优势
1. **零接口变更**: 抖音平台无需修改任何调用逻辑，保持现有API的完全兼容
2. **统一入口架构**: 退款申请和退款信息同步通过DouyinProSpiController的唯一入口处理，确保架构清晰统一
3. **智能路由**: 在现有SPI接口内部智能检测礼品券订单并路由到专门处理逻辑
4. **渐进增强**: 可以逐步启用礼品券功能，不影响现有业务流程
5. **监控完备**: 新增礼品券特有的监控指标，同时保持对现有SPI接口性能的监控

该方案充分利用现有系统的成熟能力，通过接口内部增强而非新增接口的方式，最小化开发工作量和集成风险，同时确保系统的稳定性和可维护性。特别是通过现有SPI接口的增强，避免了抖音平台侧的任何改动，实现了真正的无缝集成。最重要的是，确保了全项目退款申请和退款信息同步功能的统一入口，保持了架构的一致性和可维护性。
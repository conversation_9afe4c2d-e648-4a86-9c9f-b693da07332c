[2025-08-03 09:40:53:053] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-08-03 09:40:54:054] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:643] : Successfully waited for workers to finish.
[2025-08-03 09:40:54:054] [e-msr-marketing] [] [INFO ] [SpringContextShutdownHook] [SimpleMessageListenerContainer.java:640] : Waiting for workers to finish.
[2025-08-03 11:51:02:002] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:02:002] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 11:51:04:004] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 11:51:04:004] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 11:51:04:004] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 79ms. Found 0 repository interfaces.
[2025-08-03 11:51:05:005] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 11:51:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$ac265015] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @82503ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 23678 ms
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 11:51:26:026] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@3f3c5ecd{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.4239820508786755229.8080/],UNAVAILABLE}
[2025-08-03 11:51:27:027] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 11:53:43:043] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:53:43:043] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 80ms. Found 0 repository interfaces.
[2025-08-03 11:53:45:045] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$cba77c64] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @82552ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 11:54:06:006] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 23526 ms
[2025-08-03 11:54:07:007] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 11:54:07:007] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@1760e594{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.229552926135450342.8080/],UNAVAILABLE}
[2025-08-03 11:54:07:007] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 11:57:13:013] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:14:014] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 80ms. Found 0 repository interfaces.
[2025-08-03 11:57:16:016] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$af8238f9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @83067ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 11:57:37:037] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 23607 ms
[2025-08-03 11:57:38:038] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 11:57:38:038] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@6115846e{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.4211203486625105817.8080/],UNAVAILABLE}
[2025-08-03 11:57:38:038] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[2025-08-03 12:03:12:012] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:03:13:013] [] [] [INFO ] [main] [SpringApplication.java:655] : The following profiles are active: local
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:246] : Multiple Spring Data modules found, entering strict repository configuration mode!
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:126] : Bootstrapping Spring Data repositories in DEFAULT mode.
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [RepositoryConfigurationDelegate.java:184] : Finished Spring Data repository scanning in 79ms. Found 0 repository interfaces.
[2025-08-03 12:03:15:015] [] [] [INFO ] [main] [GenericScope.java:295] : BeanFactory id=9dab5921-8558-39bb-8b06-8d79cb75312f
[2025-08-03 12:04:24:024] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$28da8a98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [PostProcessorRegistrationDelegate.java:330] : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [Log.java:169] : Logging initialized @243689ms to org.eclipse.jetty.util.log.Slf4jLog
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [JettyServletWebServerFactory.java:145] : Server initialized with port: 8080
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [Server.java:359] : jetty-9.4.22.v20191022; built: 2019-10-22T13:37:13.455Z; git: b1e6b55512e008f7fbdf1cbea4ff8a6446d1073b; jvm 1.8.0_452-b09
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [ContextHandler.java:2221] : Initializing Spring embedded WebApplicationContext
[2025-08-03 12:04:25:025] [] [] [INFO ] [main] [ServletWebServerApplicationContext.java:284] : Root WebApplicationContext: initialization completed in 72379 ms
[2025-08-03 12:04:26:026] [] [] [INFO ] [main] [DruidDataSourceAutoConfigure.java:56] : Init DruidDataSource
[2025-08-03 12:04:26:026] [] [] [INFO ] [main] [ContextHandler.java:1015] : Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@5922cff3{application,/,[file:///private/var/folders/3d/56blcpjs4m3fdvn8g8xwcq080000gn/T/jetty-docbase.6483916947883909620.8080/],UNAVAILABLE}
[2025-08-03 12:04:26:026] [] [] [INFO ] [main] [ConditionEvaluationReportLoggingListener.java:136] : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.

-- 为t_douyin_gift_verification_record表增加礼品发放状态和客户信息字段

-- 1. 增加礼品发放状态字段
ALTER TABLE `t_douyin_gift_verification_record` 
ADD COLUMN `gift_issue_status` varchar(20) DEFAULT 'PENDING' COMMENT '礼品发放状态：PENDING-待发放，ISSUED-已发放，CANCELLED-已作废' AFTER `sync_status`;

-- 2. 增加客户信息字段
ALTER TABLE `t_douyin_gift_verification_record` 
ADD COLUMN `customer_code` varchar(50) DEFAULT NULL COMMENT '顾客编码' AFTER `gift_issue_status`,
ADD COLUMN `phone` varchar(20) DEFAULT NULL COMMENT '顾客手机号' AFTER `customer_code`;

-- 3. 创建索引以提高查询性能
ALTER TABLE `t_douyin_gift_verification_record` 
ADD INDEX `idx_gift_issue_status` (`gift_issue_status`),
ADD INDEX `idx_customer_code` (`customer_code`),
ADD INDEX `idx_phone` (`phone`);

-- 4. 更新注释
COMMENT ON TABLE `t_douyin_gift_verification_record` IS '抖音礼品券核销记录表';

-- 5. 更新历史数据（将已有核销记录的发放状态设置为已发放）
UPDATE `t_douyin_gift_verification_record` 
SET `gift_issue_status` = 'ISSUED' 
WHERE `gift_record_id` IS NOT NULL AND `verification_status` = 'SUCCESS'; 
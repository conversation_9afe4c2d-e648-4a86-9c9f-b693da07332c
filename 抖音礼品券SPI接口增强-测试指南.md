# 抖音礼品券SPI接口增强 - 测试指南

## 📋 功能概述

SPI接口增强功能已完成，实现了在现有的抖音Pro版退款接口中无缝集成礼品券处理逻辑，保持API向后兼容性。

## 🎯 增强内容

### 1. 退款申请接口增强 (`/refund-apply`)

**增强位置**: `DouyinProSpiServiceImpl.handleRefundApply()`

**核心功能**:
- 自动检测礼品券订单类型
- 路由到专门的礼品券退款处理逻辑
- 支持未核销和已核销券的不同处理策略

**判断逻辑**:
```java
// 1. 基于订单类型判断：order_type = 22 表示礼品券
if (order.getOrderType() != null && order.getOrderType() == 22) {
    return true;
}

// 2. 基于商品规则判断：groupon_type = 2 表示礼品券
if (rule != null && "erp:douyin_coupon_groupon_type:2".equals(rule.getGrouponType())) {
    return true;
}
```

### 2. 退款信息同步接口增强 (`/refund-notice`)

**增强位置**: `DouyinProSpiServiceImpl.handleRefundNotice()`

**核心功能**:
- 处理已核销礼品券的退款信息同步
- 更新礼品发放记录状态
- 记录退款审核结果

## 🧪 测试方案

### 测试环境准备

1. **数据库准备**
```sql
-- 执行礼品券表创建脚本
source douyin-gift-tables.sql;

-- 验证表创建成功
SHOW TABLES LIKE '%gift%';
```

2. **应用启动**
```bash
# 启动应用并检查日志
tail -f logs/application.log | grep -i gift
```

### 测试用例

#### 测试用例1: 未核销礼品券退款申请

**请求示例**:
```bash
curl -X POST http://localhost:8080/douyin-pro/refund-apply \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "GIFT_ORDER_001",
    "after_sale_id": "AS_001",
    "certificates": [
      {
        "code": "GIFT_COUPON_001",
        "status": 0
      }
    ],
    "timestamp": 1640995200,
    "sign": "test_signature"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "data": {
    "refund_id": "REF_AS_001_001",
    "audit_result": 1,
    "audit_reason": "礼品券退款处理完成，未核销券:1张，已核销券:0张",
    "processed_count": 1,
    "unverified_count": 1,
    "verified_count": 0
  }
}
```

#### 测试用例2: 已核销礼品券退款申请

**请求示例**:
```bash
curl -X POST http://localhost:8080/douyin-pro/refund-apply \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "GIFT_ORDER_002",
    "after_sale_id": "AS_002",
    "certificates": [
      {
        "code": "GIFT_COUPON_002",
        "status": 1
      }
    ],
    "timestamp": 1640995200,
    "sign": "test_signature"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "data": {
    "refund_id": "REF_AS_002_001",
    "audit_result": 1,
    "audit_reason": "礼品券退款处理完成，未核销券:0张，已核销券:1张",
    "processed_count": 1,
    "unverified_count": 0,
    "verified_count": 1
  }
}
```

#### 测试用例3: 礼品券退款信息同步

**请求示例**:
```bash
curl -X POST http://localhost:8080/douyin-pro/refund-notice \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "GIFT_ORDER_002",
    "after_sale_id": "AS_002",
    "notice_list": [
      {
        "code": "GIFT_COUPON_002",
        "audit_result": 1,
        "refund_reason": "用户申请退款"
      }
    ],
    "timestamp": 1640995200,
    "sign": "test_signature"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "data": {
    "success_count": 1,
    "fail_count": 0,
    "total_count": 1
  }
}
```

#### 测试用例4: 普通代金券退款（兼容性测试）

**请求示例**:
```bash
curl -X POST http://localhost:8080/douyin-pro/refund-apply \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "NORMAL_ORDER_001",
    "after_sale_id": "AS_003",
    "certificates": [
      {
        "code": "NORMAL_COUPON_001",
        "status": 0
      }
    ],
    "timestamp": 1640995200,
    "sign": "test_signature"
  }'
```

**预期行为**: 应该走原有的代金券退款流程，不进入礼品券处理逻辑。

## 🔍 验证要点

### 1. 日志验证

**关键日志标识**:
```
检测到礼品券退款申请，orderId=GIFT_ORDER_001
开始处理礼品券退款申请: orderId=GIFT_ORDER_001, afterSaleId=AS_001
未核销礼品券退款处理成功: couponCode=GIFT_COUPON_001
礼品券退款申请处理完成: orderId=GIFT_ORDER_001, refundId=REF_AS_001_001
```

### 2. 数据库验证

**检查礼品核销记录表**:
```sql
SELECT * FROM t_douyin_gift_verification_record 
WHERE coupon_code = 'GIFT_COUPON_001';
```

**检查礼品退款记录表**:
```sql
SELECT * FROM t_douyin_gift_refund_record 
WHERE coupon_code = 'GIFT_COUPON_001';
```

**检查订单状态更新**:
```sql
SELECT order_id, refund_status, refund_complete_time 
FROM t_douyin_pre_order 
WHERE order_id = 'GIFT_ORDER_001';
```

### 3. 业务流程验证

1. **礼品券类型识别准确性**
   - 验证order_type=22的订单被正确识别为礼品券
   - 验证groupon_type=2的商品被正确识别为礼品券

2. **退款处理逻辑正确性**
   - 未核销券：调用processUnverifiedRefund方法
   - 已核销券：记录退款申请，等待信息同步

3. **信息同步处理正确性**
   - 退款通过：调用syncVerifiedRefundInfo方法
   - 退款拒绝：同样调用syncVerifiedRefundInfo方法记录结果

4. **向后兼容性**
   - 普通代金券退款功能不受影响
   - 现有API接口保持稳定

## ⚠️ 注意事项

### 1. 测试数据准备

**创建测试礼品券规则**:
```sql
INSERT INTO t_douyin_coupon_rule (
    id, douyin_product_id, groupon_type, store_code, 
    coupon_value, pay_amount, status
) VALUES (
    'GIFT_RULE_001', 'GIFT_PRODUCT_001', 'erp:douyin_coupon_groupon_type:2',
    'STORE001', 100.00, 50.00, 'ACTIVE'
);
```

**创建测试订单**:
```sql
INSERT INTO t_douyin_pre_order (
    id, order_id, sku_id, order_type, order_status, 
    pay_amount, phone, create_time
) VALUES (
    'PRE_ORDER_001', 'GIFT_ORDER_001', 'GIFT_PRODUCT_001', 22,
    'COUPON_ISSUED', 50.00, '13800138000', NOW()
);
```

### 2. 错误处理测试

**测试异常场景**:
- 券码不存在
- 服务调用失败
- 数据库连接异常
- 签名验证失败

### 3. 性能测试

**并发测试**:
```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 -p gift_refund_request.json \
   -T application/json \
   http://localhost:8080/douyin-pro/refund-apply
```

## 📊 测试报告模板

### 测试结果记录

| 测试用例 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 未核销礼品券退款 | 退款成功 | ✅ | 通过 | - |
| 已核销礼品券退款 | 记录申请 | ✅ | 通过 | - |
| 退款信息同步 | 同步成功 | ✅ | 通过 | - |
| 普通券兼容性 | 正常处理 | ✅ | 通过 | - |

### 性能指标

- **平均响应时间**: < 500ms
- **并发处理能力**: 100 TPS
- **错误率**: < 0.1%

## 🎯 上线检查清单

- [ ] 数据库表结构已创建
- [ ] 应用正常启动
- [ ] 礼品券识别功能正常
- [ ] 退款处理逻辑正确
- [ ] 信息同步功能正常
- [ ] 向后兼容性验证通过
- [ ] 性能测试通过
- [ ] 错误处理测试通过
- [ ] 日志记录完整
- [ ] 监控告警配置完成

## 📞 问题反馈

如果在测试过程中发现问题，请记录：
1. 具体的测试场景
2. 请求参数和响应结果
3. 相关的错误日志
4. 数据库状态截图

**SPI接口增强功能已完成，可以开始测试！** 🚀

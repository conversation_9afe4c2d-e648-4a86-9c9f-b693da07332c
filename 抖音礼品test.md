# 抖音礼品核销模块测试用例文档

## 📋 目录
1. [测试策略与目标](#测试策略与目标)
2. [测试环境准备](#测试环境准备)
3. [功能测试用例](#功能测试用例)
4. [性能测试用例](#性能测试用例)
5. [安全测试用例](#安全测试用例)
6. [集成测试用例](#集成测试用例)
7. [回归测试用例](#回归测试用例)
8. [自动化测试建议](#自动化测试建议)

---

## 测试策略与目标

### 测试目标
- **功能完整性**：验证抖音礼品券核销、退款、状态同步等核心功能
- **性能要求**：确保核销响应时间 < 2s，支持1000+ TPS并发
- **安全可靠**：验证签名验证、幂等性、事务一致性
- **集成兼容**：确保与现有系统100%兼容，零API变更影响
- **异常处理**：验证各种异常场景的正确处理

### 测试范围
- ✅ 抖音礼品券核销服务(DouyinGiftVerificationService)
- ✅ 抖音礼品券退款服务(DouyinGiftRefundService) 
- ✅ SPI接口增强(DouyinProSpiController)
- ✅ 现有券核销服务增强(DouyinCouponVerifyService)
- ✅ 礼品发放系统集成(ManualGiftRecordService)
- ✅ API接口层(DouyinCouponController)

### 风险等级定义
- **🔴 高风险**：影响核心业务流程，导致资损或服务不可用
- **🟡 中风险**：影响用户体验，但不影响核心功能
- **🟢 低风险**：边界场景，影响范围有限

---

## 测试环境准备

### 测试数据准备
```sql
-- 测试券规则数据
INSERT INTO t_douyin_coupon_rule (id, rule_name, douyin_product_id, groupon_type, status) VALUES
('TEST_RULE_A001', 'A类礼品券规则-电子币', 'DOUYIN_GIFT_A_001', 'erp:douyin_coupon_groupon_type:2', 'ENABLED'),
('TEST_RULE_B001', 'B类礼品券规则-自营商品', 'DOUYIN_GIFT_B_001', 'erp:douyin_coupon_groupon_type:2', 'ENABLED'),
('TEST_RULE_C001', 'C类礼品券规则-联营商品', 'DOUYIN_GIFT_C_001', 'erp:douyin_coupon_groupon_type:2', 'ENABLED');

-- 测试券码数据
INSERT INTO t_douyin_coupon (coupon_code, coupon_rule_id, coupon_status, douyin_order_id, phone) VALUES
('GIFT_A_CODE_001', 'TEST_RULE_A001', 'UNUSED', 'ORDER_A_001', '13800138001'),
('GIFT_B_CODE_001', 'TEST_RULE_B001', 'UNUSED', 'ORDER_B_001', '13800138002'),
('GIFT_C_CODE_001', 'TEST_RULE_C001', 'UNUSED', 'ORDER_C_001', '13800138003');
```

### 环境配置
```yaml
# application-test.yml
douyin:
  gift:
    verification:
      enabled: true
      concurrent-limit: 1000
      timeout: 30000
    refund:
      enabled: true
      async-processing: true
      
redis:
  gift-lock:
    prefix: "test:douyin:gift:lock:"
    expire-seconds: 30
```

---

## 功能测试用例

### 1. 礼品券核销功能测试

#### 测试用例 TC_GIFT_001：A类礼品券核销成功
**用例描述**：验证A类礼品券(电子币)核销正常流程  
**风险等级**：🔴 高风险  
**前置条件**：
- 存在有效的A类礼品券规则
- 券码状态为UNUSED
- 用户账户正常

**测试步骤**：
1. 调用券核销接口：`POST /douyin/coupon/verify`
   ```json
   {
     "couponCode": "GIFT_A_CODE_001",
     "storeCode": "STORE001", 
     "staffId": "STAFF001"
   }
   ```
2. 系统检测为礼品券类型
3. 路由到DouyinGiftVerificationService处理
4. 生成礼品核销记录
5. 创建待发放礼品记录(A类电子币)

**预期结果**：
- 返回成功响应：`{"success": true, "verificationType": "GIFT_A"}`
- 券状态更新为USED
- 生成t_douyin_gift_verification_record记录
- 生成manual_gift_record记录，状态为PENDING，类型为A
- 礼品记录amount字段为券面值金额

**测试数据**：
- 券码：GIFT_A_CODE_001
- 券面值：50.00元
- 门店：STORE001

---

#### 测试用例 TC_GIFT_002：B类礼品券核销成功
**用例描述**：验证B类礼品券(自营商品)核销正常流程  
**风险等级**：🔴 高风险  
**前置条件**：
- 存在有效的B类礼品券规则
- 券码状态为UNUSED
- 商品库存充足

**测试步骤**：
1. 调用券核销接口
2. 系统路由到礼品券处理逻辑
3. 生成礼品核销记录
4. 创建待发放礼品记录(B类自营商品)

**预期结果**：
- 返回成功响应：`{"success": true, "verificationType": "GIFT_B"}`
- 生成礼品记录，类型为B，包含productCode和quantity
- deliveryType设置为LOGISTICS

---

#### 测试用例 TC_GIFT_003：C类礼品券核销成功
**用例描述**：验证C类礼品券(联营商品)核销正常流程  
**风险等级**：🔴 高风险  

**测试步骤**：同TC_GIFT_002

**预期结果**：
- 返回成功响应：`{"success": true, "verificationType": "GIFT_C"}`
- 生成礼品记录，类型为C，包含supplierCode
- deliveryType设置为SUPPLIER_DIRECT

---

#### 测试用例 TC_GIFT_004：券码不存在
**用例描述**：验证不存在的券码核销处理  
**风险等级**：🟡 中风险  

**测试步骤**：
1. 使用不存在的券码调用核销接口

**预期结果**：
- 返回失败响应：`{"success": false, "message": "券码不存在"}`
- 不生成任何记录

---

#### 测试用例 TC_GIFT_005：券码已使用
**用例描述**：验证已使用券码的重复核销处理  
**风险等级**：🟡 中风险  

**前置条件**：券码状态为USED

**预期结果**：
- 返回失败响应：`{"success": false, "message": "券已使用"}`
- 不生成新的核销记录

---

#### 测试用例 TC_GIFT_006：券码已过期
**用例描述**：验证过期券码核销处理  
**风险等级**：🟡 中风险  

**前置条件**：券码有效期已过

**预期结果**：
- 返回失败响应：`{"success": false, "message": "券已过期"}`

---

#### 测试用例 TC_GIFT_007：并发核销同一券码
**用例描述**：验证同一券码并发核销的分布式锁机制  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 启动10个并发线程
2. 同时调用同一券码的核销接口
3. 验证分布式锁效果

**预期结果**：
- 只有1个请求成功核销
- 其他9个请求返回：`{"success": false, "message": "券码正在处理中，请稍后重试"}`
- 只生成1条核销记录

---

### 2. 礼品券查询功能测试

#### 测试用例 TC_QUERY_001：查询礼品核销记录列表
**用例描述**：验证礼品核销记录查询功能  
**风险等级**：🟢 低风险  

**测试步骤**：
1. 调用查询接口：`POST /douyin/gift/verification/list`
   ```json
   {
     "couponCode": "GIFT_A_CODE_001",
     "storeCode": "STORE001",
     "giftType": "A"
   }
   ```

**预期结果**：
- 返回对应的核销记录列表
- 包含核销时间、门店、礼品类型等信息

---

#### 测试用例 TC_QUERY_002：查询礼品券详情
**用例描述**：验证礼品券详情查询功能  
**风险等级**：🟢 低风险  

**测试步骤**：
1. 调用详情查询接口：`POST /douyin/gift/detail`
   ```json
   {
     "couponCode": "GIFT_A_CODE_001"
   }
   ```

**预期结果**：
- 返回完整的礼品券信息：
  ```json
  {
    "coupon": {...},           // 基础券信息
    "verification": {...},     // 核销记录
    "giftRecord": {...},      // 礼品发放记录
    "refundRecords": [...]    // 退款记录
  }
  ```

---

### 3. 礼品券退款功能测试

#### 测试用例 TC_REFUND_001：未核销券退款申请
**用例描述**：验证未核销券的退款申请处理  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 调用SPI退款申请接口：`POST /spi/douyin/pro/refund-apply`
2. 系统检测为礼品券订单
3. 调用processUnverifiedRefund处理

**预期结果**：
- 返回允许退款：`{"result": 1, "message": "礼品券退款处理成功"}`
- 券状态更新为REFUNDED
- 生成礼品退款记录

---

#### 测试用例 TC_REFUND_002：已核销券退款信息同步
**用例描述**：验证已核销券的退款信息同步  
**风险等级**：🔴 高风险  

**前置条件**：
- 券已核销
- 存在待发放礼品记录

**测试步骤**：
1. 调用SPI退款通知接口：`POST /spi/douyin/pro/refund-notice`
2. 系统检测为礼品券订单
3. 调用syncVerifiedRefundInfo处理
4. 处理礼品发放单状态

**预期结果**：
- 同步成功响应
- 券状态更新为REFUNDED
- 礼品发放单状态更新为CANCELLED/REFUNDED
- 生成退款记录

---

#### 测试用例 TC_REFUND_003：A类礼品已发放退款
**用例描述**：验证A类礼品已发放后的退款处理  
**风险等级**：🔴 高风险  

**前置条件**：
- A类礼品已发放(STATUS_GRANTED)
- 用户电子币余额充足

**测试步骤**：
1. 触发退款信息同步
2. 调用processGrantedGiftRefund
3. 执行电子币扣减操作

**预期结果**：
- 成功扣减用户电子币余额
- 礼品记录状态更新为REFUNDED
- 生成客户账户扣减日志

---

#### 测试用例 TC_REFUND_004：B类礼品已发放退款
**用例描述**：验证B类礼品已发放后的退款处理  
**风险等级**：🔴 高风险  

**前置条件**：
- B类礼品已发放(STATUS_GRANTED)
- 商品尚未发货

**预期结果**：
- 成功回收商品库存
- 礼品记录状态更新为REFUNDED
- 生成库存回收记录

---

#### 测试用例 TC_REFUND_005：B类礼品已发货退款
**用例描述**：验证B类礼品已发货后的退款处理  
**风险等级**：🟡 中风险  

**前置条件**：
- B类礼品已发货(STATUS_SHIPPED)

**预期结果**：
- 创建逆向物流单
- 礼品记录状态保持SHIPPED
- 生成退款处理记录

---

#### 测试用例 TC_REFUND_006：C类礼品退款通知
**用例描述**：验证C类礼品退款的供应商通知  
**风险等级**：🟡 中风险  

**预期结果**：
- 发送供应商退款通知
- 创建财务调账记录
- 礼品记录状态更新为REFUNDED

---

### 4. SPI接口增强测试

#### 测试用例 TC_SPI_001：礼品券订单检测
**用例描述**：验证SPI接口的礼品券订单自动检测  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 构造礼品券订单退款申请
   ```json
   {
     "order_id": "GIFT_ORDER_001",
     "order_type": 22
   }
   ```
2. 调用/refund-apply接口
3. 验证isGiftCouponOrder检测逻辑

**预期结果**：
- 正确识别为礼品券订单
- 路由到handleGiftCouponRefund处理
- 日志记录："检测到礼品券退款申请"

---

#### 测试用例 TC_SPI_002：非礼品券订单处理
**用例描述**：验证SPI接口对非礼品券订单的正常处理  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 构造普通订单退款申请
   ```json
   {
     "order_id": "NORMAL_ORDER_001",
     "order_type": 21
   }
   ```

**预期结果**：
- 识别为非礼品券订单
- 继续现有退款处理流程
- 保持向后兼容性

---

#### 测试用例 TC_SPI_003：SPI接口签名验证
**用例描述**：验证SPI接口的签名验证机制  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 发送无效签名的请求
2. 发送正确签名的请求

**预期结果**：
- 无效签名返回：`{"err_no": 300, "err_msg": "签名验证失败"}`
- 正确签名正常处理

---

### 5. 系统集成测试

#### 测试用例 TC_INTEGRATION_001：现有券核销服务集成
**用例描述**：验证现有券核销服务的礼品券路由  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 调用现有券核销接口处理礼品券
2. 验证自动路由到礼品券处理逻辑

**预期结果**：
- 自动检测为礼品券
- 调用DouyinGiftVerificationService
- 返回包含gift_type的响应

---

#### 测试用例 TC_INTEGRATION_002：礼品发放系统集成
**用例描述**：验证与ManualGiftRecordService的集成  
**风险等级**：🔴 高风险  

**预期结果**：
- 成功创建ManualGiftRecord记录
- 正确设置礼品类型和参数
- 状态流转正常

---

### 6. 状态管理测试

#### 测试用例 TC_STATUS_001：券状态同步测试
**用例描述**：验证券状态与礼品发放状态的双向同步  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 核销礼品券
2. 礼品发放状态变更
3. 验证状态同步机制

**预期结果**：
- 券状态正确同步到礼品发放状态
- 礼品发放状态正确同步到券状态

---

#### 测试用例 TC_STATUS_002：状态机完整性测试
**用例描述**：验证状态机的完整性和一致性  
**风险等级**：🟡 中风险  

**测试状态流转**：
```
UNUSED → USED → GIFT_GRANTED → GIFT_SHIPPED → GIFT_COMPLETED
       ↓
    REFUNDED
```

---

## 性能测试用例

### 测试用例 TC_PERF_001：核销接口响应时间
**用例描述**：验证核销接口在正常负载下的响应时间  
**风险等级**：🔴 高风险  

**测试条件**：
- 并发用户：100
- 测试时长：10分钟
- 测试数据：1000张有效券码

**性能指标**：
- 平均响应时间：< 1s
- 95%响应时间：< 2s
- 99%响应时间：< 3s
- 错误率：< 0.1%

---

### 测试用例 TC_PERF_002：高并发核销测试
**用例描述**：验证系统在高并发下的性能表现  
**风险等级**：🔴 高风险  

**测试条件**：
- 并发用户：1000
- TPS目标：1000+
- 持续时间：30分钟

**性能指标**：
- 系统TPS：≥ 1000
- 分布式锁命中率：100%
- 幂等性保证：100%
- 系统稳定性：无内存泄漏

---

### 测试用例 TC_PERF_003：分布式锁性能测试
**用例描述**：验证Redis分布式锁的性能和可靠性  
**风险等级**：🟡 中风险  

**测试场景**：
- 同一券码1000并发核销
- 验证锁竞争和释放

**预期结果**：
- 只有1次成功核销
- 999次失败返回锁等待消息
- 锁自动释放无死锁

---

### 测试用例 TC_PERF_004：查询接口性能测试
**用例描述**：验证查询接口在大数据量下的性能  
**风险等级**：🟢 低风险  

**测试条件**：
- 数据量：100万核销记录
- 并发查询：200
- 查询条件：多维度组合查询

**性能指标**：
- 平均响应时间：< 500ms
- 数据准确性：100%

---

## 安全测试用例

### 测试用例 TC_SEC_001：SQL注入测试
**用例描述**：验证系统对SQL注入攻击的防护  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 在券码参数中注入SQL语句
   ```json
   {
     "couponCode": "'; DROP TABLE t_douyin_coupon; --"
   }
   ```

**预期结果**：
- 系统正常处理，不执行恶意SQL
- 返回"券码不存在"或参数错误

---

### 测试用例 TC_SEC_002：权限验证测试
**用例描述**：验证接口的访问权限控制  
**风险等级**：🟡 中风险  

**测试步骤**：
1. 无权限用户调用核销接口
2. 跨门店核销测试

**预期结果**：
- 返回权限不足错误
- 不允许跨门店核销

---

### 测试用例 TC_SEC_003：数据加密测试
**用例描述**：验证敏感数据的加密存储  
**风险等级**：🟡 中风险  

**验证项目**：
- 用户手机号加密存储
- 券码生成随机性
- 退款金额传输加密

---

### 测试用例 TC_SEC_004：幂等性安全测试
**用例描述**：验证幂等性机制的安全性  
**风险等级**：🔴 高风险  

**测试步骤**：
1. 重复提交相同核销请求
2. 修改请求时间戳重试
3. 并发重复请求

**预期结果**：
- 相同请求只处理一次
- 缓存机制正常工作
- 无重复扣减或发放

---

## 集成测试用例

### 测试用例 TC_INT_001：端到端业务流程测试
**用例描述**：验证从券核销到礼品发放的完整流程  
**风险等级**：🔴 高风险  

**测试流程**：
1. 用户核销礼品券
2. 生成核销记录
3. 创建礼品发放单
4. 礼品实际发放
5. 状态同步更新

**预期结果**：
- 每个环节数据一致
- 状态流转正确
- 业务闭环完整

---

### 测试用例 TC_INT_002：退款完整流程测试
**用例描述**：验证退款申请到处理完成的完整流程  
**风险等级**：🔴 高风险  

**测试流程**：
1. SPI接收退款申请
2. 礼品券检测和路由
3. 退款处理逻辑
4. 礼品回收处理
5. 状态同步抖音

**预期结果**：
- 退款流程完整
- 资金账务正确
- 库存处理准确

---

### 测试用例 TC_INT_003：异常恢复测试
**用例描述**：验证系统异常情况下的恢复能力  
**风险等级**：🔴 高风险  

**异常场景**：
- 数据库连接中断
- Redis服务异常
- 第三方服务超时
- 消息队列故障

**预期结果**：
- 优雅降级处理
- 数据一致性保证
- 自动重试机制
- 告警通知及时

---

## 回归测试用例

### 测试用例 TC_REG_001：现有功能兼容性测试
**用例描述**：验证新增功能不影响现有代金券功能  
**风险等级**：🔴 高风险  

**测试范围**：
- 现有代金券核销流程
- 现有退款处理流程
- 现有查询功能
- 现有SPI接口

**预期结果**：
- 所有现有功能正常
- 性能无明显下降
- 接口响应格式不变

---

### 测试用例 TC_REG_002：数据兼容性测试
**用例描述**：验证新增字段对现有数据的兼容性  
**风险等级**：🟡 中风险  

**测试项目**：
- 现有券数据读取
- 历史订单查询
- 统计报表生成

**预期结果**：
- 现有数据正常访问
- 新增字段默认值正确
- 数据迁移无误

---

## 自动化测试建议

### 1. 单元测试自动化

```java
// 示例：礼品券核销服务单元测试
@Test
@DisplayName("A类礼品券核销成功测试")
void testVerifyGiftCouponTypeA() {
    // Given
    String couponCode = "GIFT_A_CODE_001";
    String storeCode = "STORE001";
    String staffId = "STAFF001";
    
    // When
    DouyinGiftVerifyResult result = giftVerificationService
        .verifyGiftCoupon(couponCode, storeCode, staffId, new HashMap<>());
    
    // Then
    assertThat(result.isSuccess()).isTrue();
    assertThat(result.getGiftType()).isEqualTo("A");
    assertThat(result.getVerificationRecord()).isNotNull();
    assertThat(result.getGiftRecord().getStatus()).isEqualTo("PENDING");
}
```

### 2. 接口测试自动化

```java
// 示例：接口自动化测试
@Test
@DisplayName("礼品券核销接口测试")
void testGiftCouponVerifyAPI() {
    // Given
    Map<String, Object> request = Map.of(
        "couponCode", "GIFT_A_CODE_001",
        "storeCode", "STORE001",
        "staffId", "STAFF001"
    );
    
    // When
    ResponseEntity<Map> response = restTemplate.postForEntity(
        "/douyin/coupon/verify", request, Map.class);
    
    // Then
    assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    assertThat(response.getBody().get("success")).isEqualTo(true);
}
```

### 3. 性能测试自动化

```java
// 示例：JMeter脚本或Gatling配置
@Test
@DisplayName("并发核销性能测试")
void testConcurrentVerificationPerformance() {
    int threadCount = 1000;
    int testDuration = 60; // 秒
    
    // 配置性能测试参数
    PerformanceTest.builder()
        .threadCount(threadCount)
        .duration(testDuration)
        .endpoint("/douyin/coupon/verify")
        .expectedTPS(1000)
        .expectedResponseTime(2000)
        .execute();
}
```

### 4. 持续集成配置

```yaml
# .github/workflows/test.yml
name: 抖音礼品模块测试
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: 单元测试
        run: mvn test -Dtest=**/DouyinGift*Test
        
      - name: 集成测试
        run: mvn test -Dtest=**/DouyinGift*IntegrationTest
        
      - name: 性能测试
        run: mvn test -Dtest=**/DouyinGift*PerformanceTest
        
      - name: 生成测试报告
        run: mvn jacoco:report
```

### 5. 数据驱动测试

```csv
# gift_coupon_test_data.csv
couponCode,giftType,expectedResult,description
GIFT_A_CODE_001,A,SUCCESS,正常A类礼品券
GIFT_B_CODE_001,B,SUCCESS,正常B类礼品券  
GIFT_C_CODE_001,C,SUCCESS,正常C类礼品券
INVALID_CODE_001,,FAIL,无效券码
EXPIRED_CODE_001,,FAIL,过期券码
```

### 6. 监控和告警集成

```yaml
# 测试监控配置
monitoring:
  test_metrics:
    - name: "test_pass_rate"
      threshold: 0.95
      alert: "测试通过率低于95%"
      
    - name: "test_execution_time" 
      threshold: 1800  # 30分钟
      alert: "测试执行时间过长"
      
    - name: "performance_regression"
      threshold: 0.1   # 10%
      alert: "性能回归超过10%"
```

---

## 测试执行计划

### 测试阶段安排
1. **开发阶段**：单元测试、代码质量检查
2. **集成阶段**：接口测试、集成测试
3. **系统测试**：功能测试、性能测试、安全测试
4. **验收测试**：端到端测试、用户场景测试
5. **生产验证**：灰度测试、监控验证

### 测试环境要求
- **开发环境**：功能开发和单元测试
- **测试环境**：完整功能测试和集成测试  
- **预生产环境**：性能测试和安全测试
- **生产环境**：灰度验证和监控

### 缺陷管理
- **🔴 致命缺陷**：立即修复，阻塞发布
- **🟡 严重缺陷**：版本内修复
- **🟢 一般缺陷**：后续版本修复

### 通过标准
- 功能测试通过率：≥ 95%
- 性能测试达标率：100%
- 安全测试通过率：100%
- 代码覆盖率：≥ 80%
- 集成测试通过率：100%

---

**文档版本**：v1.0  
**更新时间**：2024-01-15  
**维护责任人**：抖音礼品模块开发团队
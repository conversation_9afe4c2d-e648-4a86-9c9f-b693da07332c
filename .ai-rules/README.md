# 项目AI助手指导文件

## 📋 项目基本信息

**项目名称**: E-MSR营销系统  
**业务类型**: 企业级营销平台  
**核心功能**: 第三方平台对接、券码管理、营销服务  
**技术栈**: Spring Boot 2.x + MyBatis Plus + Redis + RabbitMQ + MySQL

## 🏗️ 核心架构概览

### 分层架构
```
Controller层 → Service层 → Mapper层
     ↓           ↓         ↓
   API接口    业务逻辑    数据访问
```

### 核心业务模块
- **抖音对接模块** (`thirdparty/douyin/`)
- **手动营销模块** (`manual/`)
- **客户管理模块** (`customer/`)
- **工具类模块** (`util/`)

## 🎯 抖音业务核心

### SPI接口职责
```yaml
创单SPI: 接收抖音创建订单通知
支付SPI: 接收抖音支付成功通知  
发券SPI: 生成券码并返回给抖音
核销SPI: 处理券码验证请求
退款SPI: 处理退款申请和审核
```

### 关键实体关系
```
DouyinCouponRule (券规则)
    ↓ 一对多
DouyinCoupon (券实例)
    ↓ 关联
DouyinOrder (抖音订单)
```

## 🔧 开发规范要点

### 必须遵循的规范
1. **SPI接口必须验证签名**
2. **所有操作必须保证幂等性**  
3. **Service层控制事务边界**
4. **完整的异常处理和日志记录**
5. **敏感数据加密存储**

### 代码风格
- 遵循阿里巴巴Java开发手册
- 方法长度 < 50行
- 参数个数 ≤ 5个
- 统一的异常处理机制

## 🚨 常见问题处理

### 抖音SPI相关
- 签名验证失败 → 检查算法和参数顺序
- 幂等性问题 → 基于订单ID做重复检查
- 状态异常 → 检查券码状态流转

### 性能优化
- 数据库查询加索引
- 热点数据使用缓存
- 批量操作分批处理

## 📚 项目指导文件

详细的架构设计和开发规范请参考 `.ai-rules/` 目录下的指导文件：

- `project-core.md` - 项目核心架构指导
- `douyin-domain.md` - 抖音业务领域设计
- `development-standards.md` - 开发规范与最佳实践  
- `tech-stack.md` - 技术栈与依赖管理
- `ai-assistant-guide.md` - AI助手使用指南

---

**重要提醒**: 
- 在进行任何代码修改前，请先阅读相关的指导文件
- 抖音SPI接口修改需要特别谨慎，必须保证向后兼容
- 生产环境部署前必须通过完整的测试验证
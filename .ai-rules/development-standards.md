# 开发规范与最佳实践

## 📝 代码规范

### Java编码规范
遵循《阿里巴巴Java开发手册》，重点关注以下方面：

#### 1. 命名规范
```java
// 类名：大驼峰命名法
public class DouyinCouponService {}

// 方法名：小驼峰命名法，动词开头
public boolean verifyCoupon(String couponCode) {}

// 常量：全大写，下划线分隔
public static final String DOUYIN_SPI_SUCCESS_CODE = "0";

// 变量：小驼峰命名法，见名知意
private String douyinOrderId;
private BigDecimal couponAmount;
```

#### 2. 方法设计
```java
// 方法长度控制在50行以内
// 参数个数不超过5个，超过使用对象封装
public VerifyResult verifyCoupon(VerifyRequest request) {
    // 参数校验
    validateRequest(request);
    
    // 业务逻辑
    DouyinCoupon coupon = getCoupon(request.getCouponCode());
    checkCouponStatus(coupon);
    
    // 返回结果
    return buildVerifyResult(coupon);
}
```

#### 3. 异常处理
```java
// 业务异常使用自定义异常
public class DouyinBusinessException extends RuntimeException {
    private String errorCode;
    private String errorMessage;
}

// 异常处理统一化
try {
    return processOrder(orderRequest);
} catch (DouyinBusinessException e) {
    log.warn("抖音业务异常: code={}, message={}", e.getErrorCode(), e.getMessage());
    return buildErrorResponse(e.getErrorCode(), e.getMessage());
} catch (Exception e) {
    log.error("抖音服务异常", e);
    return buildErrorResponse("SYSTEM_ERROR", "系统异常");
}
```

### 数据库操作规范

#### 1. MyBatis规范
```xml
<!-- 使用参数化查询，防止SQL注入 -->
<select id="getCouponByCode" parameterType="string" resultType="DouyinCoupon">
    SELECT * FROM t_douyin_coupon 
    WHERE coupon_code = #{couponCode} 
    AND status != 'DELETED'
</select>

<!-- 批量操作使用foreach -->
<insert id="batchInsertCoupons" parameterType="list">
    INSERT INTO t_douyin_coupon (coupon_code, rule_id, status, created_time)
    VALUES 
    <foreach collection="list" item="coupon" separator=",">
        (#{coupon.couponCode}, #{coupon.ruleId}, #{coupon.status}, NOW())
    </foreach>
</insert>
```

#### 2. 事务管理
```java
// Service层控制事务边界
@Transactional(rollbackFor = Exception.class)
public void issueCoupons(IssueCouponRequest request) {
    // 1. 更新规则库存
    updateRuleStock(request.getRuleId(), request.getQuantity());
    
    // 2. 生成券码
    List<DouyinCoupon> coupons = generateCoupons(request);
    
    // 3. 批量保存
    batchSaveCoupons(coupons);
    
    // 4. 记录日志
    logCouponIssue(request, coupons);
}
```

## 🚀 性能优化规范

### 1. 数据库优化
```java
// 分页查询必须有排序字段
@Override
public PageResult<DouyinCoupon> queryCoupons(CouponQueryParam param) {
    Page<DouyinCoupon> page = new Page<>(param.getPageNum(), param.getPageSize());
    
    LambdaQueryWrapper<DouyinCoupon> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(StringUtils.isNotBlank(param.getStatus()), DouyinCoupon::getStatus, param.getStatus())
           .orderByDesc(DouyinCoupon::getCreatedTime); // 必须有排序
           
    return PageResult.of(douyinCouponMapper.selectPage(page, wrapper));
}

// 批量操作优化
public void batchUpdateCouponStatus(List<String> couponCodes, String status) {
    // 分批处理，避免in条件过长
    Lists.partition(couponCodes, 1000).forEach(batch -> {
        douyinCouponMapper.batchUpdateStatus(batch, status);
    });
}
```

### 2. 缓存使用规范
```java
// 缓存key规范：业务域:功能:唯一标识
private static final String CACHE_KEY_COUPON_RULE = "douyin:rule:%s";
private static final String CACHE_KEY_USER_MAPPING = "douyin:user:%s";

// 缓存更新策略
@CacheEvict(value = "douyin:rule", key = "#ruleId")
public void updateCouponRule(String ruleId, DouyinCouponRule rule) {
    douyinCouponRuleMapper.updateById(rule);
}

// 缓存空值防止穿透
@Cacheable(value = "douyin:rule", key = "#productId", unless = "#result == null")
public DouyinCouponRule getCouponRuleByProductId(String productId) {
    DouyinCouponRule rule = douyinCouponRuleMapper.selectByProductId(productId);
    return rule != null ? rule : new DouyinCouponRule(); // 返回空对象防止穿透
}
```

### 3. 并发控制
```java
// 分布式锁控制并发
@RedisLock(key = "douyin:coupon:verify:#{#couponCode}", waitTime = 3, leaseTime = 10)
public VerifyResult verifyCoupon(String couponCode, String storeCode) {
    // 券码核销逻辑
    DouyinCoupon coupon = getCoupon(couponCode);
    checkAndUpdateCouponStatus(coupon);
    return buildVerifyResult(coupon);
}
```

## 📊 日志规范

### 1. 日志级别使用
```java
// ERROR: 系统异常、业务异常导致的错误
log.error("抖音券核销失败: couponCode={}, error={}", couponCode, e.getMessage(), e);

// WARN: 业务告警、性能问题
log.warn("抖音券规则库存不足: ruleId={}, remainStock={}", ruleId, remainStock);

// INFO: 关键业务流程节点
log.info("抖音券核销成功: couponCode={}, orderId={}, amount={}", couponCode, orderId, amount);

// DEBUG: 调试信息
log.debug("抖音用户映射查询: douyinUserId={}, result={}", douyinUserId, userMapping);
```

### 2. 日志格式规范
```java
// 统一日志格式：操作描述 + 关键参数 + 结果
log.info("抖音SPI回调处理开始: type={}, orderId={}", spiType, orderId);
log.info("抖音SPI回调处理完成: type={}, orderId={}, result={}", spiType, orderId, result);

// 异常日志包含上下文信息
try {
    processOrder(orderRequest);
} catch (Exception e) {
    log.error("抖音订单处理失败: orderId={}, request={}", 
              orderRequest.getOrderId(), JSON.toJSONString(orderRequest), e);
}
```

## 🔧 接口设计规范

### 1. REST API设计
```java
@RestController
@RequestMapping("/douyin/coupons")
@Validated
public class DouyinCouponController {
    
    // 查询券列表 - GET方法
    @GetMapping
    public ResultEntity<PageResult<DouyinCoupon>> listCoupons(
            @Valid CouponQueryParam param) {
        return success(douyinCouponService.queryCoupons(param));
    }
    
    // 创建券规则 - POST方法
    @PostMapping("/rules")
    public ResultEntity<String> createCouponRule(
            @RequestBody @Valid CreateCouponRuleRequest request) {
        return success(douyinCouponService.createRule(request));
    }
    
    // 更新券状态 - PUT方法
    @PutMapping("/{couponCode}/status")
    public ResultEntity<Void> updateCouponStatus(
            @PathVariable String couponCode,
            @RequestBody @Valid UpdateStatusRequest request) {
        douyinCouponService.updateStatus(couponCode, request.getStatus());
        return success();
    }
}
```

### 2. SPI接口设计
```java
@RestController
@RequestMapping("/spi/douyin")
@Slf4j
public class DouyinSpiController {
    
    @PostMapping("/issue-coupon")
    public SpiResponse<IssueCouponResult> issueCoupon(
            @RequestBody IssueCouponRequest request,
            HttpServletRequest httpRequest) {
        
        // 1. 签名验证
        if (!signatureService.verify(request, httpRequest)) {
            return SpiResponse.error("SIGNATURE_ERROR", "签名验证失败");
        }
        
        // 2. 幂等性检查
        if (isOrderProcessed(request.getOrderId())) {
            return SpiResponse.success(getCachedResult(request.getOrderId()));
        }
        
        // 3. 业务处理
        IssueCouponResult result = douyinSpiService.issueCoupon(request);
        
        // 4. 缓存结果
        cacheResult(request.getOrderId(), result);
        
        return SpiResponse.success(result);
    }
}
```

## 🧪 测试规范

### 1. 单元测试
```java
@ExtendWith(MockitoExtension.class)
class DouyinCouponServiceTest {
    
    @Mock
    private DouyinCouponMapper douyinCouponMapper;
    
    @InjectMocks
    private DouyinCouponServiceImpl douyinCouponService;
    
    @Test
    @DisplayName("券码核销成功测试")
    void testVerifyCouponSuccess() {
        // Given
        String couponCode = "TEST001";
        String storeCode = "STORE001";
        DouyinCoupon coupon = buildTestCoupon(couponCode, CouponStatus.ISSUED);
        
        when(douyinCouponMapper.selectByCouponCode(couponCode))
            .thenReturn(coupon);
        
        // When
        VerifyResult result = douyinCouponService.verifyCoupon(couponCode, storeCode);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getCouponCode()).isEqualTo(couponCode);
        verify(douyinCouponMapper).updateById(any(DouyinCoupon.class));
    }
}
```

### 2. 集成测试
```java
@SpringBootTest
@Transactional
@TestPropertySource(locations = "classpath:application-test.yml")
class DouyinSpiIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    @DisplayName("发券SPI接口集成测试")
    void testIssueCouponSpi() {
        // Given
        IssueCouponRequest request = buildIssueCouponRequest();
        HttpEntity<IssueCouponRequest> entity = new HttpEntity<>(request, buildHeaders());
        
        // When
        ResponseEntity<SpiResponse> response = restTemplate.postForEntity(
            "/spi/douyin/issue-coupon", entity, SpiResponse.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo("0");
    }
}
```

## 🔒 安全规范

### 1. 输入验证
```java
// 使用JSR-303注解进行参数验证
public class CouponQueryParam {
    
    @NotBlank(message = "门店编码不能为空")
    @Length(max = 20, message = "门店编码长度不能超过20")
    private String storeCode;
    
    @Valid
    @NotNull(message = "时间范围不能为空")
    private DateRange dateRange;
}

// Controller层统一验证
@PostMapping("/verify")
public ResultEntity<VerifyResult> verifyCoupon(
        @RequestBody @Valid VerifyCouponRequest request) {
    return success(douyinCouponService.verifyCoupon(request));
}
```

### 2. 敏感数据处理
```java
// 敏感数据加密存储
@Component
public class SensitiveDataHandler {
    
    public String encryptMobile(String mobile) {
        return AESUtil.encrypt(mobile, getEncryptKey());
    }
    
    public String decryptMobile(String encryptedMobile) {
        return AESUtil.decrypt(encryptedMobile, getEncryptKey());
    }
}

// 日志脱敏
public class PhoneDesensitizer {
    public static String desensitize(String phone) {
        if (StringUtils.isBlank(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
```

## 📈 监控规范

### 1. 业务监控
```java
// 关键业务指标监控
@Component
public class DouyinMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public void recordCouponVerify(String result, String storeCode) {
        Counter.builder("douyin.coupon.verify")
              .tag("result", result)
              .tag("store", storeCode)
              .register(meterRegistry)
              .increment();
    }
    
    public void recordSpiLatency(String spiType, long latencyMs) {
        Timer.builder("douyin.spi.latency")
             .tag("type", spiType)
             .register(meterRegistry)
             .record(latencyMs, TimeUnit.MILLISECONDS);
    }
}
```

### 2. 健康检查
```java
@Component
public class DouyinHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查关键依赖
            checkDatabase();
            checkRedis();
            checkDouyinApi();
            
            return Health.up()
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build();
        } catch (Exception e) {
            return Health.down()
                        .withDetail("error", e.getMessage())
                        .build();
        }
    }
}
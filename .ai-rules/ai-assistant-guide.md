# AI代码助手使用指南

## 🤖 项目AI助手配置

### 项目上下文信息
```yaml
项目名称: E-MSR营销系统
业务域: 营销服务、第三方平台对接
核心技术: Spring Boot + MyBatis + Redis + RabbitMQ
代码风格: 阿里巴巴Java开发手册
```

### 关键业务术语
```yaml
抖音相关:
  - SPI: Service Provider Interface，抖音平台回调接口
  - 券码: 抖音平台发放的提货券
  - 核销: 用户到店使用券码的过程
  - 来客端: 抖音商家后台管理系统

业务流程:
  - 创单: 用户下单，创建订单
  - 发券: 支付成功后生成券码
  - 核销: 到店验证券码
  - 退款: 申请退还已支付金额
```

## 📋 常见开发任务指导

### 1. 新增SPI接口
当需要新增抖音SPI接口时，请遵循以下模式：

```java
@RestController
@RequestMapping("/spi/douyin")
@Slf4j
public class DouyinSpiController {
    
    @PostMapping("/new-spi-endpoint")
    public SpiResponse<ResultType> newSpiEndpoint(
            @RequestBody RequestType request,
            HttpServletRequest httpRequest) {
        
        // 1. 签名验证 (必须)
        if (!douyinSignatureService.verify(request, httpRequest)) {
            return SpiResponse.error("SIGNATURE_ERROR", "签名验证失败");
        }
        
        // 2. 幂等性检查 (必须)
        String idempotentKey = buildIdempotentKey(request);
        if (isProcessed(idempotentKey)) {
            return SpiResponse.success(getCachedResult(idempotentKey));
        }
        
        // 3. 业务处理
        try {
            ResultType result = douyinSpiService.processBusiness(request);
            cacheResult(idempotentKey, result);
            return SpiResponse.success(result);
        } catch (DouyinBusinessException e) {
            return SpiResponse.error(e.getErrorCode(), e.getMessage());
        }
    }
}
```

### 2. 数据库表设计
新增抖音相关表时，请遵循命名规范：

```sql
-- 表名格式：t_douyin_{业务名称}
CREATE TABLE t_douyin_new_business (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    douyin_order_id VARCHAR(64) NOT NULL COMMENT '抖音订单ID',
    business_data TEXT COMMENT '业务数据',
    status TINYINT DEFAULT 0 COMMENT '状态：0-初始，1-成功，2-失败',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_douyin_order_id (douyin_order_id),
    KEY idx_status_created (status, created_time)
) COMMENT='抖音新业务表';
```

### 3. 配置文件管理
新增配置项时，请按以下格式：

```yaml
douyin:
  new-feature:
    enabled: ${DOUYIN_NEW_FEATURE_ENABLED:true}
    timeout: ${DOUYIN_NEW_FEATURE_TIMEOUT:30000}
    retry-times: ${DOUYIN_NEW_FEATURE_RETRY:3}
```

## 🔍 代码审查要点

### 必检项目
```yaml
安全检查:
  - [ ] SPI接口是否验证签名
  - [ ] 敏感信息是否加密存储
  - [ ] SQL是否防注入

性能检查:
  - [ ] 数据库查询是否有索引
  - [ ] 是否有N+1查询问题
  - [ ] 缓存使用是否合理

业务检查:
  - [ ] 幂等性是否保证
  - [ ] 异常处理是否完整
  - [ ] 日志记录是否充分
```

### 代码质量标准
```java
// 好的示例
@Service
@Slf4j
public class DouyinCouponServiceImpl implements DouyinCouponService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VerifyResult verifyCoupon(String couponCode, String storeCode) {
        log.info("券码核销开始: couponCode={}, storeCode={}", couponCode, storeCode);
        
        // 参数校验
        validateParams(couponCode, storeCode);
        
        // 查询券信息
        DouyinCoupon coupon = getCouponByCode(couponCode);
        if (coupon == null) {
            throw new DouyinBusinessException("COUPON_NOT_FOUND", "券码不存在");
        }
        
        // 状态检查
        checkCouponStatus(coupon);
        
        // 更新状态
        updateCouponStatus(coupon, CouponStatus.USED);
        
        log.info("券码核销成功: couponCode={}, orderId={}", couponCode, coupon.getOrderId());
        return buildVerifyResult(coupon);
    }
}
```

## 🚨 常见问题与解决方案

### 1. 抖音SPI签名验证失败
```java
// 问题：签名验证一直失败
// 解决：检查签名算法和参数顺序

// 正确的签名验证方式
public boolean verifySignature(String body, String timestamp, String signature) {
    String signStr = appSecret + timestamp + body + appSecret;
    String calculatedSign = DigestUtils.sha1Hex(signStr);
    return signature.equals(calculatedSign);
}
```

### 2. 幂等性问题
```java
// 问题：重复请求导致重复处理
// 解决：基于业务唯一键做幂等性控制

@RedisLock(key = "douyin:order:#{#orderId}", waitTime = 3)
public void processOrder(String orderId) {
    // 先检查是否已处理
    if (isOrderProcessed(orderId)) {
        return getCachedResult(orderId);
    }
    
    // 处理业务逻辑
    processBusinessLogic(orderId);
    
    // 缓存处理结果
    cacheProcessResult(orderId);
}
```

### 3. 事务边界问题
```java
// 问题：事务范围过大或过小
// 解决：在Service层控制事务边界

@Service
public class DouyinOrderServiceImpl {
    
    @Transactional(rollbackFor = Exception.class)
    public void createOrder(CreateOrderRequest request) {
        // 事务内：数据库操作
        saveOrder(request);
        updateStock(request.getProductId(), request.getQuantity());
        recordLog(request);
        
        // 事务外：外部调用
        TransactionSynchronizationManager.registerSynchronization(
            new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    notifyThirdParty(request);
                }
            });
    }
}
```

## 🛠️ 开发工具使用

### IDEA配置建议
```yaml
必装插件:
  - Lombok Plugin
  - MyBatis Log Plugin
  - Alibaba Java Coding Guidelines
  - SonarLint

代码模板:
  - Service接口和实现类模板
  - Controller REST API模板
  - MyBatis Mapper模板
```

### Git提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(douyin): 新增券码核销SPI接口
fix(douyin): 修复签名验证失败问题
refactor(douyin): 重构券码查询逻辑
docs(douyin): 更新API文档
```

## 📚 学习资源

### 技术文档
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [MyBatis Plus文档](https://baomidou.com/)
- [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)

### 抖音开放平台
- [抖音开放平台文档](https://developer.open-douyin.com/)
- [SPI接口文档](https://developer.open-douyin.com/docs/resource/zh-CN/local-life/develop/OpenAPI/)
- [签名机制说明](https://developer.open-douyin.com/docs/resource/zh-CN/local-life/develop/OpenAPI/preparation/signruleintroduce)

## 🎯 AI助手提示词模板

### 代码生成提示
```
请基于E-MSR营销系统的架构规范，生成一个抖音[具体功能]的Service实现类。

要求：
1. 遵循项目的分层架构模式
2. 包含完整的参数验证和异常处理
3. 添加适当的日志记录
4. 使用事务注解控制事务边界
5. 遵循阿里巴巴Java开发手册规范

参考现有的DouyinCouponServiceImpl实现风格。
```

### 代码审查提示
```
请审查以下代码，重点关注：
1. 是否遵循项目的开发规范
2. 安全性问题（特别是抖音SPI相关）
3. 性能问题（数据库查询、缓存使用等）
4. 异常处理和日志记录
5. 事务边界是否合理

[贴入代码]
```

### 问题诊断提示
```
在E-MSR营销系统的抖音模块中遇到问题：
[描述具体问题]

请基于项目的技术栈（Spring Boot + MyBatis + Redis + RabbitMQ）和抖音SPI对接经验，提供解决方案。
```
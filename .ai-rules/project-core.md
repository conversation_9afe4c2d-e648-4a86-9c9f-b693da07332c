# 项目核心架构指导

## 🏗️ 系统核心定位

E-MSR营销系统是一个**企业级营销平台**，专注于第三方平台对接和营销服务实现。

### 核心业务领域
- **抖音平台对接**: SPI回调处理、券码支付、订单同步、用户映射
- **礼品券管理**: 核销服务、退款处理、状态追踪、规则匹配
- **手动营销**: 电子币发放、自营商品、联营商品管理
- **客户服务**: 账户管理、积分体系、会员等级

## 🔧 技术栈组成

### 核心框架
```yaml
backend_framework: Spring Boot 2.x
data_access: MyBatis Plus
database: MySQL (主) + Redis (缓存)
message_queue: RabbitMQ
build_tool: Maven
java_version: 1.8
```

### 依赖管理
```yaml
parent_artifact: bty-common-parent
key_dependencies:
  - easyexcel: 2.2.6 (Excel处理)
  - hutool: 5.0.6 (工具库)
  - apollo: 配置中心
  - consul: 服务发现
  - openfeign: 微服务调用
```

## 📁 代码组织结构

### 分层架构模式
```
controller/     # API控制器层
├── customer/   # 客户管理API
├── manual/     # 手动操作API  
├── thirdparty/ # 第三方平台API
└── admin/      # 管理后台API

service/        # 业务服务层
├── customer/   # 客户业务服务
├── manual/     # 手动操作服务
├── thirdparty/ # 第三方平台服务
└── impl/       # 服务实现

model/          # 数据模型层
├── customer/   # 客户数据模型
├── manual/     # 手动操作模型
├── thirdparty/ # 第三方平台模型
└── basic/      # 基础模型

mapper/         # 数据访问层
dto/            # 数据传输对象
util/           # 工具类集合
```

### 抖音业务模块组织
```
thirdparty/douyin/
├── controller/    # API控制器
├── service/       # 业务服务
├── model/         # 数据模型
├── dto/           # 传输对象
├── mapper/        # 数据访问
└── util/          # 工具类
```

## 🎯 核心设计原则

### 1. 分层职责明确
- **Controller**: 接收请求、参数验证、响应格式化
- **Service**: 业务逻辑实现、事务管理、异常处理
- **Mapper**: 数据访问、SQL执行、结果映射

### 2. 抖音SPI对接原则
- **幂等性保障**: 基于订单ID做重复请求处理
- **签名验证**: 严格验证抖音回调请求签名
- **异步处理**: 使用消息队列处理复杂业务逻辑
- **状态机管理**: 券码生命周期状态严格控制

### 3. 数据一致性保障
- **事务边界**: Service层控制事务范围
- **状态同步**: 关键状态变更需同步到抖音平台
- **幂等设计**: 支付、发券、退款操作必须幂等

### 4. 错误处理策略
- **业务异常**: 明确的业务错误码和消息
- **技术异常**: 统一的异常处理机制
- **回调失败**: 重试机制和补偿处理

## 🔒 安全要求

### API安全
- 抖音SPI接口必须验证签名
- 敏感信息加密存储
- 接口访问日志记录

### 数据安全
- 用户敏感信息脱敏
- 支付相关数据加密
- 操作审计日志完整

## 📊 性能要求

### 响应时间
- 抖音回调接口: < 3秒
- 查询接口: < 500ms
- 批量操作: < 10秒

### 并发能力
- 支持抖音高并发回调
- 券码核销峰值处理
- 营销活动并发支持

## 🔍 监控要求

### 业务监控
- 抖音SPI调用成功率
- 券码核销成功率
- 支付流程监控
- 退款处理监控

### 技术监控
- 接口响应时间
- 数据库连接池
- Redis缓存命中率
- MQ消息积压

## 🚀 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 统一的日志格式和级别
- 完善的单元测试覆盖
- 清晰的注释和文档

### Git规范
- 功能分支开发模式
- 规范的提交信息格式
- Code Review必须通过
- 集成测试验证

### 部署规范
- 配置外部化管理
- 灰度发布策略
- 回滚预案准备
- 监控告警配置
# 抖音业务领域设计指导

## 🎯 抖音业务核心

### 业务定位
抖音模块是E-MSR营销系统的**核心业务域**，负责与抖音开放平台的全面对接，实现券码支付、核销、退款的完整业务闭环。

### 核心业务流程
```mermaid
graph TD
    A[用户抖音下单] --> B[创单SPI回调]
    B --> C[订单创建&校验]
    C --> D[支付成功回调]
    D --> E[发券SPI回调]
    E --> F[券码生成&发放]
    F --> G[用户到店核销]
    G --> H[核销验证&完成]
    
    I[退款申请] --> J[退款审核]
    J --> K[退款处理]
    K --> L[状态同步]
```

## 🏗️ 模块架构设计

### 核心实体关系
```
DouyinCouponRule (券规则)
├── DouyinCoupon (券实例)
├── DouyinOrder (订单)
└── DouyinCouponLog (操作日志)

DouyinAuth (授权信息)
├── DouyinUserMapping (用户映射)
└── DouyinSpiAnalysisLog (SPI分析日志)
```

### 服务层设计
```
DouyinCouponService          # 券管理核心服务
DouyinCouponVerifyService    # 券验证服务
DouyinGiftVerificationService # 礼品验证服务  
DouyinGiftRefundService      # 礼品退款服务
DouyinSpiService            # SPI回调处理
DouyinProSpiService         # Pro版SPI服务
DouyinAuthService           # 授权管理服务
```

## 🔄 SPI接口设计原则

### 1. 幂等性设计
```java
// 基于抖音订单ID保证幂等性
@PostMapping("/spi/douyin/issue-coupon")
public ResponseEntity<SpiResponse> issueCoupon(@RequestBody IssueCouponRequest request) {
    // 1. 验证签名
    // 2. 幂等性检查 (基于order_id)
    // 3. 业务逻辑处理
    // 4. 返回标准响应
}
```

### 2. 状态机管理
```java
public enum CouponStatus {
    INIT(0, "初始状态"),
    ISSUED(1, "已发放"), 
    USED(2, "已使用"),
    EXPIRED(3, "已过期"),
    REFUNDED(4, "已退款");
}
```

### 3. 异常处理策略
```java
try {
    // 业务逻辑处理
    return success(result);
} catch (BusinessException e) {
    // 业务异常 - 返回明确错误码
    return error(e.getCode(), e.getMessage());
} catch (Exception e) {
    // 系统异常 - 记录日志，返回通用错误
    log.error("SPI处理异常", e);
    return error("SYSTEM_ERROR", "系统异常，请重试");
}
```

## 📊 数据设计要点

### 核心表结构
```sql
-- 券规则表
t_douyin_coupon_rule
├── douyin_product_id (抖音商品ID)
├── rule_name (规则名称)
├── total_amount (总发行量)
├── issued_amount (已发行量)
└── status (规则状态)

-- 券实例表  
t_douyin_coupon
├── coupon_code (券码)
├── rule_id (关联规则ID)
├── douyin_order_id (抖音订单ID)
├── status (券状态)
└── verification_info (核销信息)
```

### 索引设计
```sql
-- 性能关键索引
CREATE INDEX idx_coupon_code ON t_douyin_coupon(coupon_code);
CREATE INDEX idx_douyin_order_id ON t_douyin_coupon(douyin_order_id);
CREATE INDEX idx_rule_product ON t_douyin_coupon_rule(douyin_product_id);
CREATE INDEX idx_status_created ON t_douyin_coupon(status, created_time);
```

## 🔧 技术实现规范

### 1. 配置管理
```yaml
douyin:
  app-id: ${DOUYIN_APP_ID}
  app-secret: ${DOUYIN_APP_SECRET}
  client-secret: ${DOUYIN_CLIENT_SECRET}
  api:
    base-url: https://open.douyin.com
    timeout: 30000
  spi:
    signature-timeout: 300000  # 签名有效期5分钟
```

### 2. 缓存策略
```java
// 券规则缓存 - 30分钟
@Cacheable(value = "douyin:rule", key = "#productId", unless = "#result == null")
public DouyinCouponRule getCouponRule(String productId);

// 用户映射缓存 - 24小时  
@Cacheable(value = "douyin:user", key = "#douyinUserId", unless = "#result == null")
public DouyinUserMapping getUserMapping(String douyinUserId);
```

### 3. 日志规范
```java
// 关键操作必须记录日志
log.info("抖音券核销开始: couponCode={}, storeCode={}", couponCode, storeCode);
log.info("抖音券核销成功: couponCode={}, orderId={}, amount={}", couponCode, orderId, amount);
log.error("抖音券核销失败: couponCode={}, error={}", couponCode, e.getMessage(), e);
```

### 4. 监控埋点
```java
// 业务监控指标
@Monitor(value = "douyin.coupon.verify", 
         tags = {"storeCode", "ruleId"})
public VerifyResult verifyCoupon(String couponCode, String storeCode);
```

## 🚨 异常处理机制

### 业务异常分类
```java
public enum DouyinErrorCode {
    COUPON_NOT_FOUND("COUPON_001", "券码不存在"),
    COUPON_ALREADY_USED("COUPON_002", "券码已使用"), 
    COUPON_EXPIRED("COUPON_003", "券码已过期"),
    RULE_NOT_ACTIVE("RULE_001", "券规则未激活"),
    SIGNATURE_INVALID("SPI_001", "签名验证失败"),
    ORDER_DUPLICATE("ORDER_001", "订单重复处理");
}
```

### 重试机制
```java
@Retryable(value = {Exception.class}, 
           maxAttempts = 3, 
           backoff = @Backoff(delay = 1000))
public void syncOrderStatus(String orderId, String status);
```

## 🔒 安全要求

### 1. 签名验证
- 所有抖音SPI请求必须验证签名
- 签名算法严格按照抖音官方文档实现
- 签名超时时间控制在5分钟内

### 2. 数据加密
- 用户敏感信息(手机号等)必须加密存储
- 券码生成使用安全随机算法
- 支付金额等关键数据加密传输

### 3. 访问控制
- SPI接口增加IP白名单限制
- 关键操作需要审计日志
- 敏感接口增加频率限制

## 📈 性能优化

### 1. 数据库优化
- 大表分区设计 (按时间分区)
- 读写分离配置
- 连接池调优

### 2. 缓存优化  
- 热点数据缓存预热
- 缓存雪崩防护
- 缓存穿透保护

### 3. 接口优化
- 批量操作优化
- 异步处理支持
- 响应数据精简

## 🔍 测试策略

### 单元测试
- Service层业务逻辑测试覆盖率 > 80%
- 关键算法逻辑测试覆盖率 > 95%
- Mock外部依赖进行隔离测试

### 集成测试
- SPI接口端到端测试
- 数据库事务测试
- 缓存一致性测试

### 压力测试
- 券码核销并发测试
- SPI回调峰值测试
- 数据库连接池压测
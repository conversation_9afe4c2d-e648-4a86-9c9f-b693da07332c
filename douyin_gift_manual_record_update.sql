-- 抖音券礼品发放功能 - ManualGiftRecord表结构升级脚本
-- 执行时间：2025-07-30
-- 说明：为支持抖音券礼品发放和回收功能，扩展t_manual_gift_record表

-- 1. 添加抖音券相关字段
ALTER TABLE `bt_erp_manufacture`.`manual_gift_record`
ADD COLUMN `reason` varchar(138) NULL COMMENT '原因' AFTER `is_stock`,
ADD COLUMN `status` varchar(64) NULL COMMENT '礼品发放状态' AFTER `reason`;
ADD COLUMN `douyin_coupon_code` VARCHAR(64) DEFAULT NULL COMMENT '抖音券码' AFTER `gift_type`,
ADD COLUMN `douyin_verification_record_id` VARCHAR(32) DEFAULT NULL COMMENT '关联的抖音核销记录ID' AFTER `douyin_coupon_code`,
ADD COLUMN `douyin_gift_type` VARCHAR(10) DEFAULT NULL COMMENT '抖音礼品实际类型(A/B/C)' AFTER `douyin_verification_record_id`,
ADD COLUMN `parent_gift_record_id` VARCHAR(32) DEFAULT NULL COMMENT '父礼品记录ID（用于抖音券子礼品）' AFTER `douyin_gift_type`;

-- 2. 创建相关索引以提高查询性能
CREATE INDEX `idx_douyin_coupon_code` ON `t_manual_gift_record`(`douyin_coupon_code`);
CREATE INDEX `idx_douyin_verification_record_id` ON `t_manual_gift_record`(`douyin_verification_record_id`);
CREATE INDEX `idx_parent_gift_record_id` ON `t_manual_gift_record`(`parent_gift_record_id`);

-- 3. 创建复合索引，优化常用查询场景
CREATE INDEX `idx_gift_type_store_code` ON `t_manual_gift_record`(`gift_type`, `store_code`);
CREATE INDEX `idx_douyin_coupon_status` ON `t_manual_gift_record`(`douyin_coupon_code`, `type`, `is_flag`);

-- 4. 验证表结构变更
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 't_manual_gift_record' 
  AND COLUMN_NAME IN ('douyin_coupon_code', 'douyin_verification_record_id', 'douyin_gift_type', 'parent_gift_record_id')
ORDER BY ORDINAL_POSITION;

-- 5. 验证索引创建
SHOW INDEX FROM `t_manual_gift_record` WHERE Key_name LIKE 'idx_douyin%' OR Key_name LIKE 'idx_parent%';

-- 执行完成后的验证步骤：
-- 1. 确认所有字段都已成功添加
-- 2. 确认所有索引都已成功创建
-- 3. 检查表结构是否符合预期
-- 4. 进行简单的插入和查询测试

-- 回滚脚本（如果需要）：
/*
-- 删除新增的索引
DROP INDEX `idx_douyin_coupon_code` ON `t_manual_gift_record`;
DROP INDEX `idx_douyin_verification_record_id` ON `t_manual_gift_record`;
DROP INDEX `idx_parent_gift_record_id` ON `t_manual_gift_record`;
DROP INDEX `idx_gift_type_store_code` ON `t_manual_gift_record`;
DROP INDEX `idx_douyin_coupon_status` ON `t_manual_gift_record`;

-- 删除新增的字段
ALTER TABLE `t_manual_gift_record` 
DROP COLUMN `douyin_coupon_code`,
DROP COLUMN `douyin_verification_record_id`,
DROP COLUMN `douyin_gift_type`,
DROP COLUMN `parent_gift_record_id`;
*/

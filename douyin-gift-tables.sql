-- =====================================================
-- 抖音礼品券核销模块数据库表结构
-- 创建时间: 2024-12-19
-- 说明: 支持抖音礼品券核销、退款等功能的数据库表
-- =====================================================

-- ----------------------------
-- 1. 抖音礼品核销记录表
-- ----------------------------
DROP TABLE IF EXISTS `t_douyin_gift_verification_record`;
CREATE TABLE `t_douyin_gift_verification_record` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `coupon_code` VARCHAR(64) NOT NULL COMMENT '券码',
    `coupon_id` VARCHAR(32) NOT NULL COMMENT '券ID',
    `douyin_order_id` VARCHAR(64) NOT NULL COMMENT '抖音订单ID',
    `verification_time` DATETIME NOT NULL COMMENT '核销时间',
    `store_code` VARCHAR(32) NOT NULL COMMENT '门店编码',
    `staff_id` VARCHAR(32) NOT NULL COMMENT '操作员ID',
    `gift_type` CHAR(1) NOT NULL COMMENT '礼品类型(A/B/C)',
    `gift_record_id` VARCHAR(32) DEFAULT NULL COMMENT '关联礼品发放单ID',
    `verification_status` VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '核销状态',
    `sync_status` VARCHAR(20) DEFAULT 'PENDING' COMMENT '同步状态',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_coupon_code` (`coupon_code`),
    KEY `idx_coupon_id` (`coupon_id`),
    KEY `idx_douyin_order_id` (`douyin_order_id`),
    KEY `idx_verification_time` (`verification_time`),
    KEY `idx_store_code` (`store_code`),
    KEY `idx_gift_type` (`gift_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音礼品核销记录表';

-- ----------------------------
-- 2. 抖音礼品退款记录表
-- ----------------------------
DROP TABLE IF EXISTS `t_douyin_gift_refund_record`;
CREATE TABLE `t_douyin_gift_refund_record` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `refund_id` VARCHAR(64) NOT NULL COMMENT '退款ID',
    `coupon_code` VARCHAR(64) NOT NULL COMMENT '券码',
    `coupon_id` VARCHAR(32) NOT NULL COMMENT '券ID',
    `refund_type` VARCHAR(20) NOT NULL COMMENT '退款类型(UNVERIFIED/VERIFIED)',
    `refund_scenario` VARCHAR(30) NOT NULL COMMENT '退款场景',
    `gift_record_id` VARCHAR(32) DEFAULT NULL COMMENT '关联礼品发放单ID',
    `refund_status` VARCHAR(20) NOT NULL COMMENT '退款状态',
    `refund_amount` DECIMAL(10,2) DEFAULT NULL COMMENT '退款金额',
    `refund_reason` TEXT COMMENT '退款原因',
    `refund_request_time` DATETIME NOT NULL COMMENT '退款申请时间',
    `refund_complete_time` DATETIME DEFAULT NULL COMMENT '退款完成时间',
    `sync_from_source` VARCHAR(20) DEFAULT NULL COMMENT '同步来源(DOUYIN/LAIKE)',
    `request_payload_json` TEXT COMMENT '请求数据JSON',
    `response_payload_json` TEXT COMMENT '响应数据JSON',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_refund_id` (`refund_id`),
    KEY `idx_coupon_code` (`coupon_code`),
    KEY `idx_coupon_id` (`coupon_id`),
    KEY `idx_refund_type` (`refund_type`),
    KEY `idx_refund_request_time` (`refund_request_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音礼品退款记录表';

-- ----------------------------
-- 3. 扩展现有表字段
-- ----------------------------

-- 扩展 t_douyin_coupon 表，添加礼品相关字段
ALTER TABLE `t_douyin_coupon` 
ADD COLUMN `gift_type` CHAR(1) DEFAULT NULL COMMENT '礼品类型(A/B/C)' AFTER `coupon_status`,
ADD COLUMN `gift_verification_record_id` VARCHAR(32) DEFAULT NULL COMMENT '礼品核销记录ID' AFTER `gift_type`,
ADD COLUMN `gift_record_id` VARCHAR(32) DEFAULT NULL COMMENT '礼品发放单ID' AFTER `gift_verification_record_id`;

-- 为新字段添加索引
ALTER TABLE `t_douyin_coupon`
ADD KEY `idx_gift_type` (`gift_type`),
ADD KEY `idx_gift_verification_record_id` (`gift_verification_record_id`),
ADD KEY `idx_gift_record_id` (`gift_record_id`);

-- 扩展 manual_gift_record 表，添加抖音券关联字段
ALTER TABLE `manual_gift_record` 
ADD COLUMN `douyin_coupon_code` VARCHAR(64) DEFAULT NULL COMMENT '关联抖音券码' AFTER `create_by`,
ADD COLUMN `douyin_verification_id` VARCHAR(32) DEFAULT NULL COMMENT '抖音核销记录ID' AFTER `douyin_coupon_code`;

-- 为新字段添加索引
ALTER TABLE `manual_gift_record`
ADD KEY `idx_douyin_coupon_code` (`douyin_coupon_code`),
ADD KEY `idx_douyin_verification_id` (`douyin_verification_id`);

-- ----------------------------
-- 4. 初始化数据和配置
-- ----------------------------

-- 插入礼品类型配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `create_time`) VALUES
('douyin.gift.type.A.name', '电子币', '抖音A类礼品-电子币', NOW()),
('douyin.gift.type.B.name', '自营商品', '抖音B类礼品-自营商品', NOW()),
('douyin.gift.type.C.name', '联营商品', '抖音C类礼品-联营商品', NOW()),
('douyin.gift.verification.timeout', '30', '抖音礼品券核销超时时间(秒)', NOW()),
('douyin.gift.refund.auto.process', 'true', '抖音礼品券退款自动处理开关', NOW())
ON DUPLICATE KEY UPDATE 
`config_value` = VALUES(`config_value`),
`update_time` = NOW();

-- ----------------------------
-- 5. 权限和角色配置
-- ----------------------------

-- 为抖音礼品券功能添加权限配置
INSERT INTO `system_permission` (`permission_code`, `permission_name`, `permission_desc`, `module_name`, `create_time`) VALUES
('douyin:gift:verify', '抖音礼品券核销', '允许核销抖音礼品券', '抖音营销', NOW()),
('douyin:gift:refund', '抖音礼品券退款', '允许处理抖音礼品券退款', '抖音营销', NOW()),
('douyin:gift:query', '抖音礼品券查询', '允许查询抖音礼品券信息', '抖音营销', NOW())
ON DUPLICATE KEY UPDATE 
`permission_name` = VALUES(`permission_name`),
`update_time` = NOW();

-- 创建完成提示
SELECT '抖音礼品券核销模块数据库表创建完成！' AS message;
SELECT '请检查表结构是否正确，然后继续后续开发工作。' AS next_step;
